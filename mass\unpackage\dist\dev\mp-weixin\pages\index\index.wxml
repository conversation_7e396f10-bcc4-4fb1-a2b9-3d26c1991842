<view class="content"><zl-index class="vue-ref" data-custom-hidden="{{!(currentTabBarIndex==0&&authLogin)}}" vue-id="8dd740cc-1" data-ref="home" bind:__l="__l"></zl-index><zl-my class="vue-ref" data-custom-hidden="{{!(currentTabBarIndex==1&&authLogin)}}" vue-id="8dd740cc-2" noticeNum="{{msgUnredNum}}" data-ref="my" bind:__l="__l"></zl-my><zl-reports class="vue-ref" data-custom-hidden="{{!(currentTabBarIndex==2&&authLogin)}}" vue-id="8dd740cc-3" data-ref="reports" bind:__l="__l"></zl-reports><zltabbar class="vue-ref" vue-id="8dd740cc-4" selectedIndex="{{currentTabBarIndex}}" noticeNum="{{msgUnredNum}}" data-ref="zltabbar" data-event-opts="{{[['^didSelectTabBar',[['handleChangeNavBar']]]]}}" bind:didSelectTabBar="__e" bind:__l="__l"></zltabbar><block wx:if="{{moduleCode}}"><view class="empty-loading"><label class="iconfont icon-jiazai _span"></label></view></block></view>