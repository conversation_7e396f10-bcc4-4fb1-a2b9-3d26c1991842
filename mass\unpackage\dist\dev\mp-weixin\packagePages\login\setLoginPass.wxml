<view class="login-wrapper data-v-54e14fb1"><zlnavbar vue-id="47492775-1" isBack="{{true}}" class="data-v-54e14fb1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" class="data-v-54e14fb1">密码设置</view></zlnavbar><view class="login-form data-v-54e14fb1"><image class="pwd-edit-icon data-v-54e14fb1" src="../../static/images/login/pwd-edit.png"></image><view class="login-info data-v-54e14fb1">请为您的账号<label class="_span data-v-54e14fb1">{{userInfo.phone}}</label><view class="data-v-54e14fb1">设置登录密码</view></view><view class="form-item data-v-54e14fb1"><image class="form-icon data-v-54e14fb1" src="../../static/images/login/lock-icon.png"></image><input type="password" placeholder="密码8-20字符,须包含数字、字母及特殊字符" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','password']]]]]}}" value="{{loginForm.password}}" bindinput="__e" class="data-v-54e14fb1"/></view><view class="form-item data-v-54e14fb1"><image class="form-icon data-v-54e14fb1" src="../../static/images/login/lock-icon.png"></image><input type="password" placeholder="确认密码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','confirmPassword']]]]]}}" value="{{loginForm.confirmPassword}}" bindinput="__e" class="data-v-54e14fb1"/></view><view class="form-item data-v-54e14fb1"><image class="form-icon data-v-54e14fb1" src="../../static/images/login/key-icon.png"></image><input type="text" placeholder="请输入图形验证码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','imgCode']]]]]}}" value="{{loginForm.imgCode}}" bindinput="__e" class="data-v-54e14fb1"/><image class="verifiy-code data-v-54e14fb1" src="{{verifyCodeImg}}" data-event-opts="{{[['tap',[['getVerifyImgCode',['$event']]]]]}}" bindtap="__e"></image></view><view class="bottom-btn-box data-v-54e14fb1"><view data-event-opts="{{[['tap',[['updatePwdSubmit',['$event']]]]]}}" class="main-btn data-v-54e14fb1" bindtap="__e">保存密码</view></view></view></view>