import{_ as s}from"./mk.701cea42.js";import{u as n,_}from"./index.dc283410.js";const u=Vue.defineComponent({__name:"mk2",setup(r){const o=n(),t=()=>{o.push("/jsc1")};return(c,e)=>(Vue.openBlock(),Vue.createElementBlock("div",{class:"login-container",onClick:t},e[0]||(e[0]=[Vue.createElementVNode("img",{src:s,alt:"\u767B\u5F55\u9875",class:"login-image"},null,-1)])))}});const m=_(u,[["__scopeId","data-v-db7bb6f9"]]);export{m as default};
//# sourceMappingURL=mk2.5d4e506e.js.map
