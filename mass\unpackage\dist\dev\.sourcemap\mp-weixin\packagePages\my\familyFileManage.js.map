{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/familyFileManage.vue?95b0", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/familyFileManage.vue?f2a3", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/familyFileManage.vue?7700", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/familyFileManage.vue?7bf1", "uni-app:///packagePages/my/familyFileManage.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/familyFileManage.vue?2ef4", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/familyFileManage.vue?46b4", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/familyFileManage.vue?2625", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/familyFileManage.vue?d16b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "modalDialog", "emptyPage", "data", "modalVisible", "modalData", "title", "info", "btnTxt", "idCard", "familyList", "familyIndex", "switchValue", "switchVisible", "mounted", "onShow", "methods", "getList", "uni", "api", "item", "getFamilyStatus", "getAuthCode", "wxbarcode", "setAuthState", "setTimeout", "showAuthToast", "goFacialRecognitionVerify", "url", "handleContactBinding", "handleSwitchChange", "handleCodeShowChange", "handleCancel", "handleConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACa;AACyB;;;AAGrG;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,mBAAO,CAAC,4CAAmC;AAC9D;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAA+wB,CAAgB,+xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0DnyB;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAQA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACAC;MACAC;QACAD;QACA;QACA;UACAE;UACAA;UACA;QACA;MACA;QACAF;MACA;IAEA;IACA;IACAG;MAAA;MACAF;QACAV;MACA;QACA;MACA;IACA;IACAa;MAAA;MACAJ;QACAZ;MACA;MACAa;QACA;QACAV;MACA;QACA;QACA;QACA;QACA;UACA;UACAc;QACA;QACAL;QACA;MACA;IACA;IACA;IACAM;MAAA;MACAJ;MACA;MACA;QACAD;UACAV;QACA;UACA;QACA;MACA;QACAU;UACAV;QACA;UACA;QACA;MACA;MACAgB;QACA;QACA;MACA;MACAA;QACAL;QACA;MACA;IACA;IACAM;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAT;QACAU;MACA;IACA;IACA;IACAC;MACA;MACA;IAEA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACAX;MACAA;MACA;IACA;IACAY;MACA;IACA;IACAC;MAAA;MACA;MACAd;QACAV;MACA;QACA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnNA;AAAA;AAAA;AAAA;AAAmmC,CAAgB,slCAAG,EAAC,C;;;;;;;;;;;ACAvnC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAs9C,CAAgB,06CAAG,EAAC,C;;;;;;;;;;;ACA1+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/my/familyFileManage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/my/familyFileManage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./familyFileManage.vue?vue&type=template&id=12093e25&scoped=true&\"\nvar renderjs\nimport script from \"./familyFileManage.vue?vue&type=script&lang=js&\"\nexport * from \"./familyFileManage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./familyFileManage.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./familyFileManage.vue?vue&type=style&index=1&id=12093e25&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"12093e25\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/my/familyFileManage.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./familyFileManage.vue?vue&type=template&id=12093e25&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.familyList.length\n  var l0 =\n    g0 != 0\n      ? _vm.__map(_vm.familyList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = require(\"@/static/images/my/menu-icon1.svg\")\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      item.bindStatus\n        ? _vm.handleContactBinding(item)\n        : _vm.goFacialRecognitionVerify(item)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./familyFileManage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./familyFileManage.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<zlnavbar bgColor=\"bg-white\" :isBack=\"true\">\r\n\t\t\t<block slot=\"content\">家庭档案授权管理</block>\r\n\t\t</zlnavbar>\r\n\t\t<view class=\"main-box\">\r\n\t\t\t<view class=\"list-box\" v-if=\"familyList.length!=0\">\r\n\t\t\t\t<view class=\"list-item-box\" v-for=\"(item,index) in familyList\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t<view class=\"list-lable\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"flex text-right\">\r\n\t\t\t\t\t\t\t与户主关系：{{item.hzRelation}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"list-item\">\r\n\t\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon1.svg`)\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"flex \">档案授权状态</view>\r\n\t\t\t\t\t\t\t<view class=\"flex switch-box\">\r\n\t\t\t\t\t\t\t\t<switch color='#1C6CED' disabled-color=\"red\" :disabled='item.disabled' v-model='item.empowerStatus'\r\n\t\t\t\t\t\t\t\t\t:checked=\"item.empowerStatus?true:false\" @change=\"handleSwitchChange($event,item)\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"switchVisible\">\r\n\t\t\t\t\t\t\t\t</switch>\r\n\t\t\t\t\t\t\t\t<span class=\"mask-btn\" v-if='item.disabled' @click=\"showAuthToast(item)\"></span>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t<button @click=\"item.bindStatus?handleContactBinding(item):goFacialRecognitionVerify(item)\">\r\n\t\t\t\t\t\t\t{{item.bindStatus?'解除查阅绑定':'档案查阅绑定'}}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<button :disabled=\"!item.empowerStatus||!item.bindStatus?true:false\"\r\n\t\t\t\t\t\t\t@click=\"handleCodeShowChange(index,item)\">获取授权码</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"list-info-box\" v-if=\"item.authCode\">\r\n\t\t\t\t\t\t<view>请出示或告知医生此授权码</view>\r\n\t\t\t\t\t\t<view class=\"auth-code\">{{item.authCode}}</view>\r\n\t\t\t\t\t\t<canvas class=\"bar-code\" :canvas-id=\"'barcode-' + index\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<emptyPage  v-else></emptyPage>\r\n\t\t</view>\r\n\t\t<view class=\"bottom-tips\">\r\n\t\t\t<view class=\"\">注意事项：</view>\r\n\t\t\t<view class=\"\">1.家庭关系通过公共卫生服务系统获取，如有错误，请到您的档案管理机构修改。</view>\r\n\t\t\t<view class=\"\">2.健康档案调阅授权仅限单次授权，再次调阅再次授权。</view>\r\n\t\t\t<view class=\"\">3.授权码的有效期为5分钟，超时则失效，若仍要使用授权码则需要重新获取。</view>\r\n\t\t\t<view class=\"\">4.若档案授权状态关闭，则健康档案无法被医疗机构调用，即无法获取授权码。</view>\r\n\t\t</view>\r\n\t\t<modalDialog :closeVisible='true' :modalData=\"modalData\" @confirm=\"handleConfirm\" @cancel='handleCancel'\r\n\t\t\tv-show=\"modalVisible\">\r\n\t\t</modalDialog>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\timport modalDialog from \"@/components/dialog/dialog.vue\";\r\n\timport wxbarcode from 'wxbarcode'\r\n\timport {\r\n\t\tparseTime,\r\n\t\tformatCountDownTime,\r\n\t\ttoast,\r\n\t\tthrottle,\r\n\t\tdebounce\r\n\t} from \"@/utils/util\";\r\n\timport emptyPage from '@/packagePages/components/empty.vue'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tmodalDialog,\r\n\t\t\temptyPage\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmodalVisible: false,\r\n\t\t\t\tmodalData: {\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tinfo: '解除查阅绑定后，您将无法查看该人员的健康档案，是否确定解除查阅绑定？',\r\n\t\t\t\t\tbtnTxt: '确认'\r\n\t\t\t\t},\r\n\t\t\t\tidCard: null,\r\n\t\t\t\tfamilyList: [],\r\n\t\t\t\tfamilyIndex: 0,\r\n\t\t\t\tswitchValue: null,\r\n\t\t\t\tswitchVisible: true\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// this.getList()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.getList()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetList() {\r\n\t\t\t\tuni.showLoading({})\r\n\t\t\t\tapi.documentFamilyList({}).then(res => {\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\tthis.familyList = res.data\r\n\t\t\t\t\tthis.familyList.map((item, index) => {\r\n\t\t\t\t\t\titem.authCode = null\r\n\t\t\t\t\t\titem.disabled = item.bindStatus ? false : true\r\n\t\t\t\t\t\tthis.getFamilyStatus(item.idCard, index)\r\n\t\t\t\t\t})\r\n\t\t\t\t}).catch(e=>{\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t// 查询当前成员档案状态\r\n\t\t\tgetFamilyStatus(idCard, index) {\r\n\t\t\t\tapi.documentFamilyStatus({\r\n\t\t\t\t\tidCard: idCard\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.$set(this.familyList[index], 'empowerStatus', res.data)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetAuthCode(e) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t})\r\n\t\t\t\tapi.documentGetCodeAcess({\r\n\t\t\t\t\t// id: uni.getStorageSync('resident_id'),\r\n\t\t\t\t\tidCard: this.idCard,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.authCode = res.data\r\n\t\t\t\t\tthis.familyList[this.familyIndex].authCode = res.data\r\n\t\t\t\t\t// this.$set(this.familyList[this.familyIndex],'authCode' , res.data)\r\n\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\tvar symbol = res.data.length % 2 !== 0 ? \" \" : ''\r\n\t\t\t\t\t\twxbarcode.barcode(`barcode-${this.familyIndex}`, res.data + symbol, 520, 160)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 处理授权状态\r\n\t\t\tsetAuthState(state, item) {\r\n\t\t\t\titem.disabled = true\r\n\t\t\t\tthis.switchVisible = false\r\n\t\t\t\tif (state) {\r\n\t\t\t\t\tapi.documentOpen({\r\n\t\t\t\t\t\tidCard: this.idCard\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tthis.getList()\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapi.documentCancle({\r\n\t\t\t\t\t\tidCard: this.idCard\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tthis.getList()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.switchVisible = true\r\n\t\t\t\t\t// this.getList()\r\n\t\t\t\t}, 100)\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\titem.disabled = false\r\n\t\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\t}, 50000);\r\n\t\t\t},\r\n\t\t\tshowAuthToast(item) {\r\n\t\t\t\tif (!item.bindStatus) {\r\n\t\t\t\t\ttoast('请先进行档案查阅绑定', 'none', 2000)\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttoast('档案授权状态在5分钟之内仅可修改一次', 'none', 2000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 人脸识别\r\n\t\t\tgoFacialRecognitionVerify(item) {\r\n\t\t\t\t// this.modalVisible = true\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/packagePages/login/registerSuccess?type=2&&familyItem=' + JSON.stringify(item)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 解除绑定\r\n\t\t\thandleContactBinding(item) {\r\n\t\t\t\tthis.modalVisible = true\r\n\t\t\t\tthis.idCard = item.idCard\r\n\r\n\t\t\t},\r\n\t\t\t// 开/关档案授权\r\n\t\t\thandleSwitchChange(e, item) {\r\n\t\t\t\tthis.idCard = item.idCard\r\n\t\t\t\tthis.switchValue = e.detail.value\r\n\t\t\t\tthis.setAuthState(this.switchValue, item)\r\n\t\t\t},\r\n\t\t\thandleCodeShowChange(index, item) {\r\n\t\t\t\tthis.idCard = item.idCard\r\n\t\t\t\tthis.familyIndex = index\r\n\t\t\t\titem.codeVisible = true\r\n\t\t\t\titem.codebtnDisabled = !item.codebtnDisabled\r\n\t\t\t\tthis.getAuthCode()\r\n\t\t\t},\r\n\t\t\thandleCancel() {\r\n\t\t\t\tthis.modalVisible = false\r\n\t\t\t},\r\n\t\t\thandleConfirm() {\r\n\t\t\t\tthis.modalVisible = false;\r\n\t\t\t\tapi.documentFamilyUnbind({\r\n\t\t\t\t\tidCard: this.idCard\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\ttoast(res.msg, 'none', 1000)\r\n\t\t\t\t\tthis.getList()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #F5F6F7 !important;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t// /deep/ switch .wx-switch-input-disabled {\r\n\t// \tbackground: #ccc !important;\r\n\t// }\r\n\r\n\t// /deep/ .uni-switch-input-disabled:checked {\r\n\t// \tbackground-color: #B5C7FF !important;\r\n\t// }\r\n\t.main-box {\r\n\t\tpadding: 16px;\r\n\t}\r\n\r\n\t.switch-box {\r\n\t\tposition: relative;\r\n\r\n\t\t.mask-btn {\r\n\t\t\tposition: absolute;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\twidth: 45px;\r\n\t\t\theight: 25px;\r\n\t\t\tz-index: 999;\r\n\t\t}\r\n\t\tswitch{\r\n\r\n\t\t\t&::after{\r\n\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t}\r\n\r\n\t.list-box {\r\n\t\t.list-item-box {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12px;\r\n\t\t\tpadding: 16px;\r\n\t\t\tmargin-bottom: 17px;\r\n\r\n\t\t\t.list-lable {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.90)\r\n\t\t\t}\r\n\r\n\t\t\t.list-info-box {\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t\t\t\tpadding-top: 30px;\r\n\r\n\t\t\t\t.bar-code {\r\n\t\t\t\t\theight: 90px;\r\n\t\t\t\t\tmargin-top: 15px;\r\n\t\t\t\t\tmargin-left: 30px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.auth-code {\r\n\t\t\t\t\tfont-size: 60px;\r\n\t\t\t\t\tfont-weight: 860;\r\n\t\t\t\t\tcolor: #1c6ced;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.text-right {\r\n\t\t\t\t\ttext-align: center !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin: 17px 0;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-bottom: 0.5px solid #e7e7e7;\r\n\t\t\tpadding-bottom: 18px;\r\n\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder: 0;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 24px;\r\n\t\t\t\theight: 24px;\r\n\t\t\t\tmargin-right: 12px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list-cell {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #0D0B22;\r\n\r\n\r\n\t\t\t.list-cell-label {\r\n\t\t\t\tpadding: 1px 5px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tbackground: #D54941;\r\n\t\t\t\tborder-radius: 16px;\r\n\t\t\t\tmargin-left: 10px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.text-right {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: #666;\r\n\t\t\topacity: 0.9;\r\n\r\n\t\t\tspan {\r\n\t\t\t\tcolor: #1C6CED;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tbutton {\r\n\t\tflex: 1;\r\n\t\tfont-size: 14px !important;\r\n\t\tfont-weight: 600;\r\n\t\theight: 40px;\r\n\t\tline-height: 40px;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: #F2F3FF;\r\n\t\tcolor: #1C6CED;\r\n\r\n\t\t&:last-child {\r\n\t\t\tmargin-left: 16px;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\tbutton[disabled] {\r\n\t\tbackground-color: #F2F3FF !important;\r\n\t\tcolor: #B5C7FF !important;\r\n\t}\r\n\r\n\t.arrow-icon {\r\n\t\tfont-size: 20px;\r\n\t\tcolor: #999 !important;\r\n\t\tmargin-left: 5px;\r\n\t}\r\n\r\n\t.bottom-tips {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t\tline-height: 20px;\r\n\t\tline-height: 24px;\r\n\t\tpadding: 30px;\r\n\t\tpadding-top: 0;\r\n\r\n\t\tview {\r\n\t\t\t&:first-child {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./familyFileManage.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./familyFileManage.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542307970\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./familyFileManage.vue?vue&type=style&index=1&id=12093e25&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./familyFileManage.vue?vue&type=style&index=1&id=12093e25&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542307986\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}