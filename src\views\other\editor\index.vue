<template>
  <div class="app-container">
    <div class="app-container-inner">
      <el-form ref="formRef" :model="dynamicValidateForm" label-width="80px" class="demo-dynamic">
        <el-form-item prop="title" label="标题" :rules="[{ required: true, message: '请输入标题', trigger: 'blur' }]">
          <el-input v-model="dynamicValidateForm.title" />
        </el-form-item>
        <!-- 王editor -->
        <el-form-item prop="content" label="标题" :rules="[{ required: true, message: '请输入内容', trigger: 'blur' }]">
          <wang-edior v-model="dynamicValidateForm.content" />
        </el-form-item>
        <!-- vue-quill -->
        <el-form-item prop="content" label="标题" :rules="[{ required: true, message: '请输入内容', trigger: 'blur' }]"
          v-if="false">
          <quill-editor theme="snow"></quill-editor>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
          <el-button type="primary" @click="preview(formRef)">预览</el-button>
          <el-button @click="resetForm(formRef)">重置</el-button>
        </el-form-item>
      </el-form>
      <el-descriptions title="配置项 " :column="1" border class="descriptions">
        <el-descriptions-item label="value"> 双向绑定的 value 值，使用示例：v-model='content' </el-descriptions-item>
        <el-descriptions-item label="参考文档"> <a href="https://www.wangeditor.com/v5/for-frame.html" target="_blank">
            https://www.wangeditor.com/v5/for-frame.html </a> </el-descriptions-item>

      </el-descriptions>
      <el-dialog v-model="dialogVisible" title="预览" width="60%">
        <div style="display: flex;align-items: center;margin-bottom: 20px">
          <span style="width: 50px;font-weight: bold">标题：</span>
          <div>{{ dynamicValidateForm.title }}</div>
        </div>
        <div style="display: flex;align-items: center;margin-bottom: 20px">
          <span style="width: 50px;font-weight: bold">内容</span>
          <div v-html="dynamicValidateForm.content"></div>
        </div>
        <template #footer>
        </template>
      </el-dialog>
      <div>-----------------------</div>
      <div>
        <div>
          <button @click="handleExport">html转pdf</button>
          <div id="pdfDom">
            <div>测试</div>
          </div>
        </div>
      </div>
      <div>--------------------------</div>
      <vue-signature-pad  width="95%" height="300px" ref="signaturePadRef" :options="state.signOptions" />
    </div>
  </div>
</template> 

<script lang="ts" setup>
import WangEdior from '@/components/WangEdior/index.vue'
// import SignaturePad from '@/components/Signature/index.vue'
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { QuillEditor } from '@vueup/vue-quill'
import useGlobalProperties from '@/utils/useGlobalProperties'
const globelProperties = useGlobalProperties()
const formRef = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const dynamicValidateForm = reactive<{
  title: string
  content: string,
  options: Object
}>({
  title: '',
  content: '',
  options: {},
})
const content = ref<string>('')
const quillEditorRef = ref<any>(null)
// 签名
const signaturePadRef = ref();
const state = reactive({
  signOptions: {
    penColor: '#000000',
    minWidth: 1.0,
    onBegin: () => {
      signaturePadRef.value.resizeCanvas();
    },
  },
});

// 工具栏配置项
const toolbarOptions = [
  // 加粗 斜体 下划线 删除线 -----['bold', 'italic', 'underline', 'strike']
  ['bold', 'italic', 'underline', 'strike'],
  // 引用  代码块-----['blockquote', 'code-block']
  ['blockquote', 'code-block'],
  // 1、2 级标题-----[{ header: 1 }, { header: 2 }]
  [{ header: 1 }, { header: 2 }],
  // 有序、无序列表-----[{ list: 'ordered' }, { list: 'bullet' }]
  [{ list: 'ordered' }, { list: 'bullet' }],
  // 上标/下标-----[{ script: 'sub' }, { script: 'super' }]
  [{ script: 'sub' }, { script: 'super' }],
  // 缩进-----[{ indent: '-1' }, { indent: '+1' }]
  [{ indent: '-1' }, { indent: '+1' }],
  // 文本方向-----[{'direction': 'rtl'}]
  [{ direction: 'rtl' }],
  // 字体大小-----[{ size: ['small', false, 'large', 'huge'] }]
  [{ size: ['small', false, 'large', 'huge'] }],
  // 标题-----[{ header: [1, 2, 3, 4, 5, 6, false] }]
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  // 字体颜色、字体背景颜色-----[{ color: [] }, { background: [] }]
  [{ color: [] }, { background: [] }],
  // 字体种类-----[{ font: [] }]
  [{ font: [] }],
  // 对齐方式-----[{ align: [] }]
  [{ align: [] }],
  // 清除文本格式-----['clean']
  ['clean'],
  // 链接、图片、视频-----['link', 'image', 'video']
  ['link', 'image', 'video'],
  // ['table'] // 表格
]

const options = reactive({
  modules: {
    toolbar: toolbarOptions,
    history: {
      delay: 1000,
      maxStack: 50,
      userOnly: false,
    },
    ImageDrop: true,
    imageResize: {
      displayStyles: {
        backgroundColor: 'black',
        border: 'none',
        color: 'white',
      },
      modules: ['Resize', 'DisplaySize', 'Toolbar'],
    },
    // table: false,  // disable table module
    // 'better-table': {
    //   operationMenu: {
    //     items: {
    //       insertColumnRight: { text: '右边插入一列' },
    //       insertColumnLeft: { text: '左边插入一列' },
    //       insertRowUp: { text: '上边插入一行' },
    //       insertRowDown: { text: '下边插入一行' },
    //       mergeCells: { text: '合并单元格' },
    //       unmergeCells: { text: '拆分单元格' },
    //       deleteColumn: { text: '删除列' },
    //       deleteRow: { text: '删除行' },
    //       deleteTable: { text: '删除表格' },
    //     },
    //     background: {
    //       color: '#333'
    //     },
    //     color: {
    //       colors: ['green', 'red', 'yellow', 'blue', 'white'],
    //       text: 'background:'
    //     }
    //   }
    // },
    // keyboard: {
    //   bindings: QuillBetterTable.keyboardBindings
    // }
  },
  theme: 'snow',
  placeholder: '请输入正文',
  // Some Quill optiosn...
})

const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    console.log('valid', valid)
    if (valid) {
      ElMessage.success('保存成功')
    } else {
      console.log('error submit!')
      return false
    }
  })
}

const preview = (formEl) => {
  if (!formEl) return
  formEl.validate((valid) => {
    console.log('valid', valid)
    if (valid) {
      dialogVisible.value = true
    } else {
      console.log('error submit!')
      return false
    }
  })

}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

// 导出pdf
const handleExport = (name) => {
  var fileName = 'html转pdf'
  globelProperties.getPdf(fileName)
}

// 

</script>


