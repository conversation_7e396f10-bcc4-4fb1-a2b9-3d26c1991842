{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "uni-app:///packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "scrollMenu", "u<PERSON><PERSON><PERSON>", "emptyPlaceholder", "modalDialog", "computed", "collapseItemData", "serviceDataList", "data", "abnormalDataType", "navType", "modalVisible", "modalData", "type", "title", "info", "btnTxt", "formData", "value", "key", "label", "placeholder", "chartData", "diseaseOptions", "isOpen", "collapseHeight", "bsugarMg", "dbp", "tabCur", "serviceSData", "serviceDataDetail", "collapseData", "childCurIndex", "childTabList", "childChartData", "childOptions", "gestationalWeeks", "apiData", "apiDataType", "requestData", "pregnantOtherInspections", "userInfo", "tuberculosisVisit", "encryptData", "medicareidEncrypt", "familyItem", "columnName", "moduleCode", "businessName", "infoData", "menuIndex", "yytjList", "jyxm", "yxjc", "onReady", "onLoad", "tabList", "onShow", "console", "onUnload", "methods", "handleAIReportClick", "jcjybgdh", "yljgdm", "jzlsh", "archiveId", "uni", "url", "showTips", "content", "showCancel", "goHome", "getUserInfo", "getData", "params", "sfzjhm", "cxjmjkdabh", "api", "res", "item", "obj", "ehr_grgms", "archiveid", "ehr_bls", "ehr_jwjbs", "ehr_jwsss", "ehr_jwwss", "ehr_jwsxs", "ehr_ycbs", "ehr_jzbs", "ehr_cjqk", "result", "stringify", "sitem", "tempItem", "thirdItem", "resItem", "visitdate", "subItem", "healthcheckid", "tempList", "result1", "map", "diabetesData", "hypertensionData", "card<PERSON>o<PERSON><PERSON><PERSON>", "x", "getDecryptData", "queryAll_mjxx", "Promise", "queryAll_zyxx", "queryAll_tnb_sf", "sfbh", "queryAll_gxy_sf", "queryAll_tj_info", "tjj<PERSON>", "queryAll_tj_yyinfo", "tjjgdm", "bglb", "queryAll_fjh_sf", "queryAll_jyqy_ly", "qyjlid", "queryAll_lnr_zlnl", "pgbh", "queryAll_et_sf", "queryAll_et_tj", "tjbh", "queryAll_fv_cq0", "queryAll_fv_cq00", "queryAll_fv_fm", "queryAll_fv_xse", "queryAll_fv_fs", "queryAll_fv_42", "goDetail", "itemList", "pIdx", "sIdx", "_that", "serviceData", "hypertensionvisitid", "tmpArr", "tabData", "getChartsData", "setTimeout", "categories", "series", "childWeightData", "current<PERSON><PERSON>", "childHeightGirlData", "childWeightGirlData", "name", "moonage", "childRes", "handleChangePanel", "scrollTop", "duration", "selector", "handleMenuChange", "handleChildMenuChange", "handleLongPress", "handleLongPressMul", "handleLongPressMul2", "handleCancel", "handleConfirm", "topicCode", "areaCode", "businessId", "idCard", "bId", "sourceType", "reason", "amendData", "goRule", "pageHide"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACa;AACyB;;;AAG/F;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7MA;AAAA;AAAA;AAAA;AAAywB,CAAgB,yxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC+U7xB;AAKA;AAGA;AAUA;AAIA;AAEA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA,EACA;IACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAN;UACAO;UACAC;QACA,GACA;UACAH;UACAC;UACAN;UACAO;UACAC;QACA;MAEA;MACAC;MACAC;MACAV;MACAW;MACAC;MACAxB;MACAyB;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,YAEA;MACAC;MACAC;QACAF;QACAG;QACAC;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,yGACAC;IACA;IACA;IACA;MACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;IACA;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAH;MACA;QAAAI;QAAAC;QAAAC;QAAAC;MACA;QACA1D;UACA;YACAyD;UACA;UACA;YACAD;UACA;QACA;MACA;MACAG;QACAC;MACA;IACA;IACAC;MACA;MACAF;QACApD;QACAuD;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACAL;MACA;MACAA;QACAC;MACA;IACA;IACAK;MAAA;MACA;QACA;UACA;UACA;YACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAP;MACAR;MACA;MACA;QACA;QACA;UAAA;UACAgB;YACAC;UACA;QACA;UAAA;UACAD;YACAE;UACA;QACA;MACA;QACA;QACA;UAAA;UACAF;YACAC;UACA;QACA;UAAA;UACAD;YACAE;YACAD;UACA;QACA;MACA;;MAEA;MACA;QACAD;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MACA;;MAEA;MACA;QACAG;UACAC;YACAC;YACAA;UACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACArB;QACA;QACAmB;UACAX;UACA;YACA;cAAA;;cAEA;cACAY;;cAEA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACAE;kBAAA;kBACAC,gBACA;oBACA7D;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACA6D;kBAAA;kBACAG;oBACA/D;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACA6D;kBAAA;kBACAI;oBACAhE;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACA6D;kBAAA;kBACAK;oBACAjE;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACA6D;kBAAA;kBACAM;oBACAlE;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACA6D;kBAAA;kBACAO;oBACAnE;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACA6D;kBAAA;kBACAQ;oBACApE;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACA6D;kBAAA;kBACAS;oBACArE;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;cACA;cACA;gBACA;kBAAA;gBAAA;gBACA;gBACA6D;kBAAA;kBACAU;oBACAtE;oBACAF;oBACAgE;oBACArE;kBACA,GACA;oBACAO;oBACAF;oBACAC;kBACA,GACA;oBACAC;oBACAF;oBACAC;kBACA,EACA;gBACA;cACA;cACA;gBACA4D;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACA;oBACAA;kBACA;kBACA;oBACAA;kBACA;kBACA;oBACAA;kBACA;kBACAA;kBACAA;kBAEA;oBACAA;oBACAA;kBACA;gBACA;cACA;cACA;YACA;cAAA;cACA;gBACA;gBACA;gBACAD;kBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA/D;oBACA;sBACAA;oBACA;sBACAA;oBACA;oBACA;kBACA;kBACA4E;gBACA;gBACA;cACA;gBACA;kBACAZ;kBACA;kBACA;oBACAA;oBACA;oBACAD;sBACAC;wBACA,+BACAa,UACAC;wBACAC;0BACAC,kBACAC,QACAD,UACA5E;wBACA;wBACAwE;sBACA;oBACA;oBACAZ;kBACA;gBACA;cACA;cACA;YACA;cAAA;cACA;gBAAA;gBACAD;kBACAC;kBACAA;gBACA;gBACA;gBACAvE;kBACA,oDACAyF;gBACA;gBACAnB;cACA;YACA;cAAA;cACA;gBACAA;kBACAC;kBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA;gBAEAvE;kBACA,oDACAyF;gBACA;gBACAnB;gBACA;cACA;gBACA;cACA;YACA;YACA;YACA;YACA;cAAA;cACA;gBACAA;gBACA;gBACA;gBACApB;gBACA;gBACAoB;kBACA;oBACA,qCACAoB;oBACAnF,8BACAe,kBACA0B;oBACA;sBACAzC;oBACA;sBACAA;sBACA;wBACAA,mBACAgE;sBACA;sBACA;wBACAhE,gBACAgE;sBACA;sBACA;wBACAhE;sBACA;sBACA;wBACAA,2BACAgE;sBACA;sBACA;wBACAhE;sBACA;sBACA;wBACAA,0BACAoF;sBACA;oBACA;oBACA;sBACApF;sBACAA;oBACA;oBACA;sBACA;sBACAA;oBACA;oBACA;kBACA;kBACA4E;gBACA;gBAEA;cACA;gBACA;kBACAZ,+CACA;kBACAA;gBACA;;gBACA;cACA;cACA;YACA;YACA;YACA;YACA;cAAA;cACAD;cACA;cACA;cACA;cACAA;gBACApB;gBACA;kBACA;kBACA;kBACA;kBACA;oBACA;kBACA;kBACA3C;kBACAA;kBACAA;kBACA;oBACA;sBACAA;oBACA;sBACA;sBACA;sBACA;wBACAgE;0BACA;4BACAqB;0BACA;wBACA;wBACArF;sBACA;wBACAA;sBACA;oBACA;kBACA;oBACAA;oBACA;sBAAA;sBACAA;sBACAA;oBACA;oBACA;sBAAA;sBACAA;oBACA;kBACA;kBACA;gBACA;gBACA4E;cACA;cACA;cACAjC;cACA;YACA;cAAA;cACAoB;cACA;cACA;gBACA;kBACAC;kBACAA;gBACA;kBACAA;kBACAA,sEACA5D,MACA;kBACA4D;gBACA;cACA;cACA;YACA;cAAA;cACAD;cACA;cACA;cACAA;gBACA;kBACA;kBACA/D;kBACA;kBACAA;kBACA;gBACA;gBACAsF;cACA;cACA;cACA;UAAA;QAEA;UACAnC;QACA;MACA;MACA;MACA;MACA,mGACA;QACA;QACA;QACAW;UACA,wDACAyB,IACA;YACA;UACA;UACA;YACAvB;YACA;YACAA;YACA;cACA;cACA;cACA;cACA;cACA;YAAA,CACA;cACAA;YACA;UACA;UACA;YACA;cACA;cACA;YACA;cAAA;cACA;cACAwB;cACAA;cACA;YACA;cAAA;cACA;cACA;cACAC;cACAA;cACAA;cACA;UAAA;QAEA;MACA;MAEA;QACA3B;UACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;MACA;IACA;IACA4B;MACA;QACA;MACA;QACA;QACA;UACAC;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA5B;QACA;MACA;MACAF;QACArE;MACA;QACAuE;QACA;MACA;IACA;IACA;IACA6B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC,aACAhC;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA;gBACA;gBAAA,CAEA;kBACAL;kBACA;oBACA;sBACA;oBACA;sBACA;wBAAAK;sBAAA;sBACA;oBACA;kBACA;;kBACA;oBACA;sBACA;oBACA;kBACA;kBACA;oBACA;sBACA;oBACA;kBACA;kBACA;oBAAA;oBACA;sBACA;sBACA;wBACAgB;sBACA;oBACA;kBACA;kBACA;oBACA;sBACA;oBACA;kBACA;kBACA;oBACA;sBACA;oBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACArB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAoD;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAD,aACAhC;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA,IACAc;kBAAAb;kBAAAD;gBAAA;;gBAEA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA,CACA;kBACA;oBACA;sBACA;oBACA;sBACA;wBAAAA;sBAAA;oBACA;kBACA;kBACA;oBACA;sBACA;oBACA;kBACA;;kBACA;oBACA;sBACA;oBACA;kBACA;;kBACA;oBACA;sBACA;sBACA;wBACAgB;sBACA;oBACA;kBACA;kBACA;oBACA;sBACA;oBACA;kBACA;;kBACA;oBACA;sBACA;oBACA;kBACA;;kBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAgC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAF,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAJ,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAL,aACAhC;kBAAAsC;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACAzD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACA0D;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAP,aACAhC;kBAAAsC;kBAAAE;kBAAAC;gBAAA,IACAzC;kBAAAsC;kBAAAE;kBAAAC;gBAAA,IACAzC;kBAAAsC;kBAAAE;kBAAAC;gBAAA;gBACA;gBACA;gBACA;gBACA;gBAAA,CAEA;kBACA5D;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACA;oBACA;oBACA;sBACAqB;oBACA;kBACA;kBACA;oBACA;oBACA;oBACA;sBACAA;oBACA;kBACA;kBACA;oBAAA;oBACA;oBACA;oBACA;sBACAA;oBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACArB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6D;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAV,aACAhC;kBAAAmC;gBAAA;gBACA;gBAAA,CACA;kBACA;oBACA;oBACA;oBACA;sBACAlC;oBACA;sBACAA;oBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACA0C;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAX,aACAhC;kBAAA4C;gBAAA,GACA;kBACA;oBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAb,aACAhC;kBAAA8C;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAf,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAa;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAhB,aACAhC;kBAAAiD;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAlB,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACAlC;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAkD;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAnB,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACAlC;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAmD;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACApB,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAkB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACArB,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAmB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAtB,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAoB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAvB,aACAhC;kBAAAmC;gBAAA,GACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAqB;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAxH;gBAAAgF;gBAAAyC;gBAAAC;gBAAAC;gBACAC;gBACA/E;gBACAlD;gBACAkI;gBACAhF;gBACA;gBACA;gBAAA,gBAEA;gBAAA,oCAEA,yCACA,+CACA,yCACA,uCACA,mCACA,oCACA,wCACA,wCAEA;gBAAA;cAAA;gBACA;kBACA;kBACA,oEACAqB,oDACAA,uDACAA,mEACAA,8DACA4D,uBACA5D,oDACAA,mEACAA;gBACA;gBACArB;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAkF;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA/H;gBACA6C;gBAAA,MACA7C;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAIA6C;gBACA;gBACA;gBACAb,iDACA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACAa;kBACAQ;oBACAC;kBACA;gBACA;kBACAT;kBACAmF,6EACA;kBACA;kBACA3E;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA2E;MAAA;MACA;MACA;MACA;QAAA;QACApE;UACAE;QACA;MACA;QACAF;UACAE;QACA;MACA;MACA;;MAEAmE;QACA;QACA;UACAC,gGACAxC;UACAyC,0HACAA;QACA;QACA;;QAEA;QACA;QACA;UACAC;QACA;UACAC,iGACAC;QACA;UACAD,iGACAE;QACA;QACA;UACAL,sFACAA;UACAC,8EACAA;QACA;QACA;QACA;UACAK;UACAzI;UACAL;QACA;QACA;UACAqE;YACAC;YACAA;cACA,8DACAyE,cACA;gBACA/I;cACA;cACA,8DACA+I,cACA;gBACA/I;cACA;YACA;YACAgJ;YACA;YACA;UACA;QACA;MAEA;IACA;IACAC;MACA;MACA1E;MACA;QACA;UACAvE;QACA;MACA;MACA;IACA;IACAkJ;MAAA;MACAX;QACA;UACA7E;YACAyF;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;QACA;MAEA;QAAA;QACA;QACA;MACA;;MAEA;MACA;QAAA;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;MACAjG;MACA;MACA;MACA;QACAlB;MACA;QACAA,6FACAA;MACA;MACA;MACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;MACA;MACA;QACAA;MACA;MAEA;MACA;QACAoH;MACA;MACA;QACAA;MACA;MAEA;QACAA;QACAC;QACArH;QAAA;QACAF;QAAA;QACAwH;QAAA;QACApF;QACAqF;QACAC;QACAC;QACAC;QACAC;MACA;MACA9F;QACA;QACAX;MACA;QACAA;QACA;UACAa;QACA;MACA;MACA;QACAA;MACA;IACA;IACA6F;MACA1G;QACAC;MACA;IACA;IACA0G;MACA;QACA;UACA;YACA9F;UACA;QACA;MACA;MACA;QACAb;UACAC;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACx/DA;AAAA;AAAA;AAAA;AAA6lC,CAAgB,glCAAG,EAAC,C;;;;;;;;;;;ACAjnC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAg9C,CAAgB,o6CAAG,EAAC,C;;;;;;;;;;;ACAp+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./healthFile.vue?vue&type=template&id=afb6f976&scoped=true&\"\nvar renderjs\nimport script from \"./healthFile.vue?vue&type=script&lang=js&\"\nexport * from \"./healthFile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./healthFile.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./healthFile.vue?vue&type=style&index=1&id=afb6f976&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"afb6f976\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/home/<USER>\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFile.vue?vue&type=template&id=afb6f976&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = [\"hypertension\", \"diabetes\"].indexOf(_vm.serviceSData.key)\n  var g1 = [\"childManage\"].indexOf(_vm.serviceSData.key)\n  var g2 = [\"outpatientInfo\", \"hospitalInfor\"].indexOf(_vm.serviceSData.key)\n  var g3 =\n    g2 == -1\n      ? [\"fileSummary\", \"pregnantWoman\"].indexOf(_vm.serviceSData.key)\n      : null\n  var l0 =\n    _vm.collapseData && _vm.serviceDataDetail.tabList\n      ? _vm.serviceDataDetail.tabList.filter(function (item) {\n          return item.label != \"新生儿记录\" && item.label != \"分娩记录\"\n        })\n      : null\n  var g4 = _vm.collapseData\n    ? (_vm.collapseData && _vm.collapseData.length > 0) ||\n      (_vm.collapseData &&\n        _vm.collapseData[0].data &&\n        _vm.collapseData[0].data.length > 0)\n    : null\n  var l2 =\n    _vm.collapseData && g4\n      ? _vm.__map(_vm.collapseData, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var l1 =\n            !(item.value && item.type != \"desc\") &&\n            !(item.type == \"desc\") &&\n            item.label == \"\"\n              ? _vm.collapseItemData(index)\n              : null\n          var g5 =\n            !(item.value && item.type != \"desc\") &&\n            !(item.type == \"desc\") &&\n            !(item.label == \"\")\n              ? _vm.collapseData[index].data.length\n              : null\n          return {\n            $orig: $orig,\n            l1: l1,\n            g5: g5,\n          }\n        })\n      : null\n  var g6 = _vm.collapseData\n    ? (_vm.collapseData && _vm.collapseData.length == 0) ||\n      (_vm.collapseData &&\n        _vm.collapseData[0].data &&\n        _vm.collapseData[0].data.length == 0)\n    : null\n  var g7 = _vm.serviceDataDetail.data && _vm.serviceDataDetail.data.length > 0\n  var l4 = _vm.__map(_vm.serviceDataDetail.data, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l3 = g7 ? _vm.serviceDataList(index) : null\n    return {\n      $orig: $orig,\n      l3: l3,\n    }\n  })\n  var g8 = _vm.serviceDataDetail.data && _vm.serviceDataDetail.data.length == 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index,\n        item = _temp2.item\n      var _temp, _temp2\n      index == 0 ? _vm.handleLongPress(item, \"top\") : \"\"\n    }\n    _vm.e1 = function ($event, sitem, item, collapseIndex, index) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        sitem = _temp4.sitem,\n        item = _temp4.item,\n        collapseIndex = _temp4.collapseIndex,\n        index = _temp4.index\n      var _temp3, _temp4\n      sitem.type == \"detail\" && sitem.value\n        ? _vm.goDetail(sitem.label, \"\", sitem, item, collapseIndex, index)\n        : \"\"\n    }\n    _vm.e2 = function ($event, index, item) {\n      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        index = _temp6.index,\n        item = _temp6.item\n      var _temp5, _temp6\n      return _vm.handleMenuChange(index, item)\n    }\n    _vm.e3 = function ($event, item) {\n      var _temp7 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp8 = _temp7.eventParams || _temp7[\"event-params\"],\n        item = _temp8.item\n      var _temp7, _temp8\n      !item.noLongPress ? _vm.handleLongPress(item) : \"\"\n    }\n    _vm.e4 = function ($event, sitem, item, collapseIndex, sindex) {\n      var _temp9 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp10 = _temp9.eventParams || _temp9[\"event-params\"],\n        sitem = _temp10.sitem,\n        item = _temp10.item,\n        collapseIndex = _temp10.collapseIndex,\n        sindex = _temp10.sindex\n      var _temp9, _temp10\n      sitem.type == \"detail\" && sitem.value\n        ? _vm.goDetail(sitem.label, \"tab\", sitem, item, collapseIndex, sindex)\n        : \"\"\n    }\n    _vm.e5 = function (\n      $event,\n      sitem,\n      sindex,\n      collapseItem,\n      collapseIndex,\n      item,\n      index\n    ) {\n      var _temp11 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp12 = _temp11.eventParams || _temp11[\"event-params\"],\n        sitem = _temp12.sitem,\n        sindex = _temp12.sindex,\n        collapseItem = _temp12.collapseItem,\n        collapseIndex = _temp12.collapseIndex,\n        item = _temp12.item,\n        index = _temp12.index\n      var _temp11, _temp12\n      !sitem.noLongPress\n        ? _vm.handleLongPressMul2(\n            sitem,\n            sindex,\n            collapseItem,\n            collapseIndex,\n            item,\n            index\n          )\n        : \"\"\n    }\n    _vm.e6 = function ($event, sitem) {\n      var _temp13 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp14 = _temp13.eventParams || _temp13[\"event-params\"],\n        sitem = _temp14.sitem\n      var _temp13, _temp14\n      return _vm.handleLongPress(sitem)\n    }\n    _vm.e7 = function ($event, index) {\n      var _temp15 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp16 = _temp15.eventParams || _temp15[\"event-params\"],\n        index = _temp16.index\n      var _temp15, _temp16\n      $event.stopPropagation()\n      _vm.handleAIReportClick(\n        _vm.serviceDataDetail.data,\n        _vm.serviceDataList(index)\n      )\n    }\n    _vm.e8 = function ($event, sitem, item, index) {\n      var _temp17 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp18 = _temp17.eventParams || _temp17[\"event-params\"],\n        sitem = _temp18.sitem,\n        item = _temp18.item,\n        index = _temp18.index\n      var _temp17, _temp18\n      sitem.type == \"detail\" && sitem.value\n        ? _vm.goDetail(sitem.label, \"\", sitem, item, \"\", index)\n        : \"\"\n    }\n    _vm.e9 = function ($event, sitem) {\n      var _temp19 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp20 = _temp19.eventParams || _temp19[\"event-params\"],\n        sitem = _temp20.sitem\n      var _temp19, _temp20\n      return _vm.handleLongPress(sitem)\n    }\n    _vm.e10 = function ($event, sitem) {\n      var _temp21 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp22 = _temp21.eventParams || _temp21[\"event-params\"],\n        sitem = _temp22.sitem\n      var _temp21, _temp22\n      return _vm.handleLongPress(sitem)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        g4: g4,\n        l2: l2,\n        g6: g6,\n        g7: g7,\n        l4: l4,\n        g8: g8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFile.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<zlnavbar :isBack=\"true\" @home='goHome'>\r\n\t\t\t<block slot=\"content\">{{ serviceSData.name }}</block>\r\n\t\t</zlnavbar>\r\n\t\t<!-- 滚动菜单 -->\r\n\t\t<scrollMenu :menuData='scrollMenu' @menuIndex='handleTabMenuChange'></scrollMenu>\r\n\t\t<!-- main -->\r\n\t\t<view class=\"main-box\">\r\n\t\t\t<view class=\"list-box\">\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item\" style=\"padding: 16px;\">\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"list-lable list-label-big flex-1\"><span>{{ serviceSData.name }}</span></view>\r\n\t\t\t\t\t\t\t<view class=\"flex text-right \">\r\n\t\t\t\t\t\t\t\t<button @click=\"goHome\">返回档案首页</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 孕周 -->\r\n\t\t\t\t<view class=\"list-item-box list-item-box2\" v-if=\"serviceSData.key == 'pregnantWoman' && gestationalWeeks\">\r\n\t\t\t\t\t<view class=\"list-item\" style=\"padding: 16px;\">\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<!-- <view class=\"list-lable\">孕周</view> -->\r\n\t\t\t\t\t\t\t<view class=\"flex text-right\">{{ gestationalWeeks }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item-box\" style=\"padding:0 16px;\" v-if=\"serviceDataDetail.arrData\">\r\n\t\t\t\t\t\t<view class=\"list-item\" v-for=\"(item, index) in serviceDataDetail.arrData\" :key=\"index\"\r\n\t\t\t\t\t\t\t@longpress=\"index == 0 ? handleLongPress(item, 'top') : ''\">\r\n\t\t\t\t\t\t\t<view class=\"list-cell\" v-if=\"item.value\">\r\n\t\t\t\t\t\t\t\t<!-- 公共事件信息【高血压，糖尿病，顶部确诊日期在用】 -->\r\n\t\t\t\t\t\t\t\t<view class=\"list-lable\">\r\n\t\t\t\t\t\t\t\t\t{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t<!-- AAA0 -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-right \">{{ item.value }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 非折叠多条数据 -->\r\n\t\t\t\t\t\t\t<view class=\"no-collapse-content-box\" v-for=\"(collapseItem, collapseIndex) in collapseData[index].data\"\r\n\t\t\t\t\t\t\t\t:key=\"collapseIndex\" v-else-if=\"item.label == ''\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(sitem, index) in collapseItem\" :key=\"sitem.value\">\r\n\t\t\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type == 'detail' && 'list-item2']\">\r\n\t\t\t\t\t\t\t\t\t\t<view :class=\"['list-cell', sitem.type != 'detail' && 'sub-list-cell']\"\r\n\t\t\t\t\t\t\t\t\t\t\t@click=\"sitem.type == 'detail' && sitem.value ? goDetail(sitem.label, '', sitem, item, collapseIndex, index) : ''\"\r\n\t\t\t\t\t\t\t\t\t\t\t@longpress=\"handleLongPress(sitem)\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA1 -->\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 详情 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view :class=\"['text-right', sitem.type == 'detail' && 'text-right-btn']\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ sitem.value }}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-item collapse-content-desc\" @longpress=\"handleLongPress(sitem)\"\r\n\t\t\t\t\t\t\t\t\t\tv-if=\"sitem.type == 'desc'\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA2 -->\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ sitem.value }}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 普通带折叠 -->\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"collapse-item\">\r\n\t\t\t\t\t\t\t\t\t<view class='list-cell' @click.stop.prevent='handleChangePanel(item)'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='list-lable'>{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- AAA3 -->\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"iconfont icon-arrow-right arrow-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'collapse-item-arrow-active': item.isOpen, 'collapse-item--animation': item.isOpen }\"></i>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-show='item.isOpen'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='collapse-content' :class=\"{ 'is--transition': item.isOpen }\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(collapseItem, collapseIndex) in collapseData[index].data\" :key=\"collapseIndex\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-item\" v-for=\"(collapsesItem, collapsesIndex) in collapseItem\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:key=\"collapsesIndex\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\" @longpress=\"handleLongPress(collapsesItem)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ collapsesItem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA4 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-right \">{{ collapsesItem.value }}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 图表 -->\r\n\t\t\t\t<view class=\"list-item-box\" v-if=\"['hypertension', 'diabetes'].indexOf(serviceSData.key) > -1\">\r\n\t\t\t\t\t<view class=\"list-content-item\">\r\n\t\t\t\t\t\t<view class=\"list-item \">\r\n\t\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ serviceSData.key == 'hypertension' ? '近七次血压记录' : '近七次血糖记录' }}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-right\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"block-label\">{{ serviceSData.key == 'hypertension' ? '收缩压' : '血糖值' }}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"block-label\" v-if=\"serviceSData.key == 'hypertension'\">舒张压</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"list-item\">\r\n\t\t\t\t\t\t\t<view class=\"charts-label-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"charts-label-item\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"charts-label\">\r\n\t\t\t\t\t\t\t\t\t\t<span>{{ bsugarMg || 0 }}</span>{{ serviceSData.key == 'hypertension' ? 'mmHg' : 'mmol/L' }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ serviceSData.key == 'hypertension' ? '最近一次收缩压' : '最近一次空腹血糖' }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"charts-label-item\" v-if=\"serviceSData.key == 'hypertension'\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"charts-label\"><span>{{ dbp || 0 }}</span>mmHg</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">最近一次舒张压</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"charts-box\">\r\n\t\t\t\t\t\t\t\t<u-charts type=\"line\" :opts=\"diseaseOptions\" :chartData=\"chartData\" :disableScroll=\"true\"\r\n\t\t\t\t\t\t\t\t\t:ontouch=\"true\" :onzoom=\"true\" :onmovetip='true' :canvas2d='true' :tapLegend='true' />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 儿童 -->\r\n\t\t\t\t<view class=\"list-item-box\" v-if=\"['childManage'].indexOf(serviceSData.key) > -1\">\r\n\t\t\t\t\t<view class=\"tabs\">\r\n\t\t\t\t\t\t<view class=\"tabs-scroll\" v-if=\"serviceDataDetail.tabList\">\r\n\t\t\t\t\t\t\t<view class=\"tabs-scroll_item\" :class=\"{ 'tab-active': childCurIndex == index }\"\r\n\t\t\t\t\t\t\t\tv-for=\" (item, index) in childTabList\" :key=\"index\" @click=\"handleChildMenuChange(index)\">\r\n\t\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<i class=\"iconfont icon-shuoming\" @click=\"goRule\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"list-content-item\">\r\n\t\t\t\t\t\t<view class=\"charts-info-left\">{{ childCurIndex == 0 ? '身高（cm）' : '体重（kg）' }} </view>\r\n\t\t\t\t\t\t<view class=\"charts-box\">\r\n\t\t\t\t\t\t\t<u-charts type=\"mix\" :opts=\"childOptions\" :chartData=\"childChartData\" :disableScroll=\"true\"\r\n\t\t\t\t\t\t\t\t:ontouch=\"true\" :onzoom=\"true\" :onmovetip='true' :canvas2d='true' :tapLegend='true' />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"charts-info\">曲线根据《7岁以下儿童生长标准（WS/T 423-2022）》绘制</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 错误数据反馈 -->\r\n\t\t\t\t<view v-if=\"['outpatientInfo', 'hospitalInfor'].indexOf(serviceSData.key) == -1\">\r\n\t\t\t\t\t<view class=\"hint-label\">\r\n\t\t\t\t\t\t<span class=\"iconfont icon-tishi\"></span>错误数据反馈：如果您的健康档案信息有误，请长按错误数据可以进行错误反馈。\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"hint-label\" @click=\"showTips\"\r\n\t\t\t\t\t\tv-if=\"['fileSummary', 'pregnantWoman'].indexOf(serviceSData.key) > -1\">\r\n\t\t\t\t\t\t<span class=\"iconfont icon-dianji\"></span>点击查看不支持反馈的内容\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 根据导航菜单渲染列表与折叠内容 -->\r\n\t\t\t\t<view class=\"list-item-box\" v-if='collapseData'>\r\n\t\t\t\t\t<!-- 导航 -->\r\n\t\t\t\t\t<scroll-view class=\"tabs\" scroll-x=\"true\" scroll-with-animation :scroll-left=\"scrollLeft\">\r\n\t\t\t\t\t\t<view class=\"tabs-scroll\" v-if=\"serviceDataDetail.tabList\">\r\n\t\t\t\t\t\t\t<view class=\"tabs-scroll_item\" :class=\"{ 'tab-active': tabCur == index }\"\r\n\t\t\t\t\t\t\t\tv-for=\" (item, index) in serviceDataDetail.tabList.filter(item => item.label != '新生儿记录' && item.label != '分娩记录')\"\r\n\t\t\t\t\t\t\t\t:key=\"index\" @click=\"handleMenuChange(index, item)\">\r\n\t\t\t\t\t\t\t\t<!-- 档案摘要tab页签【基本信息，疾病信息】， 糖尿病，高血压，肺结核——随访和体检切换 -->\r\n\t\t\t\t\t\t\t\t{{ item.label }}\r\n\t\t\t\t\t\t\t\t<!-- AAA5 -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t<!-- 档案摘要 直接改动结构，后续不匹配，包一层if-else -->\r\n\t\t\t\t\t<view class=\"list-content-item\"\r\n\t\t\t\t\t\tv-if=\"collapseData && collapseData.length > 0 || collapseData && collapseData[0].data && collapseData[0].data.length > 0\">\r\n\t\t\t\t\t\t<view class=\"list-item list-item-no-collapse\" v-for=\"(item, index) in collapseData\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"list-cell\" @longpress=\"!item.noLongPress ? handleLongPress(item) : ''\"\r\n\t\t\t\t\t\t\t\tv-if=\"item.value && item.type != 'desc'\">\r\n\t\t\t\t\t\t\t\t<!-- 档案摘要——基本信息， 肺结核——全程管理 -->\r\n\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t<!-- AAA6 -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-right\">\r\n\t\t\t\t\t\t\t\t\t<span v-if=\"!item.encryptData\">{{ item.value }}</span>\r\n\t\t\t\t\t\t\t\t\t<span v-else>{{ item.encryptData }}</span>\r\n\t\t\t\t\t\t\t\t\t<!-- 小眼睛 -->\r\n\t\t\t\t\t\t\t\t\t<span :class=\"['iconfont eye-icon', item.icon]\" v-if=\"item.icon && item.value != '无'\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"getDecryptData(item)\"></span>\r\n\t\t\t\t\t\t\t\t\t<!-- 待确认 -->\r\n\t\t\t\t\t\t\t\t\t<span v-if=\"item.key1\">{{ item.value1 }}</span>\r\n\t\t\t\t\t\t\t\t\t<span v-if=\"item.key2\">{{ item.value2 }}</span>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- collapse-content-desc -->\r\n\t\t\t\t\t\t\t<view @longpress=\"handleLongPress(item)\" v-else-if=\"item.type == 'desc'\">\r\n\t\t\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t\t<!-- AAA7 -->\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ item.value }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"no-collapse-content-box\" v-for=\"(collapseItem, collapseIndex) in collapseItemData(index) \"\r\n\t\t\t\t\t\t\t\t:key=\"collapseIndex\" v-else-if=\"item.label == ''\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(sitem, sindex) in collapseItem\" :key=\"sitem.value\">\r\n\t\t\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type == 'detail' && 'list-item2']\" v-if=\"sitem.type != 'desc'\">\r\n\t\t\t\t\t\t\t\t\t\t<view :class=\"['list-cell', sitem.type != 'detail' && 'sub-list-cell']\"\r\n\t\t\t\t\t\t\t\t\t\t\t@click=\"sitem.type == 'detail' && sitem.value ? goDetail(sitem.label, 'tab', sitem, item, collapseIndex, sindex) : ''\"\r\n\t\t\t\t\t\t\t\t\t\t\t@longpress=\"!sitem.noLongPress ? handleLongPressMul2(sitem, sindex, collapseItem, collapseIndex, item, index) : ''\">\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 糖尿病，高血压，肺结核——随访列表，体检列表 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA8 -->\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 详情 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view :class=\"['text-right', sitem.type == 'detail' && 'text-right-btn']\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ sitem.value }}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-item collapse-content-desc\" @longpress=\"handleLongPress(sitem)\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA9 -->\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ sitem.value }}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- 普通带折叠 -->\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"collapse-item\">\r\n\t\t\t\t\t\t\t\t\t<view class='list-cell' @click.stop.prevent='handleChangePanel(item)'>\r\n\t\t\t\t\t\t\t\t\t\t<!-- 档案摘要——疾病信息 -->\r\n\t\t\t\t\t\t\t\t\t\t<view class='list-lable'>{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- AAA10 -->\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<i v-if=\"collapseData[index].data.length > 0\" class=\"iconfont icon-arrow-right arrow-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'collapse-item-arrow-active': item.isOpen, 'collapse-item--animation': item.isOpen }\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span v-else>无</span>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-show='item.isOpen'>\r\n\t\t\t\t\t\t\t\t\t\t<!-- {{  collapseData[index].data }} -->\r\n\t\t\t\t\t\t\t\t\t\t<view class='collapse-content' :class=\"{ 'is--transition': item.isOpen }\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(collapseItem, collapseIndex) in collapseData[index].data\" :key=\"collapseIndex\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view :class=\"collapsesItem.type != 'none' ? 'list-item' : ''\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tv-for=\"(collapsesItem, collapsesIndex) in collapseItem\" :key=\"collapsesIndex\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"collapsesItem.type == 'none'\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- 下面index  不能乱改成 collapsesIndex -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t@longpress=\"handleLongPressMul2(collapsesItem, collapsesIndex, collapseItem, collapseIndex, item, index)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-else-if=\"collapsesItem.type != 'desc'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- 档案摘要——疾病信息——数组项 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ collapsesItem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA11 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-right\">{{ collapsesItem.value || '' }}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"collapse-content-desc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t@longpress=\"handleLongPressMul2(collapsesItem, collapsesIndex, collapseItem, collapseIndex)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ collapsesItem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA12 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ collapsesItem.value }}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<empty-placeholder\r\n\t\t\t\t\t\tv-if=\"collapseData && collapseData.length == 0 || collapseData && collapseData[0].data && collapseData[0].data.length == 0\"></empty-placeholder>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 门诊信息：带详情跳转的内容-->\r\n\t\t\t\t<view class=\"list-item-box\" v-for=\"(item, index) in serviceDataDetail.data\" :key=\"index\"\r\n\t\t\t\t\tv-if=\"serviceDataDetail.data && serviceDataDetail.data.length > 0\">\r\n\t\t\t\t\t<view class=\"list-lableAI\" @click.stop=\"handleAIReportClick(serviceDataDetail.data,serviceDataList(index))\">\r\n\t\t\t\t\t\t<image src=\"/static/images/frame.png\" mode=\"aspectFit\" class=\"ai-icon\" v-if=\"serviceSData.symbol == 'mzxx' || serviceSData.symbol == 'zyxx'\"></image>\r\n\t\t\t\t\t\t<text class=\"ai-label\" v-if=\"serviceSData.symbol == 'mzxx' || serviceSData.symbol == 'zyxx'\">AI报告分析</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"list-content-item\">\r\n\t\t\t\t\t\t<block v-for=\"(sitem, index) in serviceDataList(index)\" :key=\"sitem.value\">\r\n\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type == 'detail' && 'list-item2']\" v-if=\"sitem.type != 'desc'\">\r\n\t\t\t\t\t\t\t\t<view class=\"list-cell\"\r\n\t\t\t\t\t\t\t\t\t@click=\"sitem.type == 'detail' && sitem.value ? goDetail(sitem.label, '', sitem, item, '', index) : ''\"\r\n\t\t\t\t\t\t\t\t\t@longpress=\"handleLongPress(sitem)\" v-if=\"sitem.key !== 'jktjfldm'\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">\r\n\t\t\t\t\t\t\t\t\t\t<!-- 门诊信息——列表，体检管理——列表， 家医签约 -->\r\n\t\t\t\t\t\t\t\t\t\t{{ serviceSData.key == 'homeDoctor' && sitem.type == 'detail' ? '签约日期：' : '' }}{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\tAAA13\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<!-- 详情 -->\r\n\t\t\t\t\t\t\t\t\t<view :class=\"['text-right', sitem.type == 'detail' && 'text-right-btn']\">\r\n\t\t\t\t\t\t\t\t\t\t{{ sitem.value }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- collapse-content-desc -->\r\n\t\t\t\t\t\t\t<view class=\"list-item \" @longpress=\"handleLongPress(sitem)\" v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t\t\t<!-- 门诊信息——列表，体检管理——列表（textarea） -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t<!-- AAA14 -->\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ sitem.value }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-item-box\" v-if=\"serviceDataDetail.data && serviceDataDetail.data.length == 0\">\r\n\t\t\t\t\t<empty-placeholder></empty-placeholder>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"main-btn bottom-btn\" v-if=\"false\">错误数据反馈</button>\r\n\t\t</view>\r\n\t\t<modalDialog :closeVisible='true' :modalData=\"modalData\" @confirm=\"handleConfirm\" @cancel='handleCancel'\r\n\t\t\tv-show=\"modalVisible\">\r\n\t\t</modalDialog>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/api.js\";\r\nimport uCharts from '@/packagePages/components/echarts/echarts.vue';\r\nimport scrollMenu from \"@/packagePages/components/scrollMenu/index.vue\";\r\nimport emptyPlaceholder from '@/packagePages/components/empty.vue'\r\nimport modalDialog from \"@/components/dialog/dialog.vue\";\r\nimport {\r\n\tserviceJsonData\r\n} from './serviceJsonData.js'\r\nimport {\r\n\tchildHeightBoyData,\r\n\tchildHeightGirlData,\r\n\tchildWeightBoyData,\r\n\tchildWeightGirlData,\r\n\tdiabetesData,\r\n\thypertensionData,\r\n\tchildOptions,\r\n\tdiseaseOptions\r\n} from '@/utils/chartOptionData.js'\r\nimport {\r\n\tparseTime,\r\n\ttoast,\r\n} from \"@/utils/util.js\";\r\nimport {\r\n\tdiseaseHistory\r\n} from \"@/utils/enum.js\";\r\nexport default {\r\n\tcomponents: {\r\n\t\tscrollMenu,\r\n\t\tuCharts,\r\n\t\temptyPlaceholder,\r\n\t\tmodalDialog\r\n\t},\r\n\tcomputed: {\r\n\t\tcollapseItemData() {\r\n\t\t\treturn function (index) {\r\n\t\t\t\treturn this.collapseData[index].data || []\r\n\t\t\t};\r\n\t\t},\r\n\t\tserviceDataList() {\r\n\t\t\treturn function (index) {\r\n\t\t\t\treturn this.serviceDataDetail.data[index] || []\r\n\t\t\t};\r\n\t\t},\r\n\t\t// userInfo() {\r\n\t\t// \treturn this.$store.getters.userInfo\r\n\t\t// }\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tabnormalDataType: '',\r\n\t\t\tnavType: '',\r\n\t\t\tmodalVisible: false,\r\n\t\t\tmodalData: {\r\n\t\t\t\ttype: 'form',\r\n\t\t\t\ttitle: '错误数据反馈',\r\n\t\t\t\tinfo: '',\r\n\t\t\t\tbtnTxt: '提交',\r\n\t\t\t\tformData: [{\r\n\t\t\t\t\tvalue: null,\r\n\t\t\t\t\tkey: 'reason',\r\n\t\t\t\t\ttype: 'input',\r\n\t\t\t\t\tlabel: '',\r\n\t\t\t\t\tplaceholder: '请输入异常原因'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: null,\r\n\t\t\t\t\tkey: 'amendData',\r\n\t\t\t\t\ttype: 'input',\r\n\t\t\t\t\tlabel: '',\r\n\t\t\t\t\tplaceholder: '请输入更正数据'\r\n\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t},\r\n\t\t\tchartData: {},\r\n\t\t\tdiseaseOptions: diseaseOptions,\r\n\t\t\ttype: 'grey',\r\n\t\t\tisOpen: false,\r\n\t\t\tcollapseHeight: 0,\r\n\t\t\tscrollMenu: [],\r\n\t\t\tbsugarMg: '',\r\n\t\t\tdbp: '',\r\n\t\t\ttabCur: 0,\r\n\t\t\tserviceSData: '',\r\n\t\t\tserviceDataDetail: '',\r\n\t\t\tcollapseData: null,\r\n\t\t\tchildCurIndex: 0,\r\n\t\t\tchildTabList: ['儿童身高趋势图', '儿童体重趋势图'],\r\n\t\t\tchildChartData: {},\r\n\t\t\tchildOptions: childOptions,\r\n\t\t\tgestationalWeeks: '',\r\n\t\t\tapiData: null,\r\n\t\t\tapiDataType: '',\r\n\t\t\trequestData: null, // 接口请求数据，保存起来处理跳转详情页面\r\n\t\t\tpregnantOtherInspections: '',\r\n\t\t\tuserInfo: null,\r\n\t\t\ttuberculosisVisit: '',\r\n\t\t\tencryptData: '',\r\n\t\t\tmedicareidEncrypt: '',\r\n\t\t\tfamilyItem: null,\r\n\t\t\tcolumnName: null,\r\n\t\t\tmoduleCode: null,\r\n\t\t\tbusinessName: '',\r\n\t\t\tinfoData: [{\r\n\t\t\t\t\r\n\t\t\t}],\r\n\t\t\tmenuIndex: null,\r\n\t\t\tyytjList: [{\r\n\t\t\t\tinfoData: [],\r\n\t\t\t\tjyxm: [],\r\n\t\t\t\tyxjc: []\r\n\t\t\t}],\r\n\t\t}\r\n\t},\r\n\tonReady() {\r\n\t\tif (['childManage', 'diabetes', 'hypertension'].indexOf(this.serviceSData.key) > -1) {\r\n\t\t\tthis.getChartsData();\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// console.log(\"onLoad\", options,this.serviceSData.key)\r\n\t\t// console.log(options., 'options====health');\r\n\t\tthis.moduleCode = options.moduleCode || '' // 消息提醒类型\r\n\t\tthis.serviceSData = JSON.parse(options.item) // 服务项菜单JSON\r\n\t\t// console.log('******************',this.serviceSData)\r\n\t\tthis.familyItem = JSON.parse(options.familyItem) // 用户信息\r\n\t\tthis.scrollMenu = [this.familyItem] // 顶部菜单用户\r\n\t\tthis.serviceDataDetail = serviceJsonData[this.serviceSData.key] // 服务项菜单具体内容项\r\n\t\tthis.collapseData = this.serviceDataDetail.tabList && this.serviceDataDetail.data[this.serviceDataDetail\r\n\t\t\t.tabList[this.tabCur].value] // 带导航服务项菜单具体内容项\r\n\t\tthis.apiDataType = this.serviceDataDetail.tabList && this.serviceDataDetail.tabList[0].type // 数据类型\r\n\t\t// 处理对应服务子项tab\r\n\t\tif (this.serviceSData.moduleCodes && this.moduleCode) {\r\n\t\t\tvar currentIndex = this.serviceSData.moduleCodes.indexOf(Number(this.moduleCode))\r\n\t\t\tthis.tabCur = currentIndex > -1 ? currentIndex : 0\r\n\t\t}\r\n\t\t// console.log(\"this.serviceDataDetail\", this.serviceDataDetail)\r\n\t\t// console.log(\"this.collapseData\", this.collapseData)\r\n\t},\r\n\tonShow() {\r\n\t\tthis.menuIndex = uni.getStorageSync('menuIndex')\r\n\t\tconsole.log(\"人员切换\", this.menuIndex)\r\n\t\tthis.getUserInfo()\r\n\t\tthis.$forceUpdate()\r\n\t},\r\n\tonUnload() {\r\n\t\tif (!this.navType) {\r\n\t\t\tthis.pageHide()\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t//跳转AI\r\n\t\t handleAIReportClick(item,serviceDataList) {\r\n\t\t\t console.log('itemSSSSSSSSSSSSSSSSSSSSS',item,serviceDataList)\r\n\t\t\tlet destype = '3', jcjybgdh = '', yljgdm = '', jzlsh = '', archiveId = uni.getStorageSync('archiveId')\r\n\t\t\tif(serviceDataList && serviceDataList.length > 0){\r\n\t\t\t\tserviceDataList.forEach((item) => {\r\n\t\t\t\t\tif (item.key == 'jzlsh') {\r\n\t\t\t\t\t\tjzlsh = item.value\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (item.yljgdm) {\r\n\t\t\t\t\t\tyljgdm = item.yljgdm\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/packagePages/my/dialogue?yljgdm=${yljgdm}&type=2&destype=${destype}&reportid=${jcjybgdh}&jzlsh=${jzlsh}&cxjmjkdabh=${archiveId}`\r\n\t\t\t})\r\n\t\t  },\r\n\t\tshowTips() {\r\n\t\t\tvar content = this.serviceSData.key == 'fileSummary' ? '身份证号，出生日期，居民健康档案ID，档案状态，责任医生，档案管理机构' : '产前检查'\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: content,\r\n\t\t\t\tshowCancel: false,\r\n\t\t\t})\r\n\t\t},\r\n\t\tgoHome() {\r\n\t\t\tthis.navType = 'home'\r\n\t\t\t//清除uni.getStorageSync('referrerInfo')中的内容\r\n\t\t\tif(uni.getStorageSync('referrerInfo')){\r\n\t\t\t\tuni.removeStorageSync('referrerInfo')\r\n\t\t\t}\r\n\t\t\tuni.reLaunch({\r\n\t\t\t\turl: `/pages/index/index?menuIndex=${this.menuIndex}`\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetUserInfo(callback) {\r\n\t\t\tthis.$store.dispatch('user/storeSetUserInfo').then((res) => {\r\n\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\tthis.userInfo = res.data\r\n\t\t\t\t\tif (this.userInfo.idCardDecrypt) {\r\n\t\t\t\t\t\tthis.getData()\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// callback(res.data)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetData() {\r\n\t\t\tuni.showLoading({})\r\n\t\t\tconsole.log(\"menuIndex\", this.menuIndex)\r\n\t\t\tlet params = null\r\n\t\t\tif (this.menuIndex > 0) {\r\n\t\t\t\t// 门诊, 摘要\r\n\t\t\t\tif (['fileSummary', 'outpatientInfo', 'hospitalInfor'].indexOf(this.serviceSData.key) > -1) {\r\n\t\t\t\t\tparams = {\r\n\t\t\t\t\t\tsfzjhm: this.familyItem?.idCard\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tparams = {\r\n\t\t\t\t\t\tcxjmjkdabh: this.familyItem?.archiveId,\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 门诊, 摘要\r\n\t\t\t\tif (['fileSummary', 'outpatientInfo', 'hospitalInfor'].indexOf(this.serviceSData.key) > -1) {\r\n\t\t\t\t\tparams = {\r\n\t\t\t\t\t\tsfzjhm: this.familyItem?.idCardDecrypt\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tparams = {\r\n\t\t\t\t\t\tcxjmjkdabh: uni.getStorageSync('archiveId'),\r\n\t\t\t\t\t\tsfzjhm: this.familyItem?.idCardDecrypt\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// -1 : 查所有；4 高血压体检；5 糖尿病体检\r\n\t\t\tif (this.serviceSData.key == 'diabetes') {\r\n\t\t\t\tparams.jktjfldm = \"4\"\r\n\t\t\t} else if (this.serviceSData.key == 'hypertension') {\r\n\t\t\t\tparams.jktjfldm = \"5\"\r\n\t\t\t} else if (this.serviceSData.key == 'elderly') {\r\n\t\t\t\tparams.jktjfldm = \"2\"\r\n\t\t\t} else {\r\n\t\t\t\tparams.jktjfldm = \"-1\"\r\n\t\t\t}\r\n\t\t\t// params.jktjfldm = \"-1\"  // 覆盖体检\r\n\r\n\t\t\t// 孕产妇其他次产前检查\r\n\t\t\tif (['pregnantWoman'].indexOf(this.serviceSData.key) > -1) {\r\n\t\t\t\tapi[`${this.serviceSData.key}Api00`](params).then(res => {\r\n\t\t\t\t\tres.data.map(item => {\r\n\t\t\t\t\t\titem.inspections = '其他次产前检查'\r\n\t\t\t\t\t\titem.type = 'otherInspections'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.pregnantOtherInspections = res.data\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// 肺结核第一次随访记录——————9.12去掉\r\n\t\t\t// if (['tuberculosis'].indexOf(this.serviceSData.key) > -1) {\r\n\t\t\t// \tapi[`${this.serviceSData.key}Api0_1`](params).then(res => {\r\n\t\t\t// \t\tif (res.data) {\r\n\t\t\t// \t\t\tres.data.visit = '第一次入户随访'\r\n\t\t\t// \t\t\tres.data.type = 'firstVist'\r\n\t\t\t// \t\t\tthis.tuberculosisVisit = res.data\r\n\t\t\t// \t\t}\r\n\t\t\t// \t})\r\n\t\t\t// }\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconsole.log(\"getData接口\", `${this.serviceSData.key}Api${this.tabCur}`)\r\n\t\t\t\t// 其余公共接口\r\n\t\t\t\tapi[`${this.serviceSData.key}Api${this.tabCur}`](params).then(res => {\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\tswitch (this.serviceSData.key) {\r\n\t\t\t\t\t\tcase 'fileSummary': // 档案摘要\r\n\r\n\t\t\t\t\t\t\tlet obj = res.data\r\n\t\t\t\t\t\t\tres.data = { ...obj }\r\n\r\n\t\t\t\t\t\t\t// 过敏史 ehr_grgms\r\n\t\t\t\t\t\t\tlet ehr_grgms = []\r\n\t\t\t\t\t\t\tif (obj.ehr_grgms) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_grgms')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_grgms.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_grgms.push([\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\tlabel: \"药物过敏源名称\",\r\n\t\t\t\t\t\t\t\t\t\t\tvalue: item.ywgmymc,\r\n\t\t\t\t\t\t\t\t\t\t\tkey: 'ywgmymc'\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\tlabel: \"登记日期\",\r\n\t\t\t\t\t\t\t\t\t\t\tvalue: item.gmdjrq?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\t\tkey: 'gmdjrq'\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 暴露史 ehr_bls\r\n\t\t\t\t\t\t\tlet ehr_bls = []\r\n\t\t\t\t\t\t\tif (obj.ehr_bls) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_bls')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_bls.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_bls.push([{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"暴露类别名称\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.bllbmc,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'bllbmc'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"登记日期\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.djrq?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\tkey: 'djrq'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 疾病史 ehr_jwjbs\r\n\t\t\t\t\t\t\tlet ehr_jwjbs = []\r\n\t\t\t\t\t\t\tif (obj.ehr_jwjbs) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_jwjbs')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_jwjbs.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_jwjbs.push([{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"疾病名称\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.jwhbzlmc,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'jwhbzlmc'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"确诊日期\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.jwhbqzrq?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\tkey: 'jwhbqzrq'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 手术史 ehr_jwsss /外伤 ehr_jwwss /输血史 ehr_jwsxs\r\n\t\t\t\t\t\t\tlet ehr_jwsss = []\r\n\t\t\t\t\t\t\tif (obj.ehr_jwsss) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_jwsss')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_jwsss.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_jwsss.push([{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"手书史\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.sss,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'sss'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"手术操作日期\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.ssczrqsj?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\tkey: 'ssczrqsj'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 外伤史\r\n\t\t\t\t\t\t\tlet ehr_jwwss = []\r\n\t\t\t\t\t\t\tif (obj.ehr_jwwss) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_jwwss')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_jwwss.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_jwwss.push([{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"外伤名称\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.wsmc,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'wsmc'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"外伤发生日期\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.wsfsrqsj?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\tkey: 'wsfsrqsj'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 输血史\r\n\t\t\t\t\t\t\tlet ehr_jwsxs = []\r\n\t\t\t\t\t\t\tif (obj.ehr_jwsxs) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_jwsxs')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_jwsxs.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_jwsxs.push([{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"输血原因\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.sxyy,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'sxyy'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"输血日期\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.sxrqsj?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\tkey: 'wsfsrqsj'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 遗传病史 ehr_ycbs\r\n\t\t\t\t\t\t\tlet ehr_ycbs = []\r\n\t\t\t\t\t\t\tif (obj.ehr_ycbs) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_ycbs')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_ycbs.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_ycbs.push([{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"家族性疾病名称\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.ycjbmc,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'bllbmc'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"与本人关系\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.qsgxmc,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'qsgxmc'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"登记日期\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.djrq?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\tkey: 'djrq'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 家族病史 ehr_jzbs\r\n\t\t\t\t\t\t\tlet ehr_jzbs = []\r\n\t\t\t\t\t\t\tif (obj.ehr_jzbs) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_jzbs')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_jzbs.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_jzbs.push([{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"家族性疾病名称\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.jzxjbmc,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'bllbmc'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"与本人关系\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.qsgxmc,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'qsgxmc'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"登记日期\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.djrq?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\tkey: 'djrq'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 残疾状况 ehr_cjqk\r\n\t\t\t\t\t\t\tlet ehr_cjqk = []\r\n\t\t\t\t\t\t\tif (obj.ehr_cjqk) {\r\n\t\t\t\t\t\t\t\tlet arr = this.serviceDataDetail.data.data2.filter(item => item.key == 'ehr_cjqk')\r\n\t\t\t\t\t\t\t\tlet xh = arr[0].businessId || ''\r\n\t\t\t\t\t\t\t\tobj.ehr_cjqk.map(item => {\r\n\t\t\t\t\t\t\t\t\tehr_cjqk.push([{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"序号\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item[xh],\r\n\t\t\t\t\t\t\t\t\t\tarchiveid: item.cxjmjkdabh,\r\n\t\t\t\t\t\t\t\t\t\ttype: \"none\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"残疾情况名称\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.cjqkmc,\r\n\t\t\t\t\t\t\t\t\t\tkey: 'cjqkmc'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tlabel: \"登记日期\",\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.djrq?.substr(0, 10) || '',\r\n\t\t\t\t\t\t\t\t\t\tkey: 'djrq'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t])\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.collapseData.map(item => {\r\n\t\t\t\t\t\t\t\titem.isOpen = false\r\n\t\t\t\t\t\t\t\tif (item.key == \"ehr_grgms\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_grgms || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_grgms'\r\n\t\t\t\t\t\t\t\t} else if (item.key == \"ehr_bls\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_bls || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_bls'\r\n\t\t\t\t\t\t\t\t} else if (item.key == \"ehr_jwjbs\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_jwjbs || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_jwjbs'\r\n\t\t\t\t\t\t\t\t} else if (item.key == \"ehr_jwsss\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_jwsss || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_jwsss'\r\n\t\t\t\t\t\t\t\t} else if (item.key == \"ehr_jwwss\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_jwwss || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_jwwss'\r\n\t\t\t\t\t\t\t\t} else if (item.key == \"ehr_jwsxs\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_jwsxs || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_jwsxs'\r\n\t\t\t\t\t\t\t\t} else if (item.key == \"ehr_ycbs\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_ycbs || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_ycbs'\r\n\t\t\t\t\t\t\t\t} else if (item.key == \"ehr_jzbs\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_jzbs || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_jzbs'\r\n\t\t\t\t\t\t\t\t} else if (item.key == \"ehr_cjqk\") {\r\n\t\t\t\t\t\t\t\t\titem.data = ehr_cjqk || '无'\r\n\t\t\t\t\t\t\t\t\titem.key = 'ehr_cjqk'\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\titem.value = res.data[item.key] || '无'\r\n\t\t\t\t\t\t\t\t\tif (item.key == 'csrq') {\r\n\t\t\t\t\t\t\t\t\t\titem.value = res.data['csrq'].substr(0, 10)\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tif (item.key == 'sfzjhm') {\r\n\t\t\t\t\t\t\t\t\t\titem.encrypt = res.data['sfzjhmEncrypt']\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tif (item.key == 'sbkh') {\r\n\t\t\t\t\t\t\t\t\t\titem.encrypt = res.data['sbkhEncrypt']\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\titem.businessId = res.data['cxjmjkdabh'] // xh  \r\n\t\t\t\t\t\t\t\t\titem.archiveid = res.data['cxjmjkdabh']\r\n\r\n\t\t\t\t\t\t\t\t\tif (item.key1 || item.key2) {\r\n\t\t\t\t\t\t\t\t\t\titem.value1 = res.data[item.key1]\r\n\t\t\t\t\t\t\t\t\t\titem.value2 = res.data[item.key2]\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'vaccination': // 预防接种\r\n\t\t\t\t\t\t\tif (this.apiDataType == 'multiple') {\r\n\t\t\t\t\t\t\t\tlet temData = this.collapseData[0].data\r\n\t\t\t\t\t\t\t\tlet result = []\r\n\t\t\t\t\t\t\t\tres.data.forEach((item) => {\r\n\t\t\t\t\t\t\t\t\tlet list = temData[0].map((subItem) => {\r\n\t\t\t\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(subItem))\r\n\t\t\t\t\t\t\t\t\t\t// info.businessId = item[this\r\n\t\t\t\t\t\t\t\t\t\t// \t.serviceDataDetail\r\n\t\t\t\t\t\t\t\t\t\t// \t.tabList[this.tabCur].businessId] || item.cxjmjkdabh\r\n\t\t\t\t\t\t\t\t\t\t// 2个tab 3种传值\r\n\t\t\t\t\t\t\t\t\t\tinfo.businessId = item.yfjzjzid || item.yfjzblfyid\r\n\t\t\t\t\t\t\t\t\t\tif (info.type != 'detail') {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.value = item[info.key] || '无'\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.label = item[info.key] || '无'\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\treturn info\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tresult.push(list)\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tthis.collapseData[0].data = result\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.collapseData.forEach((item) => {\r\n\t\t\t\t\t\t\t\t\titem.value = res.data[item.key] || '无'\r\n\t\t\t\t\t\t\t\t\t// 疫苗异常反应史\r\n\t\t\t\t\t\t\t\t\tif (item.key == 'adversecards') {\r\n\t\t\t\t\t\t\t\t\t\titem.value = ''\r\n\t\t\t\t\t\t\t\t\t\tlet result = []\r\n\t\t\t\t\t\t\t\t\t\tres.data[item.key].map((resItem) => {\r\n\t\t\t\t\t\t\t\t\t\t\titem.data.map(sitem => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tlet tempItem = JSON.parse(JSON\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.stringify(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsitem))\r\n\t\t\t\t\t\t\t\t\t\t\t\ttempItem.map(thirdItem => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthirdItem.value =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tresItem[\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthirdItem\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.key]\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\tresult.push(tempItem)\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\titem.data = result\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'pregnantWoman': // 孕产妇\r\n\t\t\t\t\t\t\tif (this.tabCur == 0 && this.serviceSData.key == 'pregnantWoman') {\r\n\t\t\t\t\t\t\t\tres.data.forEach(item => {\r\n\t\t\t\t\t\t\t\t\titem.inspections = '第一次产前检查'\r\n\t\t\t\t\t\t\t\t\titem.type = 'inspections'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tvar data = res.data.concat(...this.pregnantOtherInspections)\r\n\t\t\t\t\t\t\t\tdata.sort(function (a, b) {\r\n\t\t\t\t\t\t\t\t\treturn new Date(b.visitdate).getTime() - new Date(a\r\n\t\t\t\t\t\t\t\t\t\t.visitdate).getTime()\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tres.data = data\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\tcase 'tuberculosis': // 肺结核\r\n\t\t\t\t\t\t\tif (this.tabCur == 0 && this.serviceSData.key == 'tuberculosis') {\r\n\t\t\t\t\t\t\t\tres.data.map(item => {\r\n\t\t\t\t\t\t\t\t\titem.visit = '督导随访'\r\n\t\t\t\t\t\t\t\t\titem.type = 'visit'\r\n\t\t\t\t\t\t\t\t\tif (item.sfdycsf == 1) {\r\n\t\t\t\t\t\t\t\t\t\titem.sfdycsf = '第一次随访检查'\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\titem.sfdycsf = '其他次随访检查'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t// var data = null\r\n\t\t\t\t\t\t\t\t// if (this.tuberculosisVisit) {\r\n\t\t\t\t\t\t\t\t// \tdata = res.data.concat([this\r\n\t\t\t\t\t\t\t\t// \t\t.tuberculosisVisit\r\n\t\t\t\t\t\t\t\t// \t])\r\n\t\t\t\t\t\t\t\t// } else {\r\n\t\t\t\t\t\t\t\t// \tdata = res.data\r\n\t\t\t\t\t\t\t\t// }\r\n\r\n\t\t\t\t\t\t\t\tvar data = res.data\r\n\r\n\t\t\t\t\t\t\t\tdata.sort(function (a, b) {\r\n\t\t\t\t\t\t\t\t\treturn new Date(b.visitdate).getTime() - new Date(a\r\n\t\t\t\t\t\t\t\t\t\t.visitdate).getTime()\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tres.data = data\r\n\t\t\t\t\t\t\t\t// 加判断避免孕产妇用到\r\n\t\t\t\t\t\t\t} else if (this.tabCur == 1 && this.serviceSData.key == 'tuberculosis') {\r\n\t\t\t\t\t\t\t\tthis.tuberculosisVisit = res.data[0]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\tcase 'elderly': // 老年人\r\n\t\t\t\t\t\tcase 'diabetes': // 糖尿病\r\n\t\t\t\t\t\tcase 'hypertension': // 高血压\r\n\t\t\t\t\t\tcase 'childManage': // 儿童健康档案\r\n\t\t\t\t\t\t\tif (this.apiDataType == 'multiple') {\r\n\t\t\t\t\t\t\t\tres.data = res.data || []\r\n\t\t\t\t\t\t\t\tthis.requestData = res.data\r\n\t\t\t\t\t\t\t\tvar temData = this.collapseData[0].data\r\n\t\t\t\t\t\t\t\tconsole.log('LISTtemData',temData)\r\n\t\t\t\t\t\t\t\tvar result = []\r\n\t\t\t\t\t\t\t\tres.data.forEach((item, index) => {\r\n\t\t\t\t\t\t\t\t\tlet list = temData[0].map((subItem) => {\r\n\t\t\t\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(\r\n\t\t\t\t\t\t\t\t\t\t\tsubItem))\r\n\t\t\t\t\t\t\t\t\t\tinfo.businessId = item[this\r\n\t\t\t\t\t\t\t\t\t\t\t.serviceDataDetail\r\n\t\t\t\t\t\t\t\t\t\t\t.tabList[this.tabCur].businessId]\r\n\t\t\t\t\t\t\t\t\t\tif (info.type != 'detail') {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.value = item[info.key] || '无'\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.label = item[info.key] || '无'\r\n\t\t\t\t\t\t\t\t\t\t\tif (item.homevisitid) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinfo.homevisitid =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\titem.homevisitid\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (item.examinid) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinfo.examinid =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\titem.examinid\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (item.dmvisitid) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinfo.dmvisitid = item.dmvisitid\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (item.hypertensionvisitid) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinfo.hypertensionvisitid =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\titem.hypertensionvisitid\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (item.assessid) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinfo.assessid = item.assessid\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (item.healthcheckid) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinfo.healthcheckid = item\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.healthcheckid\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif (info.type == 'prenatal') {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.label = '产前检查'\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.value = item[info.key]\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif (!info.businessId) {\r\n\t\t\t\t\t\t\t\t\t\t\t// 很关键，下钻查询用 随访，体检共用\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.businessId = item.sfbh || item.tjjlid || item.pgbh || item.fwjlid\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\treturn info\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tresult.push(list)\r\n\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\tthis.collapseData[0].data = result\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.collapseData.map((item) => {\r\n\t\t\t\t\t\t\t\t\titem.value = res.data && res.data[item.key] ||\r\n\t\t\t\t\t\t\t\t\t\tthis.tuberculosisVisit[item.key] || '无'\r\n\t\t\t\t\t\t\t\t\titem.businessId = this.tuberculosisVisit['fjhhzsfbh'] || res.data.xsecszh    // 肺结核——全程管理在用， 儿童——出生医学证明再用\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t// 老版本：门诊-住院-体检同样的处理逻辑\r\n\t\t\t\t\t\tcase 'outpatientInfo': // 门诊信息\r\n\t\t\t\t\t\tcase 'hospitalInfor': // 住院信息\r\n\t\t\t\t\t\tcase 'physicalExamination': // 体检管理\r\n\t\t\t\t\t\t\tres.data = res.data || []\r\n\t\t\t\t\t\t\tthis.requestData = res.data\r\n\t\t\t\t\t\t\tvar temData = this.serviceDataDetail.data[0]\r\n\t\t\t\t\t\t\tvar result = []\r\n\t\t\t\t\t\t\tres.data.forEach((item) => {\r\n\t\t\t\t\t\t\t\tconsole.log('AAAitem',item,temData)\r\n\t\t\t\t\t\t\t\tlet list = temData.map((subItem) => {\r\n\t\t\t\t\t\t\t\t\t// console.log('BBBsubItem',subItem)\r\n\t\t\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(subItem))\r\n\t\t\t\t\t\t\t\t\tvar businessId = this.tabCur && item[this.serviceDataDetail.tabList[this.tabCur].businessId] || item.healthcheckid\r\n\t\t\t\t\t\t\t\t\tif (!this.tabList) {\r\n\t\t\t\t\t\t\t\t\t\tthis.businessName = 'ehr_jktjjl'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tinfo.businessId = item.tjjlid\r\n\t\t\t\t\t\t\t\t\tinfo.yljgdm = item.yljgdm\r\n\t\t\t\t\t\t\t\t\tinfo.tjjgdm = item.tjjgdm\r\n\t\t\t\t\t\t\t\t\tif (info.type != 'detail') {\r\n\t\t\t\t\t\t\t\t\t\tif (info.key != 'hisZyCyxj') {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.value = item[info.key] || '无'\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t// 出院诊断\r\n\t\t\t\t\t\t\t\t\t\t\tvar tempList = []\r\n\t\t\t\t\t\t\t\t\t\t\tif (item[info.key]) {\r\n\t\t\t\t\t\t\t\t\t\t\t\titem[info.key].map(tempItem => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (tempItem.cgzdmc) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttempList.push(tempItem.cgzdmc)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\tinfo.value = tempList && tempList.join(',') || '无'\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinfo.value = '无'\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tinfo.label = item[info.key] || ''\r\n\t\t\t\t\t\t\t\t\t\tif (item.healthcheckid) { // 体检管理\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.healthcheckid = item.healthcheckid\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.tjjgdm = item.tjjgdm\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif (item.bId) { // 住院信息\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.bId = item.bId\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\treturn info\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tresult.push(list)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.serviceDataDetail.data = result\r\n\t\t\t\t\t\t\tconsole.log('CCCresult',this.serviceDataDetail.data)\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'homeDoctor': // 家医预约\r\n\t\t\t\t\t\t\tres.data = res.data || {}\r\n\t\t\t\t\t\t\tthis.businessName = 'ehr_jtysqyjlxx'\r\n\t\t\t\t\t\t\tthis.serviceDataDetail.data[0].map((item) => {\r\n\t\t\t\t\t\t\t\tif (item.type != 'detail') {\r\n\t\t\t\t\t\t\t\t\titem.value = res.data[item.key] || '无'\r\n\t\t\t\t\t\t\t\t\titem.businessId = res.data.qyjlid\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\titem.label = res.data[item.key] || '无'\r\n\t\t\t\t\t\t\t\t\titem.label = res.data[item.key] && parseTime(res.data[item\r\n\t\t\t\t\t\t\t\t\t\t.key],\r\n\t\t\t\t\t\t\t\t\t\t\"{y}-{m}-{d}\") || '无'\r\n\t\t\t\t\t\t\t\t\titem.businessId = res.data.qyjlid\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'chineseMedicine': // 中医药\r\n\t\t\t\t\t\t\tres.data = res.data || []\r\n\t\t\t\t\t\t\tlet temData1 = this.collapseData[0].data\r\n\t\t\t\t\t\t\tlet result1 = []\r\n\t\t\t\t\t\t\tres.data.forEach((item) => {\r\n\t\t\t\t\t\t\t\tlet list = temData1[0].map((subItem) => {\r\n\t\t\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(subItem))\r\n\t\t\t\t\t\t\t\t\tinfo.value = item[info.key] || '无'\r\n\t\t\t\t\t\t\t\t\t// 业务主键\r\n\t\t\t\t\t\t\t\t\tinfo.businessId = item.fwjlid\r\n\t\t\t\t\t\t\t\t\treturn info\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tresult1.push(list)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.collapseData[0].data = result1\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\t// 包含基础信息的接口[糖尿病，高血压，肺结核，孕产妇]\r\n\t\t\t// 儿童的曲线数据不在这里查询\r\n\t\t\tif (['diabetes', 'hypertension', 'tuberculosis', 'pregnantWoman'].indexOf(this.serviceSData.key) >\r\n\t\t\t\t-1) {\r\n\t\t\t\t// 糖尿病基础信息\r\n\t\t\t\t// console.log(\"[糖尿病，高血压，肺结核，孕产妇]接口\", `${this.serviceSData.key}ApiBase`)\r\n\t\t\t\tapi[`${this.serviceSData.key}ApiBase`](params).then(res => {\r\n\t\t\t\t\tvar categories = res.data && res.data.rq && res.data.rq\r\n\t\t\t\t\t\t.map(\r\n\t\t\t\t\t\t\titem => {\r\n\t\t\t\t\t\t\t\treturn parseTime(item, \"{y}-{m}-{d}\")\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\tthis.serviceDataDetail.arrData && this.serviceDataDetail.arrData.map(item => {\r\n\t\t\t\t\t\titem.businessId2 = res.data.basicinfoid\r\n\t\t\t\t\t\t// 高血压，糖尿病——顶部基本信息，第一行的下钻，需要带上业务id\r\n\t\t\t\t\t\titem.businessId = res.data.tnbhzdjbh || res.data.gxyhzdjbh || res.data.fjhhzsfbh\r\n\t\t\t\t\t\tif (item.key == 'nextPhysicalDate') {\r\n\t\t\t\t\t\t\t// 后端直接返回\r\n\t\t\t\t\t\t\t// var now = new Date(res.data['nextCheckDate']);\r\n\t\t\t\t\t\t\t// var duedate = new Date(now);\r\n\t\t\t\t\t\t\t// duedate.setDate(now.getDate() + 365);\r\n\t\t\t\t\t\t\t// item.value = parseTime(duedate)\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\titem.value = res.data[item.key] || '无'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tswitch (this.serviceSData.key) {\r\n\t\t\t\t\t\tcase 'pregnantWoman':\r\n\t\t\t\t\t\t\tthis.gestationalWeeks = res.data\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\tcase 'diabetes': // 糖尿病\r\n\t\t\t\t\t\t\tthis.bsugarMg = res.data.zjyckfxtz\r\n\t\t\t\t\t\t\tdiabetesData.categories = categories\r\n\t\t\t\t\t\t\tdiabetesData.series[0].data = res.data.kfxtz\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\tcase 'hypertension': // 高血压\r\n\t\t\t\t\t\t\tthis.bsugarMg = res.data.zjycssy  // 收缩压\r\n\t\t\t\t\t\t\tthis.dbp = res.data.zjycszy\r\n\t\t\t\t\t\t\thypertensionData.categories = categories\r\n\t\t\t\t\t\t\thypertensionData.series[0].data = res.data.ssy\r\n\t\t\t\t\t\t\thypertensionData.series[1].data = res.data.szy\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\tif (['vaccination'].indexOf(this.serviceSData.key) > -1 && this.tabCur == 1) {\r\n\t\t\t\tapi[`${this.serviceSData.key}ApiBase`](params).then(res => {\r\n\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\tthis.collapseData[1].value = res.data.jzjj\r\n\t\t\t\t\t\tthis.collapseData[1].businessId = res.data.yfjzkid\r\n\t\t\t\t\t\tthis.collapseData[2].value = res.data.crbs\r\n\t\t\t\t\t\tthis.collapseData[2].businessId = res.data.yfjzkid\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// console.log(this.collapseData, 'this.collapseData===')\r\n\t\t},\r\n\t\tcardNoFilter(value) {\r\n\t\t\tif (value == null || value == '' || value == undefined) {\r\n\t\t\t\treturn '';\r\n\t\t\t} else {\r\n\t\t\t\tlet x = ''\r\n\t\t\t\tfor (let i = 0; i < (value.length - 6); i++) {\r\n\t\t\t\t\tx = x + '*'\r\n\t\t\t\t}\r\n\t\t\t\treturn value.substr(0, 3) + x + value.substr(15, 3)\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetDecryptData(item) {\r\n\t\t\tif (item.encryptData) {\r\n\t\t\t\titem.encryptData = ''\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tapi.fetchDataDecrypt({\r\n\t\t\t\tdata: item.encrypt\r\n\t\t\t}).then(res => {\r\n\t\t\t\titem.encryptData = res.data || ''\r\n\t\t\t\tthis.$forceUpdate()\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 查询门诊模块 [手术，输血没数据，未核对]  \r\n\t\tasync queryAll_mjxx(key,yljgdm) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.mjzjzjl_info({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.mjzjzjl_cfmx({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.mjzjzjl_zdjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.mjzjzjl_jyjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.mjzjzjl_jcjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.mjzjzjl_mzss({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\t// api.mjzjzjl_sxjl({ jzlsh: key })  // 门诊的输血的不查了去掉了  ——地下res[6]也要去掉\r\n\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tconsole.log('GGGGG', res)\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data, yljgdm: yljgdm}]\r\n\t\t\t\t\t\t// this.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[1].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisMzCfmx = res[1].data\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[2].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisMzSysjcmxjlzd = res[2].data\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[3].code == 200) { //检验记录\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisMzSysjcmxjl = res[3].data\r\n\t\t\t\t\t\tthis.infoData[0].hisMzSysjcmxjl.forEach(item => {\r\n\t\t\t\t\t\t  item.yljgdm = yljgdm\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[4].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisMzYxjl = res[4].data\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[5].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisMzOperation = res[5].data\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// if (res[6].code == 200) {\r\n\t\t\t\t// \tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t// \t\tthis.infoData[0].hisMzSxjl = res[6].data\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t\tconsole.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 查询住院模块[诊断，手术，输血没数据，未核对]\r\n\t\tasync queryAll_zyxx(key,yljgdm) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.zybasy_info({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.zybasy_yzxx({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.zybasy_zdjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.zybasy_jyjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.zybasy_jcjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.zybasy_ssjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\tapi.zybasy_sxjl({ jzlsh: key, yljgdm: yljgdm  })\r\n\r\n\t\t\t\t// api.zybasy_info({ jzlsh: '211668' }),\r\n\t\t\t\t// api.zybasy_yzxx({ jzlsh: '211668' }),\r\n\t\t\t\t// api.zybasy_zdjl({ jzlsh: '211668' }), // ————无数据\r\n\t\t\t\t// api.zybasy_jyjl({ jzlsh: '211668' }),\r\n\t\t\t\t// api.zybasy_jcjl({ jzlsh: '211668' }),\r\n\t\t\t\t// api.zybasy_ssjl({ jzlsh: '211668' }), // ————无数据\r\n\t\t\t\t// api.zybasy_sxjl({ jzlsh: '211668' })  // ————无数据\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data, yljgdm: yljgdm }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[1].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].zymkYzmx = res[1].data  // 医嘱\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[2].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisZyCyxj = res[2].data  // 诊断\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[3].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisZyYzmx = res[3].data  // 检验\r\n\t\t\t\t\t\tthis.infoData[0].hisZyYzmx.forEach(item => {\r\n\t\t\t\t\t\t  item.yljgdm = yljgdm\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[4].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisZyOperation = res[4].data  // 检查\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[5].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].outHos = res[5].data  // 手术\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res[6].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0].hisZySxjl = res[6].data  // 输血\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 查询糖尿病——随访下钻\r\n\t\tasync queryAll_tnb_sf(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.diabetesApi0_info({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 查询高血压——随访下钻\r\n\t\tasync queryAll_gxy_sf(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.hypertensionApi0_info({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 查询——体检下钻【糖尿病。高血压，体检模块】公卫体检\r\n\t\tasync queryAll_tj_info(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.physicalExaminationApi0_info({ tjjlid: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 查询——体检【体检模块】医院体检 \r\n\t\tasync queryAll_tj_yyinfo(key,tjjgdm) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.yytjjl_info({ tjjlid: key, tjjgdm: tjjgdm, bglb: '1' }),\r\n\t\t\t\tapi.yytjjl_info({ tjjlid: key, tjjgdm: tjjgdm, bglb: '3' }),\r\n\t\t\t\tapi.yytjjl_info({ tjjlid: key, tjjgdm: tjjgdm, bglb: '2' }),\r\n\t\t\t\t// api.mjzjzjl_jyjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\t// api.mjzjzjl_jcjl({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\t// api.mjzjzjl_mzss({ jzlsh: key, yljgdm: yljgdm  }),\r\n\t\t\t\t// api.mjzjzjl_sxjl({ jzlsh: key })  // 门诊的输血的不查了去掉了  ——地下res[6]也要去掉\r\n\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tconsole.log('GGGGG?>>>>>>>>>>>>>', res,'this.infoData',this.infoData)\r\n\t\t\t\t// if (res[0].code == 200) {\r\n\t\t\t\t// \tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t// \t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t// \t} else {\r\n\t\t\t\t// \t\tthis.infoData = [{ ...res[0].data, tjjgdm: tjjgdm}]\r\n\t\t\t\t// \t\t// this.infoData = [{ ...res[0].data }]\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\t// this.infoData[0].infoData = res[0].data\r\n\t\t\t\t\tthis.yytjList[0].infoData = res[0].data\r\n\t\t\t\t\tthis.yytjList[0].infoData.forEach(item => {\r\n\t\t\t\t\t  item.tjjgdm = tjjgdm\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (res[1].code == 200) {\r\n\t\t\t\t\t// this.infoData[0].yxjc = res[1].data\r\n\t\t\t\t\tthis.yytjList[0].yxjc = res[1].data\r\n\t\t\t\t\tthis.yytjList[0].yxjc.forEach(item => {\r\n\t\t\t\t\t  item.tjjgdm = tjjgdm\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (res[2].code == 200) { //检验记录\r\n\t\t\t\t\t// this.infoData[0].jyxm = res[2].data\r\n\t\t\t\t\tthis.yytjList[0].jyxm = res[2].data\r\n\t\t\t\t\tthis.yytjList[0].jyxm.forEach(item => {\r\n\t\t\t\t\t  item.tjjgdm = tjjgdm\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// if (res[0].code == 200) {\r\n\t\t\t\t// \tif (this.infoData) {\r\n\t\t\t\t// \t\tthis.infoData[0].hisMzCfmx = res[0].data\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t\t// if (res[1].code == 200) {\r\n\t\t\t\t// \tif (this.infoData) {\r\n\t\t\t\t// \t\tthis.infoData[0].hisMzSysjcmxjlzd = res[1].data\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t\t// if (res[2].code == 200) { //检验记录\r\n\t\t\t\t// \tif (this.infoData) {\r\n\t\t\t\t// \t\tthis.infoData[0].hisMzSysjcmxjl = res[2].data\r\n\t\t\t\t// \t\tthis.infoData[0].hisMzSysjcmxjl.forEach(item => {\r\n\t\t\t\t// \t\t  item.tjjgdm = tjjgdm\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t\tthis.infoData = this.yytjList\r\n\t\t\t\tconsole.log(\"组装this.yytjList\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\t\tasync queryAll_fjh_sf(key, pIdx) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.tuberculosisApi0_info({ sfbh: key }),\r\n\t\t\t\t// api.tuberculosisApi0_info({ sfbh: 'FJHSF_010611000000202105110003' }), \r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\t// this.infoData = [{ ...res[0].data, visit: '督导随访', type: 'visit' }]\r\n\t\t\t\t\t// this.infoData = [{ ...this.tuberculosisVisit }, { ...res[0].data }]\r\n\t\t\t\t\tif (pIdx == 0) {\r\n\t\t\t\t\t\tres[0].data.sfdycsf = 0\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tres[0].data.sfdycsf = 1\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.infoData = [{ ...res[0].data }, { ...this.tuberculosisVisit }]\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 家医签约-履约\r\n\t\tasync queryAll_jyqy_ly(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.homeDoctorDetailApi0({ qyjlid: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tthis.infoData = [].concat(res[0].data)\r\n\t\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 查询老年人——自理能力评估下钻  0242ac190002\r\n\t\tasync queryAll_lnr_zlnl(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.elderlyApi0_info({ pgbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\tasync queryAll_et_sf(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.childManageApi0_info({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\t// console.log(`res[0].data`, res[0].data,this.infoData)\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = res[0].data\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"儿童随访组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\tasync queryAll_et_tj(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.childManageApi1_info({ tjbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 妇女\r\n\t\t// 产前——第一次\r\n\t\tasync queryAll_fv_cq0(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.pregnantWomanApi0_1({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tres[0].data.inspections = '第一次产前检查详情'\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 产前——其他次\r\n\t\tasync queryAll_fv_cq00(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.pregnantWomanApi00_1({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tres[0].data.inspections = '其他次产前检查详情'\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\t\tasync queryAll_fv_fm(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.pregnantWomanApi1_1({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\tasync queryAll_fv_xse(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.pregnantWomanApi2_1({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\tasync queryAll_fv_fs(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.pregnantWomanApi3_1({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\tasync queryAll_fv_42(key) {\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tapi.pregnantWomanApi4_1({ sfbh: key }),\r\n\t\t\t]).then((res) => {\r\n\t\t\t\tif (res[0].code == 200) {\r\n\t\t\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t\t\tthis.infoData[0] = Object.assign(this.infoData[0], res[0].data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.infoData = [{ ...res[0].data }]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"组装this.infoData\", this.infoData)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// item 是 sitem的一个属性, sitem 是itemList数字的子项, pIdx是sitem在itemList的索引, sIdx是item在sitem的索引  sitem.label, '', sitem, item, '', index\r\n\t\tasync goDetail(item, type = '', sitem, itemList, pIdx, sIdx) {\r\n\t\t\tlet _that = this\r\n\t\t\tconsole.log(\"goDetail\", 'label:'+item, 'type: ' + type, 'sitem:',sitem, 'itemList:',itemList, pIdx, sIdx)\r\n\t\t\tvar data = item\r\n\t\t\tvar serviceData = JSON.stringify(this.serviceSData)\r\n\t\t\tconsole.log(\"serviceData\", serviceData)\r\n\t\t\t// 处理页面数据，需要传到详情页面的数据\r\n\t\t\tthis.infoData = null\r\n\r\n\t\t\tswitch (this.serviceSData.key) {\r\n\t\t\t\t// 住院信息\r\n\t\t\t\tcase 'hospitalInfor':\r\n\t\t\t\tcase 'physicalExamination':\r\n\t\t\t\tcase 'pregnantWoman':\r\n\t\t\t\tcase 'childManage':\r\n\t\t\t\tcase 'elderly':\r\n\t\t\t\tcase 'diabetes':\r\n\t\t\t\tcase 'hypertension':\r\n\t\t\t\tcase 'tuberculosis':\r\n\t\t\t\t// 门诊信息\r\n\t\t\t\tcase 'outpatientInfo':\r\n\t\t\t\t\tthis.infoData = this.requestData.filter((item) => {\r\n\t\t\t\t\t\t// console.log(\"MZXXitem\", item)\r\n\t\t\t\t\t\treturn item.homevisitid && item.homevisitid == sitem.homevisitid ||\r\n\t\t\t\t\t\t\titem.examinid && item.examinid == sitem.examinid ||\r\n\t\t\t\t\t\t\titem.dmvisitid && item.dmvisitid == sitem.dmvisitid ||\r\n\t\t\t\t\t\t\titem.healthcheckid && item.healthcheckid == sitem.healthcheckid ||\r\n\t\t\t\t\t\t\titem.hypertensionvisitid && item.hypertensionvisitid == sitem\r\n\t\t\t\t\t\t\t\t.hypertensionvisitid ||\r\n\t\t\t\t\t\t\titem.assessid && item.assessid == sitem.assessid ||\r\n\t\t\t\t\t\t\titem.healthcheckid && item.healthcheckid == sitem.healthcheckid ||\r\n\t\t\t\t\t\t\titem.bId && item.bId == sitem.bId\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconsole.log(\"MZXXthis.infoData\", this.infoData)\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\t// console.log(\"this.serviceSData.key\", this.serviceSData.key)\r\n\t\t\t// 门诊信息\r\n\t\t\tif (this.serviceSData.key == 'outpatientInfo') {\r\n\t\t\t\tawait this.queryAll_mjxx(itemList[1].value,itemList[1].yljgdm || '')\r\n\t\t\t\t// this.infoData[0].yljgdm = sitem.yljgdm || '' //用于第三层明细接口传值\r\n\t\t\t}\r\n\t\t\t// 住院信息\r\n\t\t\tif (this.serviceSData.key == 'hospitalInfor') {\r\n\t\t\t\tawait this.queryAll_zyxx(itemList[1].value,itemList[1].yljgdm || '')\r\n\t\t\t\t// this.infoData[0].yljgdm = sitem.yljgdm || '' //用于第三层明细接口传值\r\n\t\t\t}\r\n\t\t\t// 糖尿病\r\n\t\t\tif (this.serviceSData.key == 'diabetes') {\r\n\t\t\t\tif (this.tabCur == 1) {\r\n\t\t\t\t\tawait this.queryAll_tj_info(sitem.businessId)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tawait this.queryAll_tnb_sf(sitem.businessId)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 高血压\r\n\t\t\tif (this.serviceSData.key == 'hypertension') {\r\n\t\t\t\tif (this.tabCur == 1) {\r\n\t\t\t\t\tawait this.queryAll_tj_info(sitem.businessId)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tawait this.queryAll_gxy_sf(sitem.businessId)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 体检模块\r\n\t\t\tif (this.serviceSData.key == 'physicalExamination' && itemList[5].value == \"8\") { //医院体检\r\n\t\t\t\tawait this.queryAll_tj_yyinfo(sitem.businessId,sitem.tjjgdm || '',)\r\n\t\t\t}\r\n\t\t\tif (this.serviceSData.key == 'physicalExamination' && itemList[5].value !== \"8\") { //公卫体检\r\n\t\t\t\tawait this.queryAll_tj_info(sitem.businessId)\r\n\t\t\t}\r\n\t\t\t// 肺结核\r\n\t\t\tif (this.serviceSData.key == 'tuberculosis') {\r\n\t\t\t\tif (this.tabCur == 0) {\r\n\t\t\t\t\t// 是否第一次\r\n\t\t\t\t\tawait this.queryAll_fjh_sf(sitem.businessId, pIdx)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 家医签约——履约记录\r\n\t\t\tif (this.serviceSData.key == 'homeDoctor') {\r\n\t\t\t\tif (this.tabCur == 0) {\r\n\t\t\t\t\tlet tmpArr = this.serviceDataDetail.data[0].filter(item => item.key == 'qyjlid')\r\n\t\t\t\t\tawait this.queryAll_jyqy_ly(tmpArr[0].value)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 老年人\r\n\t\t\tif (this.serviceSData.key == 'elderly') {\r\n\t\t\t\tif (this.tabCur == 2) {\r\n\t\t\t\t\tawait this.queryAll_tj_info(sitem.businessId)\r\n\t\t\t\t} else if (this.tabCur == 0) {\r\n\t\t\t\t\tawait this.queryAll_lnr_zlnl(sitem.businessId)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 儿童\r\n\t\t\tif (this.serviceSData.key == 'childManage') {\r\n\t\t\t\tif (this.tabCur == 0) {\r\n\t\t\t\t\tawait this.queryAll_et_sf(sitem.businessId)\r\n\t\t\t\t} else if (this.tabCur == 1) {\r\n\t\t\t\t\tawait this.queryAll_et_tj(sitem.businessId)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 妇女——————因为2-3屏蔽，456前移，真实调试\r\n\t\t\tif (this.serviceSData.key == 'pregnantWoman') {\r\n\t\t\t\tif (this.tabCur == 0) {\r\n\t\t\t\t\t// 需要判断属于什么类型，然后调用不同的接口\r\n\t\t\t\t\tlet type = itemList.data[pIdx][1].value\r\n\t\t\t\t\tconsole.log(\"产检类型\", type)\r\n\t\t\t\t\tif (type == '第一次产前检查') {\r\n\t\t\t\t\t\tawait this.queryAll_fv_cq0(sitem.businessId)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tawait this.queryAll_fv_cq00(sitem.businessId)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (this.tabCur == 1) {\r\n\t\t\t\t\tawait this.queryAll_fv_fm(sitem.businessId)\r\n\t\t\t\t} else if (this.tabCur == 2) {\r\n\t\t\t\t\tawait this.queryAll_fv_xse(sitem.businessId) // 7695c5ce-6a6c-11ef-8a73-0242ac190002\r\n\t\t\t\t} else if (this.tabCur == 3) {\r\n\t\t\t\t\tawait this.queryAll_fv_fs(sitem.businessId)\r\n\t\t\t\t} else if (this.tabCur == 4) {\r\n\t\t\t\t\tawait this.queryAll_fv_42(sitem.businessId)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log(this.infoData, 'infoData')\r\n\t\t\tthis.infoData = encodeURIComponent(JSON.stringify(this.infoData))\r\n\t\t\t// console.log(this.infoData, 'infoData222')\r\n\t\t\tvar familyItem = JSON.stringify(this.familyItem)\r\n\t\t\t// if(itemList[5]?.value && itemList[5]?.value == \"8\"){\r\n\t\t\t// \tuni.navigateTo({\r\n\t\t\t// \t\turl: `/packagePages/home/<USER>\n\t\t\t// \t})\r\n\t\t\t// }\r\n\t\t\tif (type == '') {\r\n\t\t\t\tconsole.log(\"tab空——无tabData\", this.serviceSData.key)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/home/<USER>\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log(\"tab存在——有tabData\", this.serviceSData.key)\r\n\t\t\t\tvar tabData = JSON.stringify(this.serviceDataDetail.tabList[this.tabCur])\r\n\t\t\t\t// console.log(\"this.infoData\", this.infoData)\r\n\t\t\t\t// console.log(\"tabData\", tabData)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/home/<USER>\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetChartsData() {\r\n\t\t\t// console.log(\"menuIndex: chart\", this.menuIndex)\r\n\t\t\tlet params = null\r\n\t\t\tif (this.menuIndex > 0) {\r\n\t\t\t\tparams = {\r\n\t\t\t\t\tcxjmjkdabh: this.familyItem?.archiveId,\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tparams = {\r\n\t\t\t\t\tcxjmjkdabh: uni.getStorageSync('archiveId')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// params.cxjmjkdabh = '15080200900700483'  // 覆盖 儿童——健康检查\r\n\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\t// 高血压，糖尿病\r\n\t\t\t\tlet res = {\r\n\t\t\t\t\tcategories: this.serviceSData.key == 'diabetes' ? diabetesData.categories :\r\n\t\t\t\t\t\thypertensionData.categories,\r\n\t\t\t\t\tseries: this.serviceSData.key == 'diabetes' ? diabetesData.series : hypertensionData\r\n\t\t\t\t\t\t.series\r\n\t\t\t\t};\r\n\t\t\t\tthis.chartData = JSON.parse(JSON.stringify(res));\r\n\r\n\t\t\t\t// 儿童健康档案\r\n\t\t\t\tlet currentGender = this.userInfo.sex\r\n\t\t\t\tvar childHeightData = {},\r\n\t\t\t\t\tchildWeightData = {}\r\n\t\t\t\tif (this.childCurIndex == 0) {\r\n\t\t\t\t\tcurrentGender == '1' ? childHeightData = childHeightBoyData : childHeightData =\r\n\t\t\t\t\t\tchildHeightGirlData\r\n\t\t\t\t} else {\r\n\t\t\t\t\tcurrentGender == '1' ? childWeightData = childWeightBoyData : childWeightData =\r\n\t\t\t\t\t\tchildWeightGirlData\r\n\t\t\t\t}\r\n\t\t\t\tlet childRes = {\r\n\t\t\t\t\tcategories: this.childCurIndex == 0 ? childHeightData.categories : childWeightData\r\n\t\t\t\t\t\t.categories,\r\n\t\t\t\t\tseries: this.childCurIndex == 0 ? childHeightData.series : childWeightData\r\n\t\t\t\t\t\t.series\r\n\t\t\t\t};\r\n\t\t\t\tlet moonage = childHeightData.moonage || childWeightData.moonage\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tname: this.childCurIndex == 0 ? '身高' : '体重',\r\n\t\t\t\t\ttype: 'point',\r\n\t\t\t\t\tdata: new Array(44)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.serviceSData.key == 'childManage') {\r\n\t\t\t\t\tapi.childManageApi1(params).then((res) => {\r\n\t\t\t\t\t\tres.data = res.data || []\r\n\t\t\t\t\t\tres.data.forEach((item) => {\r\n\t\t\t\t\t\t\tif (this.childCurIndex == 0 && moonage.indexOf(Number(item\r\n\t\t\t\t\t\t\t\t.moonage)) != -\r\n\t\t\t\t\t\t\t\t1) {\r\n\t\t\t\t\t\t\t\tdata.data[moonage.indexOf(Number(item.moonage))] = item.height\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (this.childCurIndex == 1 && moonage.indexOf(Number(item\r\n\t\t\t\t\t\t\t\t.moonage)) != -\r\n\t\t\t\t\t\t\t\t1) {\r\n\t\t\t\t\t\t\t\tdata.data[moonage.indexOf(Number(item.moonage))] = item.weight\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tchildRes.series[7] = data\r\n\t\t\t\t\t\tthis.childChartData = JSON.parse(JSON.stringify(childRes));\r\n\t\t\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t}, 300);\r\n\t\t},\r\n\t\thandleChangePanel(item) {\r\n\t\t\tthis.isOpen = !this.isOpen\r\n\t\t\titem.isOpen = !item.isOpen\r\n\t\t\tthis.collapseData.map(data => {\r\n\t\t\t\tif (data.label != item.label) {\r\n\t\t\t\t\tdata.isOpen = false\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t// this.scrollTop()\r\n\t\t},\r\n\t\tscrollTop() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.$nextTick(function () {\r\n\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\tduration: 0,\r\n\t\t\t\t\t\tselector: \".tabs\" //滚动到的元素的id\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t}, 50)\r\n\t\t},\r\n\t\thandleMenuChange(index, item) {\r\n\t\t\tthis.tabCur = index\r\n\t\t\tthis.apiDataType = item.type\r\n\t\t\tthis.collapseData = this.serviceDataDetail.data[this.serviceDataDetail.tabList[index].value]\r\n\t\t\tthis.getData()\r\n\t\t},\r\n\t\thandleChildMenuChange(index) {\r\n\t\t\tthis.childCurIndex = index\r\n\t\t\tthis.getChartsData();\r\n\t\t},\r\n\t\t// 错误数据反馈———糖尿病，高血压 基本信息———体检列表用【糖尿病，高血压。。】\r\n\t\thandleLongPress(item, type) {\r\n\t\t\tif (!this.$allowFeedback) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif (item.value == '--') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// !item.value || item.value == '无' ||\r\n\t\t\tif (item.type == 'detail') return\r\n\t\t\tthis.abnormalDataType = type\r\n\t\t\tthis.businessId = item.businessId || item.businessId2\r\n\t\t\tthis.columnName = item.key || this.businessName\r\n\t\t\tthis.archiveid = item.archiveid\r\n\t\t\tthis.modalData.info = item.label2 ? `${item.label2} : ${item.label}` : `${item.label} : ${item.value}`\r\n\r\n\t\t\t// 老年人——中医药\r\n\t\t\tif (this.serviceSData.key == 'elderly' && item.key == 'ehr_lnrzyyjkgl') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// 门诊-住院不允许错误数据反馈\r\n\t\t\tif (['outpatientInfo', 'hospitalInfor', 'chineseMedicine'].indexOf(this.serviceSData.key) == -1) {\r\n\t\t\t\tthis.modalVisible = true\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 多选项的长按—— 档案摘要在用\r\n\t\thandleLongPressMul(pItem, pIdx, item, idx) {\r\n\t\t\tif (!this.$allowFeedback) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif (item.value == '--') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.abnormalDataType = ''\r\n\t\t\tthis.businessId = pItem[0].value\r\n\t\t\tthis.archiveid = pItem[0].archiveid\r\n\t\t\tthis.businessName = this.collapseData[idx]?.key || this.businessName  //兜底勿删\r\n\t\t\tthis.columnName = item.key\r\n\t\t\tthis.modalData.info = `${item.label} : ${item.value}`\r\n\r\n\t\t\t// 老年人——中医药\r\n\t\t\tif (this.serviceSData.key == 'elderly' && item.key == 'ehr_lnrzyyjkgl') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// 门诊-住院不允许错误数据反馈\r\n\t\t\tif (['outpatientInfo', 'hospitalInfor', 'chineseMedicine'].indexOf(this.serviceSData.key) == -1) {\r\n\t\t\t\tthis.modalVisible = true\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 多选项的长按—— 糖尿病-高血压，。。。  档案摘要也在这里\r\n\t\thandleLongPressMul2(sitem, sindex, collapseItem, collapseIndex, item, index) {\r\n\t\t\tif (!this.$allowFeedback) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif (sitem.value == '--') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.abnormalDataType = ''\r\n\t\t\tif (this.serviceSData.key == 'fileSummary') {\r\n\t\t\t\tthis.businessId = collapseItem[0].value\r\n\t\t\t\tthis.businessName = item.key\r\n\r\n\t\t\t} else {\r\n\t\t\t\tthis.businessId = sitem.businessId\r\n\t\t\t\tthis.businessName = this.serviceDataDetail['tabList'][this.tabCur]?.businessName\r\n\t\t\t}\r\n\r\n\t\t\t// 特殊处理——第一次产前检查\r\n\t\t\tif (this.serviceSData.key == 'pregnantWoman' && collapseIndex == 0) {\r\n\t\t\t\tthis.businessName = this.serviceDataDetail['tabList'][this.tabCur]?.businessName2\r\n\t\t\t}\r\n\r\n\t\t\tthis.columnName = sitem.key\r\n\t\t\tthis.modalData.info = `${collapseItem[sindex].label} : ${collapseItem[sindex].value}`\r\n\r\n\t\t\t// 老年人——中医药\r\n\t\t\tif (this.serviceSData.key == 'elderly' && item.key == 'ehr_lnrzyyjkgl') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// 门诊-住院不允许错误数据反馈\r\n\t\t\tif (['outpatientInfo', 'hospitalInfor', 'chineseMedicine'].indexOf(this.serviceSData.key) == -1) {\r\n\t\t\t\tthis.modalVisible = true\r\n\t\t\t}\r\n\t\t},\r\n\t\thandleCancel() {\r\n\t\t\tthis.modalVisible = false\r\n\t\t},\r\n\t\thandleConfirm(modalData) {\r\n\t\t\tif (!modalData[0].value) {\r\n\t\t\t\ttoast('请输入异常原因')\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tuni.showLoading({})\r\n\t\t\tthis.modalVisible = false;\r\n\t\t\tvar businessName = ''\r\n\t\t\tif (this.businessName == 'ehr_dyccqsffw') {\r\n\t\t\t\tbusinessName = this.businessName\r\n\t\t\t} else {\r\n\t\t\t\tbusinessName = this.serviceDataDetail.tabList && this.serviceDataDetail.tabList[this.tabCur]\r\n\t\t\t\t\t.businessName || this.businessName\r\n\t\t\t}\r\n\t\t\t// 这个文件只处理首页且是top的场景，详情和下转不在此页面不用过度判断\r\n\t\t\tif (this.abnormalDataType == 'top') {\r\n\t\t\t\tif (this.serviceSData.key == 'diabetes') {\r\n\t\t\t\t\tbusinessName = 'ehr_tnbhzjbxxdj'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.serviceSData.key == 'hypertension') {\r\n\t\t\t\t\tbusinessName = 'ehr_gxyhzjbxxdj'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.serviceSData.key == 'tuberculosis') {\r\n\t\t\t\t\tbusinessName = 'ehr_fjhhzsfxx'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 特殊处理下 疫苗接种不良反应外面的接种卡2个字段\r\n\t\t\tif (this.serviceSData.key == 'vaccination' && ['crbs', 'jzjj'].includes(this.columnName)) {\r\n\t\t\t\tbusinessName = 'ehr_yfjzkxx'\r\n\t\t\t}\r\n\r\n\t\t\tvar topicCode = this.serviceSData.topicCode\r\n\t\t\tif (businessName == 'ehr_jktjjl') {\r\n\t\t\t\ttopicCode = '11'\r\n\t\t\t}\r\n\t\t\tif (businessName == 'ehr_lnrzyyjkgl') {\r\n\t\t\t\ttopicCode = '09'\r\n\t\t\t}\r\n\r\n\t\t\tlet params = {\r\n\t\t\t\ttopicCode: topicCode,\r\n\t\t\t\tareaCode: this.familyItem.basicAreaCode || this.familyItem.areaCode,\r\n\t\t\t\tbusinessName: businessName,  // 数据库表名称\r\n\t\t\t\tcolumnName: this.columnName,\t // 更正字段\r\n\t\t\t\tbusinessId: this.businessId,   // 序号xh   ？？？  主要是这个字段\r\n\t\t\t\tarchiveid: this.archiveid,\r\n\t\t\t\tidCard: this.familyItem.idCardDecrypt || this.familyItem.idCard,\r\n\t\t\t\tbId: null,\r\n\t\t\t\tsourceType: 3,\r\n\t\t\t\treason: modalData[0].value,\r\n\t\t\t\tamendData: modalData[1].value,\r\n\t\t\t}\r\n\t\t\tapi.serviceFeedback(params).then(res => {\r\n\t\t\t\ttoast('反馈提交成功', 2000)\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t}).catch(e => {\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\tthis.modalData.formData.map(item => {\r\n\t\t\t\t\titem.info = ''\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\tthis.modalData.formData.map(item => {\r\n\t\t\t\titem.info = ''\r\n\t\t\t})\r\n\t\t},\r\n\t\tgoRule() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/packagePages/home/<USER>'\r\n\t\t\t})\r\n\t\t},\r\n\t\tpageHide() {\r\n\t\t\tif (this.serviceSData.key == 'fileSummary') {\r\n\t\t\t\tthis.collapseData.map(item => {\r\n\t\t\t\t\tif (item.key == 'medicareid') {\r\n\t\t\t\t\t\titem.encryptData = ''\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (this.moduleCode) {\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: `/packagePages/my/messageNotification`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.$forceUpdate()\r\n\t\t},\r\n\t}\r\n};\r\n</script>\r\n<style>\r\npage {\r\n\tbackground-color: #F5F6F7 !important;\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.main-box {\r\n\tpadding: 16px;\r\n\tpadding-bottom: 70px;\r\n}\r\n\r\n.tabs {\r\n\tborder-bottom: 1px solid #e7e7e7;\r\n\tposition: relative;\r\n\r\n\t.icon-shuoming {\r\n\t\tposition: absolute;\r\n\t\tright: 10px;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\tcolor: rgba(0, 0, 0, 0.6)\r\n\t}\r\n}\r\n\r\n.tabs-scroll {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex-wrap: nowrap;\r\n\tbox-sizing: border-box;\r\n\r\n\t.tabs-scroll_item {\r\n\t\theight: 48px;\r\n\t\tline-height: 48px;\r\n\t\t// margin-right: -10px;\r\n\t\tflex-shrink: 0;\r\n\t\tpadding-bottom: 10px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 14px;\r\n\t\tpadding: 0 16px;\r\n\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t}\r\n\r\n\t.tab-active {\r\n\t\tposition: relative;\r\n\t\tcolor: #0052D9 !important;\r\n\r\n\t\t&::after {\r\n\t\t\tcontent: \"\";\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 16px;\r\n\t\t\theight: 3px;\r\n\t\t\tbackground: #0052d9;\r\n\t\t\tborder-radius: 999px;\r\n\t\t\tleft: 50%;\r\n\t\t\tbottom: 0px;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.hint-label {\r\n\tmargin-bottom: 10px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tcolor: #666;\r\n\r\n\t.icon-tishi {\r\n\t\tfont-size: 20px;\r\n\t\tcolor: #EF7029;\r\n\t}\r\n\r\n\t.icon-dianji {\r\n\t\tfont-size: 14px;\r\n\t\tmargin: 0 3px;\r\n\t\tcolor: #EF7029;\r\n\t}\r\n}\r\n\r\n.list-box {\r\n\t.list-item-box2 {\r\n\t\tbackground-color: #EF7029 !important;\r\n\r\n\t\t.list-cell {\r\n\t\t\t.list-lable {\r\n\t\t\t\tfont-size: 14px !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\tfont-weight: 4 00 !important;\r\n\t\t\t}\r\n\r\n\t\t\t.text-right {\r\n\t\t\t\tfont-size: 17px !important;\r\n\t\t\t\tfont-weight: 600 !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\topacity: 1 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.list-item-box {\r\n\t\tmargin-bottom: 17px;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12px;\r\n\t\tmargin-bottom: 17px;\r\n\t\t.list-lableAI{\r\n\t\t\tdisplay: inline-flex;\r\n\t\t\talign-items: center;\r\n\t\t\tgap: 6px;\r\n\t\t\tpadding: 15px 0px 0px 15px;\r\n\t\t\timage{\r\n\t\t\t\twidth: 24px;\r\n\t\t\t\theight: 24px;\r\n\t\t\t\tmargin-right: 8px;\r\n\t\t\t}\r\n\t\t\t.ai-label{\r\n\t\t\t\tcolor: #1C6CED !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.list-content-item {\r\n\t\tpadding: 0 16px;\r\n\t\t\r\n\t}\r\n\r\n\t.list-item2 {\r\n\t\t.list-cell {\r\n\t\t\t.list-lable {\r\n\t\t\t\tcolor: #1C6CED !important;\r\n\t\t\t}\r\n\r\n\t\t\t.text-right {\r\n\t\t\t\tcolor: #333 !important;\r\n\t\t\t}\r\n\r\n\t\t\t.text-right-btn {\r\n\t\t\t\tcolor: #1C6CED !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.list-item {\r\n\t\tpadding: 16px 0;\r\n\t\tborder-bottom: 1px solid #e7e7e7;\r\n\r\n\t\t&:last-child {\r\n\t\t\tborder: 0;\r\n\t\t}\r\n\r\n\t\timage {\r\n\t\t\twidth: 24px;\r\n\t\t\theight: 24px;\r\n\t\t}\r\n\t}\r\n\r\n\t.sub-list-cell {\r\n\t\tfont-size: 12px !important;\r\n\r\n\t\t.list-lable {\r\n\t\t\tfont-weight: 400 !important;\r\n\t\t\tcolor: #666666 !important;\r\n\t\t}\r\n\r\n\t\t.text-right-btn {\r\n\t\t\tcolor: #1C6CED !important;\r\n\t\t}\r\n\r\n\t\t// .text-right,\r\n\t\t.text-left {\r\n\t\t\tcolor: #1C6CED !important;\r\n\t\t\tword-break: break-all;\r\n\t\t}\r\n\t}\r\n\r\n\t.collapse-content-desc {\r\n\t\t.text-right-btn {\r\n\t\t\tcolor: #1C6CED !important;\r\n\t\t}\r\n\r\n\t\t.text-right,\r\n\t\t.text-left {\r\n\t\t\tfont-size: 12px !important;\r\n\t\t\tcolor: #333 !important;\r\n\t\t\tword-break: break-all;\r\n\t\t}\r\n\t}\r\n\r\n\t.list-cell {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tjustify-content: space-between;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #333;\r\n\r\n\t\t.list-label-big {\r\n\t\t\tfont-size: 17px;\r\n\t\t\tdisplay: -webkit-box;\r\n\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t-webkit-line-clamp: 1;\r\n\t\t\tline-clamp: 1;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\r\n\t\t.text-right {\r\n\t\t\tcolor: #333;\r\n\t\t\topacity: 0.9;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t\tmargin-left: 16px;\r\n\t\t\tflex: 1;\r\n\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t.block-label {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-left: 27px;\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.60) !important;\r\n\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\twidth: 12px;\r\n\t\t\t\t\theight: 2px;\r\n\t\t\t\t\tbackground-color: #00919e;\r\n\t\t\t\t\tleft: -20px;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\tborder-radius: 5px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:nth-child(2)::after {\r\n\t\t\t\t\tbackground-color: #FFB546 !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tbutton {\r\n\t\t\t\tfont-size: 14px !important;\r\n\t\t\t\theight: 28px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tborder-radius: 6px;\r\n\t\t\t\tbackground-color: #F2F3FF;\r\n\t\t\t\tcolor: #1C6CED;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.list-lable {\r\n\t\tfont-weight: 600;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\timage {\r\n\t\t\twidth: 24px;\r\n\t\t\theight: 24px;\r\n\t\t\tmargin-right: 8px;\r\n\t\t}\r\n\t}\r\n\r\n\t.text-left {\r\n\t\tfont-size: 14px;\r\n\t\ttext-align: left;\r\n\t\tcolor: #333;\r\n\t\tmargin-top: 5px;\r\n\t\tword-break: break-all;\r\n\t}\r\n}\r\n\r\n.arrow-icon {\r\n\tfont-size: 20px;\r\n\tcolor: #999 !important;\r\n\tmargin-left: 5px;\r\n}\r\n\r\n// 图表\r\n.charts-box {\r\n\twidth: 100%;\r\n\theight: 210px;\r\n}\r\n\r\n.charts-info {\r\n\tfont-size: 11px;\r\n\tfont-weight: 400;\r\n\ttext-align: center;\r\n\tcolor: rgba(0, 0, 0, 0.60);\r\n\tmargin: 15px 0;\r\n\tpadding-bottom: 15px;\r\n}\r\n\r\n.charts-info-left {\r\n\tfont-size: 12px;\r\n\tfont-weight: 400;\r\n\ttext-align: left;\r\n\tcolor: rgba(0, 0, 0, 0.60);\r\n\tmargin-top: 15px;\r\n}\r\n\r\n.charts-label-box {\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 17px;\r\n\r\n\t.charts-label-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\r\n\t\t&:nth-child(2) {\r\n\t\t\tmargin-left: 40px;\r\n\t\t}\r\n\r\n\t\t.charts-label {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: #cccccc;\r\n\r\n\t\t\tspan {\r\n\t\t\t\tfont-size: 45px;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tcolor: #00919e;\r\n\t\t\t\tmargin-right: 5px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n\r\n// 折叠菜单\r\n.collapse-item {\r\n\twidth: 100%;\r\n\r\n\t.collapse-item-arrow-active {\r\n\t\ttransform: rotate(90deg);\r\n\t}\r\n\r\n\t.collapse-item--animation {\r\n\t\ttransition-property: transform;\r\n\t\ttransition-duration: 0.3s;\r\n\t\ttransition-timing-function: ease;\r\n\t}\r\n\r\n\t.collapse-content {\r\n\t\tmargin-top: 17px;\r\n\t\tfont-size: 17px;\r\n\t\tpadding: 0 10px;\r\n\t\tcolor: #666;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 6px;\r\n\r\n\t\t&.is--transition {\r\n\t\t\ttransition-property: width, height, background-color, border-width;\r\n\t\t\ttransition-duration: 0.3s;\r\n\t\t\ttransition-timing-function: ease-in;\r\n\t\t\ttransition-delay: 500ms;\r\n\t\t}\r\n\t}\r\n\r\n\timage {\r\n\t\tmargin-left: 17px;\r\n\t\tmargin-top: 5px;\r\n\t}\r\n}\r\n\r\n.no-collapse-content-box {\r\n\tmargin-bottom: 16px !important;\r\n\t.list-item2 {\r\n\t\tbackground-color: #fff !important;\r\n\t\tborder-bottom: 0px !important;\r\n\t\tpadding: 16px 0 !important;\r\n\r\n\t\t&::after {\r\n\t\t\theight: 0px !important;\r\n\t\t}\r\n\t}\r\n\r\n\t&:first-child {\r\n\t\t.list-item2 {\r\n\t\t\tpadding-top: 0px !important;\r\n\t\t}\r\n\t}\r\n\r\n\t.list-item {\r\n\t\tbackground: #f5f5f5;\r\n\t\tpadding: 16px 10px !important;\r\n\t\tposition: relative;\r\n\t\tborder-bottom: 0;\r\n\r\n\t\t&::after {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 10px;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 94%;\r\n\t\t\theight: 1px;\r\n\t\t\tbackground-color: #dddd;\r\n\t\t}\r\n\r\n\t\t&:nth-child(2) {\r\n\t\t\tborder-radius: 6px 6px 0 0;\r\n\t\t}\r\n\r\n\t\t&:last-child {\r\n\t\t\tborder-radius: 0 0 6px 6px;\r\n\r\n\t\t\t&::after {\r\n\t\t\t\theight: 0px !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.eye-icon {\r\n\tmargin-left: 3px;\r\n\tcolor: #000 !important;\r\n}\r\n\r\n.bottom-btn {\r\n\twidth: 91%;\r\n\tposition: fixed;\r\n\tleft: 50%;\r\n\tbottom: 20px;\r\n\ttransform: translateX(-50%);\r\n\tz-index: 9999;\r\n}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFile.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFile.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308004\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFile.vue?vue&type=style&index=1&id=afb6f976&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFile.vue?vue&type=style&index=1&id=afb6f976&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308030\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}