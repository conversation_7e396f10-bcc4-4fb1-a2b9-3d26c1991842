<view class="mask-box"><view class="zl-modal"><block wx:if="{{closeVisible}}"><view data-event-opts="{{[['tap',[['handleCancel',['$event']]]]]}}" class="iconfont icon-guanbi" bindtap="__e"></view></block><view class="dialog-content"><block wx:if="{{modalData.title}}"><view class="dialog-title">{{modalData.title}}</view></block><block wx:if="{{modalData.info}}"><view class="dialog-info">{{modalData.info+''}}<block wx:if="{{modalData.info1}}"><label data-event-opts="{{[['tap',[['goAgreement',['service']]]]]}}" bindtap="__e" class="_span">{{modalData.info1}}</label></block><block wx:if="{{modalData.info2}}"><label data-event-opts="{{[['tap',[['goAgreement',['policy']]]]]}}" bindtap="__e" class="_span">{{modalData.info2}}</label></block></view></block><block wx:if="{{modalData.type=='form'}}"><block><block wx:for="{{modalData.formData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view style="margin-top:10px;"><view>{{item.label}}</view><input maxlength="50" type="text" placeholder="{{item.placeholder}}" data-event-opts="{{[['input',[['setInputValue',['$event',index]]]]]}}" value="{{item.value}}" bindinput="__e"/></view></block></block></block><block wx:if="{{modalData.type=='list'}}"><view><radio-group value="{{selectedValue}}" data-event-opts="{{[['change',[['handleSlectPhone',['$event']]]],['input',[['__set_model',['','selectedValue','$event',[]]]]]]}}" bindchange="__e" bindinput="__e"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item"><radio style="transform:scale(0.7);" value="{{item}}" checked="{{item===selectedValue}}"><text>{{item}}</text></radio></view></block></radio-group></view></block><block wx:if="{{modalData.btnTxt}}"><button data-event-opts="{{[['tap',[['handleFormConfirm',['$event']]]]]}}" class="main-btn" bindtap="__e">{{modalData.btnTxt}}</button></block></view><block wx:if="{{btnVisible}}"><view class="dialog-footer"><view data-event-opts="{{[['tap',[['handleCancel',['$event']]]]]}}" bindtap="__e">{{modalData.cancelTxt}}</view><view data-event-opts="{{[['tap',[['handleConfirm',['$event']]]]]}}" bindtap="__e">{{modalData.confirmTxt}}</view></view></block></view></view>