{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/facialRecognitionVerify.vue?9805", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/facialRecognitionVerify.vue?4ca9", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/facialRecognitionVerify.vue?1ed8", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/facialRecognitionVerify.vue?0b54", "uni-app:///packagePages/my/facialRecognitionVerify.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/facialRecognitionVerify.vue?3d01", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/facialRecognitionVerify.vue?c18c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "methods", "handleNextSubmit", "uni", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gCAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC2E;AACL;AACc;;;AAGpF;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,6FAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAAsxB,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2B1yB;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACAC;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,y5CAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/my/facialRecognitionVerify.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/my/facialRecognitionVerify.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./facialRecognitionVerify.vue?vue&type=template&id=140aff98&\"\nvar renderjs\nimport script from \"./facialRecognitionVerify.vue?vue&type=script&lang=js&\"\nexport * from \"./facialRecognitionVerify.vue?vue&type=script&lang=js&\"\nimport style0 from \"./facialRecognitionVerify.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/my/facialRecognitionVerify.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./facialRecognitionVerify.vue?vue&type=template&id=140aff98&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function () {\n      return _vm.goAgreement(\"agreement\")\n    }\n    _vm.e1 = function () {\n      return _vm.goAgreement(\"policy\")\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./facialRecognitionVerify.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./facialRecognitionVerify.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-wrapper\">\r\n\t\t<zlnavbar :isBack=\"true\">\r\n\t\t\t<block slot=\"content\">人脸识别验证</block>\r\n\t\t</zlnavbar>\r\n\t\t<view class=\"login-form\">\r\n\t\t\t<image class=\"form-icon\" mode='widthFix' src=\"../../static/images/my/facialRecognition.png\"></image>\r\n\r\n\t\t\t<view class=\"bottom-btn-box\">\r\n\t\t\t\t<view class=\"main-btn\" @click=\"handleNextSubmit\">下一步</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"bottom-info\">\r\n\t\t\t\t<checkbox class=\"checked-btn\" color=\"#1C6CED\" :value=\"checkValue\" :checked=\"checkState\"\r\n\t\t\t\t\t@change=\"handleRadioChange\"></checkbox>\r\n\t\t\t\t<view>我已阅读并同意<span @click=\"() => goAgreement('agreement')\">《用户协议》</span><span\r\n\t\t\t\t\t\t@click=\"() => goAgreement('policy')\">《隐私政策》</span></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"bottom-tips\">\r\n\t\t\t\t<view class=\"\">1.请确认由本人亲自操作</view>\r\n\t\t\t\t<view class=\"\">2.请将脸置于提示框内，并按提示完成动作</view>\r\n\t\t\t\t<view class=\"\">3.人脸验证上传的图片将用于身份核实</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\thandleNextSubmit() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '去人脸识别',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.login-wrapper {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tflex-direction: column;\r\n\t\toverflow: scroll;\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t}\r\n\r\n\r\n\t.login-form {\r\n\t\twidth: 100%;\r\n\t\tpadding: 42px 34px;\r\n\r\n\t\timage {\r\n\t\t\twidth: 307px;\r\n\t\t\theight: 307px;\r\n\t\t\tmargin-bottom: 36px;\r\n\t\t}\r\n\r\n\r\n\t\t.bottom-tips {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t\t\tline-height: 22px;\r\n\t\t\tpadding: 0 32px;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\r\n\r\n\t.bottom-info {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #1C6CED;\r\n\t\tmargin-top: 23px;\r\n\t\t//padding: 0 45px;\r\n\t\tline-height: 20px;\r\n\t\tmargin-bottom: 31px;\r\n\r\n\t\tspan {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tcursor: pointer;\r\n\t\t}\r\n\r\n\t\tcheckbox {\r\n\t\t\t.wx-checkbox-input {\r\n\t\t\t\ttransform: scale(0.6);\r\n\t\t\t\tcolor: #1C6CED;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tborder-radius: 50% !important;\r\n\t\t\t\tmargin-right: 2px;\r\n\t\t\t\tmargin-top: -2px;\r\n\t\t\t}\r\n\r\n\t\t\t&:before {\r\n\t\t\t\ttop: 42% !important;\r\n\t\t\t\ttransform: scale(0.6);\r\n\t\t\t\tright: 7px\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./facialRecognitionVerify.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./facialRecognitionVerify.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308025\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}