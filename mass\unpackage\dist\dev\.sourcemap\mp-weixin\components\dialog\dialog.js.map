{"version": 3, "sources": ["webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/dialog/dialog.vue?dd81", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/dialog/dialog.vue?3a70", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/dialog/dialog.vue?f8a4", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/dialog/dialog.vue?0bbc", "uni-app:///components/dialog/dialog.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/dialog/dialog.vue?e244", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/dialog/dialog.vue?e259"], "names": ["props", "modalData", "dialogData", "btnVisible", "list", "type", "default", "closeVisible", "defult", "data", "selected<PERSON><PERSON><PERSON>", "mounted", "methods", "selectItem", "goAgreement", "uni", "url", "handleCancel", "handleConfirm", "handleFormConfirm", "handleSlectPhone", "console", "setInputValue"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqwB,CAAgB,qxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0CzxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAA;IACAC;IACAC;IACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;IACA;IACAH;MACAA;MACAC;IACA;EACA;EACAG;IACA;MACAC;IACA;EACA;EACAC,6BAEA;EACAC;IACAC;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACAC;IACA;IACAC;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACpGA;AAAA;AAAA;AAAA;AAAo7C,CAAgB,w4CAAG,EAAC,C;;;;;;;;;;;ACAx8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dialog/dialog.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dialog.vue?vue&type=template&id=09ce4f44&\"\nvar renderjs\nimport script from \"./dialog.vue?vue&type=script&lang=js&\"\nexport * from \"./dialog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dialog.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dialog/dialog.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialog.vue?vue&type=template&id=09ce4f44&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialog.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"mask-box\">\r\n\t\t<!-- 模态框 -->\r\n\t\t<view class=\"zl-modal\">\r\n\t\t\t<view class=\"iconfont icon-guanbi\" v-if=\"closeVisible\" @click=\"handleCancel\"></view>\r\n\t\t\t<view class=\"dialog-content\">\r\n\t\t\t\t<view class=\"dialog-title\" v-if='modalData.title'>{{modalData.title}}</view>\r\n\t\t\t\t<view class=\"dialog-info\" v-if='modalData.info'>{{modalData.info}}\r\n\t\t\t\t\t<span v-if=\"modalData.info1\" @click=\"goAgreement('service')\">{{modalData.info1}}</span>\r\n\t\t\t\t\t<span v-if=\"modalData.info2\" @click=\"goAgreement('policy')\">{{modalData.info2}}</span>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"modalData.type=='form'\">\r\n\t\t\t\t\t<view class=\"\" v-for=\"(item,index) in modalData.formData\" :key='index' style=\"margin-top:10px\">\r\n\t\t\t\t\t\t<view class=\"\">{{item.label}}</view>\r\n\t\t\t\t\t\t<input maxlength=\"50\" type=\"text\" :value=\"item.value\" :placeholder=\"item.placeholder\"\r\n\t\t\t\t\t\t\t@input=\"setInputValue($event,index)\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view v-if=\"modalData.type=='list'\">\r\n\t\t\t\t\t<radio-group v-model=\"selectedValue\" @change=\"handleSlectPhone\">\r\n\t\t\t\t\t\t<view class=\"list-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n\t\t\t\t\t\t\t<radio :value=\"item\" :checked=\"item === selectedValue\" style=\"transform: scale(0.7)\">\r\n\t\t\t\t\t\t\t\t<text>{{ item }}</text>\r\n\t\t\t\t\t\t\t</radio>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</radio-group>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 提交 -->\r\n\t\t\t\t<button class=\"main-btn\" v-if=\"modalData.btnTxt\"\r\n\t\t\t\t\t@click=\"handleFormConfirm\">{{modalData.btnTxt}}</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"dialog-footer\" v-if=\"btnVisible\">\r\n\t\t\t\t<view class=\"\" @click=\"handleCancel\">{{modalData.cancelTxt}}</view>\r\n\t\t\t\t<view class=\"\" @click=\"handleConfirm\">{{modalData.confirmTxt}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tmodalData: null,\r\n\t\t\tdialogData: null,\r\n\t\t\tbtnVisible: false,\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: []\r\n\t\t\t},\r\n\t\t\tcloseVisible: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefult: true\r\n\t\t\t},\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tselectedValue: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tselectItem(index) {\r\n\t\t\t\tthis.selectedIndex = index; // 更新选中项的索引\r\n\t\t\t},\r\n\t\t\tgoAgreement(type) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/login/agreement?type=${type}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleCancel() {\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\t\t\thandleConfirm() {\r\n\t\t\t\tthis.$emit('confirm')\r\n\t\t\t},\r\n\t\t\thandleFormConfirm() {\r\n\t\t\t\tif (!this.selectedValue) {\r\n\t\t\t\t\tthis.$emit('confirm', this.modalData.formData)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit('confirm', this.selectedValue)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thandleSlectPhone(e) {\r\n\t\t\t\tthis.selectedValue = e.detail.value\r\n\t\t\t\tconsole.log(this.selectedValue, 'selectedValue')\r\n\t\t\t},\r\n\t\t\tsetInputValue(e, index) {\r\n\t\t\t\tthis.modalData.formData[index].value = e.detail.value\r\n\t\t\t},\r\n\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.mask-box {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 9999;\r\n\t\tbackground-color: rgba($color: #000000, $alpha: 0.7);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.list-item {\r\n\t\tmargin: 10px 0;\r\n\t\tfont-size: 25px;\r\n\r\n\t\ttext {\r\n\t\t\tmargin-left: 8px;\r\n\t\t}\r\n\t}\r\n\r\n\tradio::before {\r\n\t\tfont-family: \"cuIcon\";\r\n\t\tcontent: \"\\e645\";\r\n\t\tposition: absolute;\r\n\t\tcolor: #ffffff !important;\r\n\t\ttop: 50%;\r\n\t\tleft: 5px;\r\n\t\t// transform: translate(-50%);\r\n\t\tfont-size: 32upx;\r\n\t\tline-height: 16px;\r\n\t\tpointer-events: none;\r\n\t\t// transform: scale(0.8, 0.8);\r\n\t\ttransition: all 0.3s ease-in-out 0s;\r\n\t}\r\n\r\n\t.zl-modal {\r\n\t\twidth: 87%;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 12px;\r\n\t\tposition: relative;\r\n\r\n\t\t.icon-guanbi {\r\n\t\t\twidth: 50px;\r\n\t\t\theight: 50px;\r\n\t\t\tposition: absolute;\r\n\t\t\tright: -15px;\r\n\t\t\ttop: -15px;\r\n\t\t\tfont-size: 19px;\r\n\t\t\tcolor: rgba($color: #000000, $alpha: 0.9);\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: flex-end;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t}\r\n\r\n\t\t.dialog-title {\r\n\t\t\tfont-size: 18px;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t\t\tposition: relative;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-bottom: 10px;\r\n\r\n\t\t}\r\n\r\n\t\t.dialog-info {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: rgba(0, 0, 0, 0.60);\r\n\t\t\tmargin-top: 4px;\r\n\r\n\t\t\ti {\r\n\t\t\t\twidth: 16px;\r\n\t\t\t\theight: 16px;\r\n\t\t\t\tbackground: #DCDEF8;\r\n\t\t\t\tfont-size: 9px;\r\n\t\t\t\tcolor: #4F5ADC;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tmargin-left: 10px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.dialog-content {\r\n\t\t\tpadding: 25px 24px;\r\n\r\n\t\t\t.main-btn {\r\n\t\t\t\tmargin-top: 24px\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tinput {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 45px;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 8px;\r\n\t\t\tborder: 1px solid #E7E7E9;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\t// color: #9E9EA7;\r\n\t\t\tpadding: 0 10px;\r\n\t\t\tmargin-top: 15px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tborder: 1px solid #4F5ADC;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\timage {\r\n\t\t\twidth: 50px;\r\n\t\t\theight: 50px;\r\n\t\t\tmargin-bottom: 20px;\r\n\t\t}\r\n\r\n\r\n\t\t.dialog-footer {\r\n\t\t\tborder-top: 1px solid #E7E7E9;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\tview {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tpadding: 15px 0;\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tcolor: #1C6CED;\r\n\t\t\t\t\tborder-left: 1px solid #e7e7e9;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialog.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialog.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542313669\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}