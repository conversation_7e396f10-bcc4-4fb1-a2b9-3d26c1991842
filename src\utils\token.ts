import { ElMessage } from 'element-plus';
import { useUserInfo } from '@/stores';
import { Local, Session } from '@/utils/storage';

// token 键定义
export const accessTokenKey = 'access-token';
export const refreshAccessTokenKey = `x-${accessTokenKey}`;

// 获取 token
export const getToken = () => {
  return Local.get(accessTokenKey);
};

// 清除 token
export const clearTokens = () => {
  const stores = useUserInfo();
  stores.userInfos = {} as any;
  Local.remove(accessTokenKey);
  Local.remove(refreshAccessTokenKey);
  Session.clear();
};

// 清除 token
export const clearAccessTokens = () => {
  clearTokens();

  // 刷新浏览器
  window.location.reload();
};

export const forceLogout = () => {
  ElMessage.error('该账号已在其他地方登录, 请重新登录');
  setTimeout(() => {
    clearAccessTokens();
  }, 1000);
  // getAPI(SysAuthApi).logoutPost();
};
