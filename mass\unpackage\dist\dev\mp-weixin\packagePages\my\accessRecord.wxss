
page {
	background-color: #F5F6F7 !important;
}

button.data-v-c7795f16 {
  outline: none;
  padding: 0;
}
.main-box.data-v-c7795f16 {
  padding: 16px;
  padding-bottom: 80px;
}
.list-box .list-item-box.data-v-c7795f16 {
  margin-bottom: 17px;
  background-color: #fff;
  border-radius: 12px;
  padding: 0 16px;
  margin-bottom: 17px;
}
.list-box .list-item.data-v-c7795f16 {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e7e7e7;
  padding: 16px 0;
}
.list-box .list-item.data-v-c7795f16:last-child {
  border: 0;
}
.list-box .list-item image.data-v-c7795f16 {
  width: 24px;
  height: 24px;
}
.list-box .list-cell.data-v-c7795f16 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 400;
  color: #333;
}
.list-box .list-cell .list-lable.data-v-c7795f16 {
  font-weight: 600;
}
.list-box .list-cell .list-cell-label.data-v-c7795f16 {
  padding: 1px 5px;
  font-size: 14px;
  color: #FFFFFF;
  background: #D54941;
  border-radius: 16px;
  margin-left: 10px;
}
.list-box .list-cell .text-right.data-v-c7795f16 {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
.arrow-icon.data-v-c7795f16 {
  font-size: 20px;
  color: #999 !important;
  margin-left: 5px;
}
.empty-box.data-v-c7795f16 {
  padding-top: 100px;
}

