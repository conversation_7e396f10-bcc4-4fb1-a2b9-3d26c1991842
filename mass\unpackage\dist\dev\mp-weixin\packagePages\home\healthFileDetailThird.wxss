
page {
	background-color: #F5F6F7 !important;
}

.main-box.data-v-960aaf7e {
  padding: 16px;
  padding-bottom: 70px;
}
.tabs.data-v-960aaf7e {
  border-bottom: 1px solid #e7e7e7;
}
.tabs-scroll.data-v-960aaf7e {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  box-sizing: border-box;
}
.tabs-scroll .tabs-scroll_item.data-v-960aaf7e {
  height: 48px;
  line-height: 48px;
  flex-shrink: 0;
  padding-bottom: 10px;
  display: flex;
  justify-content: center;
  font-size: 14px;
  padding: 0 16px;
  color: rgba(0, 0, 0, 0.9);
}
.tabs-scroll .tab-active.data-v-960aaf7e {
  position: relative;
  color: #0052D9 !important;
}
.tabs-scroll .tab-active.data-v-960aaf7e::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 3px;
  background: #0052d9;
  border-radius: 999px;
  left: 50%;
  bottom: 0px;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.list-box .list-item-box2.data-v-960aaf7e {
  background-color: #EF7029 !important;
}
.list-box .list-item-box2 .list-cell .list-lable.data-v-960aaf7e {
  font-size: 14px !important;
  color: #fff !important;
  font-weight: 4 00 !important;
}
.list-box .list-item-box2 .list-cell .text-right.data-v-960aaf7e {
  font-size: 17px !important;
  font-weight: 600 !important;
  color: #fff !important;
  opacity: 1 !important;
}
.list-box .list-item-box.data-v-960aaf7e {
  margin-bottom: 17px;
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 17px;
}
.list-box .list-content-item.data-v-960aaf7e {
  padding: 0 16px;
}
.list-box .list-item2 .list-cell .list-lable.data-v-960aaf7e {
  color: #1C6CED !important;
}
.list-box .list-item2 .list-cell .text-right.data-v-960aaf7e {
  color: #333 !important;
}
.list-box .list-item2 .list-cell .text-right-btn.data-v-960aaf7e {
  color: #1C6CED !important;
}
.list-box .list-item.data-v-960aaf7e {
  padding: 16px 0;
  border-bottom: 1px solid #e7e7e7;
}
.list-box .list-item.data-v-960aaf7e:last-child {
  border: 0;
}
.list-box .list-item image.data-v-960aaf7e {
  width: 24px;
  height: 24px;
}
.list-box .sub-list-cell.data-v-960aaf7e {
  font-size: 12px !important;
}
.list-box .sub-list-cell .list-lable.data-v-960aaf7e {
  font-weight: 400 !important;
  color: #666666 !important;
}
.list-box .sub-list-cell .text-right-btn.data-v-960aaf7e {
  color: #1C6CED !important;
}
.list-box .sub-list-cell .text-left.data-v-960aaf7e {
  color: #1C6CED !important;
  word-break: break-all;
}
.list-box .collapse-content-desc .text-right-btn.data-v-960aaf7e {
  color: #1C6CED !important;
}
.list-box .collapse-content-desc .text-right.data-v-960aaf7e,
.list-box .collapse-content-desc .text-left.data-v-960aaf7e {
  font-size: 12px !important;
  color: #333 !important;
  word-break: break-all;
}
.list-box .list-cell.data-v-960aaf7e {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  font-size: 14px;
  color: #333;
}
.list-box .list-cell .list-label-big.data-v-960aaf7e {
  font-size: 17px;
}
.list-box .list-cell .text-right.data-v-960aaf7e {
  color: #333;
  opacity: 0.9;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 16px;
  flex: 1;
}
.list-box .list-cell .text-right .block-label.data-v-960aaf7e {
  position: relative;
  margin-left: 46px;
  color: rgba(0, 0, 0, 0.6) !important;
}
.list-box .list-cell .text-right .block-label.data-v-960aaf7e::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 2px;
  background-color: #00919e;
  left: -20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-radius: 5px;
}
.list-box .list-cell .text-right .block-label.data-v-960aaf7e:last-child::after {
  background-color: #FFB546 !important;
}
.list-box .list-cell .text-right button.data-v-960aaf7e {
  font-size: 14px !important;
  height: 28px;
  line-height: 28px;
  border-radius: 6px;
  background-color: #F2F3FF;
  color: #1C6CED;
  margin: 0;
}
.list-box .list-lable.data-v-960aaf7e {
  font-weight: 600;
  display: flex;
  align-items: center;
}
.list-box .list-lable image.data-v-960aaf7e {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
.list-box .text-left.data-v-960aaf7e {
  font-size: 14px;
  text-align: left;
  color: #333;
  margin-top: 5px;
  word-break: break-all;
}
.arrow-icon.data-v-960aaf7e {
  font-size: 20px;
  color: #999 !important;
  margin-left: 5px;
}
.charts-box.data-v-960aaf7e {
  width: 100%;
  height: 210px;
}
.charts-label-box.data-v-960aaf7e {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 17px;
}
.charts-label-box .charts-label-item.data-v-960aaf7e {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.charts-label-box .charts-label-item.data-v-960aaf7e:last-child {
  margin-left: 46px;
}
.charts-label-box .charts-label-item .charts-label.data-v-960aaf7e {
  font-size: 12px;
  color: #cccccc;
}
.charts-label-box .charts-label-item .charts-label ._span.data-v-960aaf7e {
  font-size: 45px;
  font-weight: 700;
  color: #00919e;
  margin-right: 5px;
}
.collapse-item.data-v-960aaf7e {
  width: 100%;
}
.collapse-item .collapse-item-arrow-active.data-v-960aaf7e {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.collapse-item .collapse-item--animation.data-v-960aaf7e {
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}
.collapse-item .collapse-content.data-v-960aaf7e {
  margin-top: 17px;
  font-size: 17px;
  padding: 0 10px;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 6px;
}
.collapse-item .collapse-content.is--transition.data-v-960aaf7e {
  transition-property: width, height, background-color, border-width;
  transition-duration: 0.3s;
  transition-timing-function: ease-in;
  transition-delay: 500ms;
}
.collapse-item image.data-v-960aaf7e {
  margin-left: 17px;
  margin-top: 5px;
}
.no-collapse-content-box.data-v-960aaf7e {
  margin-bottom: 16px;
}
.no-collapse-content-box .list-item2.data-v-960aaf7e {
  background-color: #fff !important;
  border-bottom: 0px !important;
  padding: 16px 0 !important;
}
.no-collapse-content-box .list-item2.data-v-960aaf7e::after {
  height: 0px !important;
}
.no-collapse-content-box:first-child .list-item2.data-v-960aaf7e {
  padding-top: 0px !important;
}
.no-collapse-content-box .list-item.data-v-960aaf7e {
  background: #f5f5f5;
  padding: 16px 10px;
  position: relative;
  border-bottom: 0;
}
.no-collapse-content-box .list-item.data-v-960aaf7e::after {
  content: '';
  position: absolute;
  left: 10px;
  bottom: 0;
  width: 94%;
  height: 1px;
  background-color: #dddd;
}
.no-collapse-content-box .list-item.data-v-960aaf7e:nth-child(2) {
  border-radius: 6px 6px 0 0;
}
.no-collapse-content-box .list-item.data-v-960aaf7e:last-child {
  border-radius: 0 0 6px 6px;
}
.no-collapse-content-box .list-item.data-v-960aaf7e:last-child::after {
  height: 0px !important;
}
.bottom-btn.data-v-960aaf7e {
  width: 91%;
  position: fixed;
  left: 50%;
  bottom: 20px;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

