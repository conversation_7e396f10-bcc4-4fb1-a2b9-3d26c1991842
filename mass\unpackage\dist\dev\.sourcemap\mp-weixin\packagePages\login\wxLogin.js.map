{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/wxLogin.vue?6914", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/wxLogin.vue?48cd", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/wxLogin.vue?4774", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/wxLogin.vue?b74a", "uni-app:///packagePages/login/wxLogin.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/wxLogin.vue?07b1", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/wxLogin.vue?023d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Footer", "modalDialog", "data", "iPhoneX", "JawHeight", "showLogin", "loginType", "wxLoginDisabled", "page", "modalVisible", "modalData", "title", "info", "info1", "info2", "cancelTxt", "confirmTxt", "checkState", "onLoad", "console", "onShow", "methods", "goAgreement", "uni", "url", "checkWxLogin", "getPhoneNumber", "goBack", "delta", "emailLogin", "wxL<PERSON>in", "provider", "success", "nav", "channelCode", "loginCode", "setTimeout", "handleCheckboxChange", "handleCancel", "handleConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAAswB,CAAgB,sxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC4B1xB;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;IACA;EACA;EACAC;IACAD;EACA;EACAE;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;UACAF;UACA;UACA;UACAA;YACAC;UACA;QACA;MACA;IACA;IACAE;MACA;QACA;QACA;MACA;MACA;QAAA;QACA;UAAA;UACA;YACAH;YACA;YACA;YACAA;cACAC;YACA;UACA;QACA;MACA;IACA;IACAG;MACAJ;QACAK;MACA;IACA;IACAC;MACAN;QACAC;MACA;IACA;IACAM;MAAA;MACA;MACA;QACAP;UACAQ;UACAC;YACAb;UACA;QACA;QACAc;UACA;YACAV;cACAC;YACA;UACA;YACAD;cACAK;YACA;UACA;UACA;;UAEA;UACA;YACAK;cACAC;cACAC;YACA;UACA;QAGA;QACAC;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtKA;AAAA;AAAA;AAAA;AAAq7C,CAAgB,y4CAAG,EAAC,C;;;;;;;;;;;ACAz8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/login/wxLogin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/login/wxLogin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./wxLogin.vue?vue&type=template&id=ddd21a82&\"\nvar renderjs\nimport script from \"./wxLogin.vue?vue&type=script&lang=js&\"\nexport * from \"./wxLogin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wxLogin.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/login/wxLogin.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wxLogin.vue?vue&type=template&id=ddd21a82&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function () {\n      return _vm.goAgreement(\"service\")\n    }\n    _vm.e1 = function () {\n      return _vm.goAgreement(\"policy\")\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wxLogin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wxLogin.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-wrapper\" :style=\"{'padding-top': (iPhoneX ? 70+JawHeight : 70) + 'px'}\">\r\n\t\t<i class='iconfont  icon-close' @click='goBack'></i>\r\n\t\t<view class=\"logo-box\">\r\n\t\t\t<image class=\"logo\" src=\"../../static/images/login/logo.png\" mode=\"widthFix\"></image>\r\n\t\t</view>\r\n\t\t<image class=\"login-img\" src=\"../../static/images/login/center-img.png\" mode=\"widthFix\"></image>\r\n\t\t<view class=\"bottom-btn-box\">\r\n\t\t\t<button class=\"bottom-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">一键快捷登录</button>\r\n\t\t</view>\r\n\t\t<view class=\"bottom-info\">\r\n\t\t\t<checkbox class=\"checked-btn\" color=\"#578AFF\" :checked=\"checkState\" @tap='handleCheckboxChange(checkState)'>\r\n\t\t\t</checkbox>\r\n\t\t\t<view>\r\n\t\t\t\t<text>我已阅读并同意</text>\r\n\t\t\t\t<span @click=\"() => goAgreement('service')\">《用户服务协议》</span>\r\n\t\t\t\t<text>和</text>\r\n\t\t\t\t<span @click=\"() => goAgreement('policy')\">《个人信息保护政策》</span>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<modalDialog :modalData=\"modalData\" :btnVisible=\"true\" @cancel=\"handleCancel\" @confirm=\"handleConfirm\"\r\n\t\t\tv-show=\"modalVisible\">\r\n\t\t</modalDialog>\r\n\t\t<Footer style=\"width: 100%\" type='white' />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tsetAuthorization,\r\n\t\tsetInfoLogin\r\n\t} from \"@/utils/auth.js\"\r\n\timport {\r\n\t\tloginFn,\r\n\t\tgetPhoneNumberFn\r\n\t} from '@/api/login.js'\r\n\timport Footer from \"@/components/footer/index.vue\"\r\n\timport modalDialog from \"@/packagePages/components/dialog/dialog.vue\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tFooter,\r\n\t\t\tmodalDialog\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tiPhoneX: this.iPhoneX,\r\n\t\t\t\tJawHeight: this.JawHeight,\r\n\t\t\t\tshowLogin: false,\r\n\t\t\t\tloginType: 0,\r\n\t\t\t\twxLoginDisabled: false,\r\n\t\t\t\tpage: null,\r\n\t\t\t\tmodalVisible: false,\r\n\t\t\t\tmodalData: {\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tinfo: '您是否同意 ',\r\n\t\t\t\t\tinfo1: '《用户服务协议》、',\r\n\t\t\t\t\tinfo2: '《个人信息保护政策》',\r\n\t\t\t\t\tcancelTxt: '不同意',\r\n\t\t\t\t\tconfirmTxt: '同意',\r\n\t\t\t\t},\r\n\t\t\t\tcheckState: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tconsole.log('777options跳转传参denglu', options)\r\n\t\t\tthis.checkWxLogin()\r\n\t\t},\r\n\t\tonShow(options) {\r\n\t\t\tconsole.log('onShow来自健康通辽LOgin', options)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoAgreement(type) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/login/agreement?type=${type}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 首次状态判断\r\n\t\t\tcheckWxLogin() {\r\n\t\t\t\tloginFn(1).then(res => { \r\n\t\t\t\t\t// console.log(res.data,res.data.userExist, '接口换取的openid')\r\n\t\t\t\t\tif (res.data.userExist) {\r\n\t\t\t\t\t\tuni.setStorageSync('resident_id', res.data.loginUser.id)\r\n\t\t\t\t\t\tsetAuthorization(res.data.access_token)\r\n\t\t\t\t\t\tsetInfoLogin(true)\r\n\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\turl: `/pages/index/index`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetPhoneNumber(e) {\r\n\t\t\t\tif (!this.checkState) {\r\n\t\t\t\t\tthis.modalVisible = true\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tloginFn(2).then(response => { // 微信登录&服务端获取openid\r\n\t\t\t\t\tgetPhoneNumberFn(e.detail.code, 2).then(res => { // 服务端获取手机号\r\n\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\tuni.setStorageSync('resident_id', res.data.loginUser.id)\r\n\t\t\t\t\t\t\tsetAuthorization(res.data.access_token)\r\n\t\t\t\t\t\t\tsetInfoLogin(true)\r\n\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\turl: `/pages/index/index`\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\tdelta: 1\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\temailLogin() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/login/emailLogin?page=' + this.page\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\twxLogin(e) {\r\n\t\t\t\tthis.wxLoginDisabled = true\r\n\t\t\t\tif (!api.isInfoLogin()) {\r\n\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\t\tsuccess(loginResult) {\r\n\t\t\t\t\t\t\tconsole.log(loginResult, 'loginResult')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tnav.navToPersonal(() => {\r\n\t\t\t\t\t\tif (this.page) {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.login = api.isInfoLogin();\r\n\r\n\t\t\t\t\t\t// 扫码登录\r\n\t\t\t\t\t\tif (uni.getStorageSync(\"loginCode\")) {\r\n\t\t\t\t\t\t\tnav.codeAuth({\r\n\t\t\t\t\t\t\t\tchannelCode: uni.getStorageSync(\"channelCode\") || '',\r\n\t\t\t\t\t\t\t\tloginCode: uni.getStorageSync(\"loginCode\") || ''\r\n\t\t\t\t\t\t\t}, () => {})\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.wxLoginDisabled = false\r\n\t\t\t\t\t}, 200)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thandleCheckboxChange(state) {\r\n\t\t\t\tthis.checkState = !state\r\n\t\t\t},\r\n\t\t\thandleCancel() {\r\n\t\t\t\tthis.modalVisible = false\r\n\t\t\t\tthis.checkState = false\r\n\t\t\t},\r\n\t\t\thandleConfirm() {\r\n\t\t\t\tthis.modalVisible = false\r\n\t\t\t\tthis.checkState = true\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.login-wrapper {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: #578AFF;\r\n\t\tpadding: 20px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-direction: column;\r\n\t\toverflow: scroll;\r\n\t}\r\n\r\n\tbutton {\r\n\t\tborder: 0;\r\n\t\toutline: none;\r\n\t}\r\n\r\n\t.icon-close {\r\n\t\tcolor: #000000;\r\n\t\tfont-size: 23px;\r\n\t}\r\n\r\n\r\n\t.login-img {\r\n\t\twidth: 80%;\r\n\t\tmin-height: 219px;\r\n\t\tmargin-bottom: 40px;\r\n\t}\r\n\r\n\t.logo-box {\r\n\t\twidth: 100%;\r\n\t\tfont-size: 12px;\r\n\t\tfont-weight: 400;\r\n\t\tcolor: #3D3E4F;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 48px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex-direction: column;\r\n\t\tmargin-top: 30px;\r\n\r\n\t\timage {\r\n\t\t\twidth: 257px;\r\n\t\t\tobject-fit: cover;\r\n\t\t\tmargin-bottom: 10px;\r\n\t\t}\r\n\t}\r\n\r\n\t.bottom-btn-box {\r\n\t\twidth: 96%;\r\n\t\tmargin-top: 32px;\r\n\r\n\t\t.bottom-btn {\r\n\t\t\tfont-size: 16px;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tcolor: #1C6CED;\r\n\t\t}\r\n\t}\r\n\r\n\t.bottom-info {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 11px;\r\n\t\tcolor: #fff;\r\n\t\tmargin-top: 40px;\r\n\t\tpadding: 0 10px;\r\n\t\tline-height: 20px;\r\n\t\twhite-space: nowrap;\r\n\t\t// margin-bottom: 25px;\r\n\r\n\t\ttext {\r\n\t\t\topacity: 0.7;\r\n\t\t}\r\n\r\n\t\tspan {\r\n\t\t\t// color: #0052d9;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tcursor: pointer;\r\n\t\t\topacity: 1 !important;\r\n\t\t}\r\n\r\n\t\tcheckbox {\r\n\t\t\t.wx-checkbox-input {\r\n\t\t\t\ttransform: scale(0.6);\r\n\t\t\t\t// color: #fff!important;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tborder-radius: 50% !important;\r\n\t\t\t\tmargin-right: 2px;\r\n\t\t\t\tmargin-top: -2px;\r\n\t\t\t}\r\n\r\n\t\t\t&:before {\r\n\t\t\t\t// color: #1C6CED!important;\r\n\t\t\t\ttop: 42% !important;\r\n\t\t\t\ttransform: scale(0.6);\r\n\t\t\t\tright: 7px\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wxLogin.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wxLogin.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308055\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}