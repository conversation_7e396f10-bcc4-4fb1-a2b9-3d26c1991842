<view class="login-wrapper"><zlnavbar vue-id="56ad83e4-1" isBack="{{true}}" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content">AI健康助手</view></zlnavbar><view class="main-content"><block wx:for="{{dialogList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="dialog-list"><view class="{{[item.type==='question'?'dialog-info flex-end-content':'dialog-info']}}"><block wx:if="{{item.type==='answer'}}"><view class="image"><image src="/static/images/index/AI_icon.png" mode="widthFix"></image></view></block><view class="{{['dialog-content',item.type==='answer'?'answer':'quest']}}"><block wx:if="{{!item.content}}"><text class="dotting"></text></block><rich-text nodes="{{item.content}}"></rich-text></view><block wx:if="{{item.type==='question'}}"><view class="image image2"><block wx:if="{{item.type==='question'}}"><image src="/static/images/index/userImg.png" mode="widthFix"></image></block></view></block></view></view></block><block wx:if="{{!loading}}"><view class="main-button"><block wx:for="{{btnList}}" wx:for-item="item" wx:for-index="index"><view><block wx:if="{{item.show}}"><view data-event-opts="{{[['tap',[['ask',['$0'],[[['btnList','',index]]]]]]]}}" class="button-list" bindtap="__e"><text class="question">{{item.title}}</text><image src="/static/images/index/arrow-right.png" mode="widthFix"></image></view></block></view></block><block wx:if="{{$root.g0}}"><view class="tips">本内容由AI生成，内容仅供参考，请仔细甄别</view></block></view></block></view></view>