# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aashutoshrathi/word-wrap@^1.2.3":
  "integrity" "sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA=="
  "resolved" "https://registry.npmmirror.com/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz"
  "version" "1.2.6"

"@antfu/utils@^0.5.2":
  "integrity" "sha512-CQkeV+oJxUazwjlHD0/3ZD08QWKuGQkhnrKo3e6ly5pd48VUpXbb77q0xMU4+vc2CkJnDS02Eq/M9ugyX20XZA=="
  "resolved" "https://registry.npmmirror.com/@antfu/utils/-/utils-0.5.2.tgz"
  "version" "0.5.2"

"@babel/code-frame@^7.0.0":
  "integrity" "sha512-CgH3s1a96LipHCmSUmYFPwY7MNx8C3avkq7i4Wl3cfa662ldtUe4VM1TPXX70pfmrlWTb6jLqTYrZyT2ZTJBgA=="
  "resolved" "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.23.5.tgz"
  "version" "7.23.5"
  dependencies:
    "@babel/highlight" "^7.23.4"
    "chalk" "^2.4.2"

"@babel/helper-validator-identifier@^7.22.20":
  "integrity" "sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz"
  "version" "7.22.20"

"@babel/highlight@^7.23.4":
  "integrity" "sha512-acGdbYSfp2WheJoJm/EBBBLh/ID8KDc64ISZ9DYtBmC8/Q204PZJLHyzeB5qMzJ5trcOkybd78M4x2KWsUq++A=="
  "resolved" "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.23.4.tgz"
  "version" "7.23.4"
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.20"
    "chalk" "^2.4.2"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.15.8", "@babel/parser@^7.23.6":
  "integrity" "sha512-Z2uID7YJ7oNvAI20O9X0bblw7Qqs8Q2hFy0R9tAfnfLkp5MW0UH9eUvnDSnFwKZ0AvgS1ucqR4KzvVHgnke1VQ=="
  "resolved" "https://registry.npmmirror.com/@babel/parser/-/parser-7.23.6.tgz"
  "version" "7.23.6"

"@babel/runtime@^7.12.0", "@babel/runtime@^7.12.5", "@babel/runtime@^7.14.0":
  "integrity" "sha512-w06OXVOFso7LcbzMiDGt+3X7Rh7Ho8MmgPoWU3rarH+8upf+wSU/grlGbWzQyr3DkdN6ZeuMFjpdwW0Q+HxobA=="
  "resolved" "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.23.7.tgz"
  "version" "7.23.7"
  dependencies:
    "regenerator-runtime" "^0.14.0"

"@better-scroll/core@^2.4.2":
  "integrity" "sha512-koKOuYA55dQ04FJRIVUpMGDr1hbCfWmfX0MGp1hKagkQSWSRpwblqACiwtggVauoj9aaJRJZ9hDsTM4weaavlg=="
  "resolved" "https://registry.npmmirror.com/@better-scroll/core/-/core-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "@better-scroll/shared-utils" "^2.5.1"

"@better-scroll/shared-utils@^2.5.1":
  "integrity" "sha512-AplkfSjXVYP9LZiD6JsKgmgQJ/mG4uuLmBuwLz8W5OsYc7AYTfN8kw6GqZ5OwCGoXkVhBGyd8NeC4xwYItp0aw=="
  "resolved" "https://registry.npmmirror.com/@better-scroll/shared-utils/-/shared-utils-2.5.1.tgz"
  "version" "2.5.1"

"@commitlint/cli@^17.3.0":
  "integrity" "sha512-ay+WbzQesE0Rv4EQKfNbSMiJJ12KdKTDzIt0tcK4k11FdsWmtwP0Kp1NWMOUswfIWo6Eb7p7Ln721Nx9FLNBjg=="
  "resolved" "https://registry.npmmirror.com/@commitlint/cli/-/cli-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/format" "^17.8.1"
    "@commitlint/lint" "^17.8.1"
    "@commitlint/load" "^17.8.1"
    "@commitlint/read" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    "execa" "^5.0.0"
    "lodash.isfunction" "^3.0.9"
    "resolve-from" "5.0.0"
    "resolve-global" "1.0.0"
    "yargs" "^17.0.0"

"@commitlint/config-conventional@^17.3.0":
  "integrity" "sha512-NxCOHx1kgneig3VLauWJcDWS40DVjg7nKOpBEEK9E5fjJpQqLCilcnKkIIjdBH98kEO1q3NpE5NSrZ2kl/QGJg=="
  "resolved" "https://registry.npmmirror.com/@commitlint/config-conventional/-/config-conventional-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "conventional-changelog-conventionalcommits" "^6.1.0"

"@commitlint/config-validator@^17.8.1":
  "integrity" "sha512-UUgUC+sNiiMwkyiuIFR7JG2cfd9t/7MV8VB4TZ+q02ZFkHoduUS4tJGsCBWvBOGD9Btev6IecPMvlWUfJorkEA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/config-validator/-/config-validator-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/types" "^17.8.1"
    "ajv" "^8.11.0"

"@commitlint/ensure@^17.8.1":
  "integrity" "sha512-xjafwKxid8s1K23NFpL8JNo6JnY/ysetKo8kegVM7c8vs+kWLP8VrQq+NbhgVlmCojhEDbzQKp4eRXSjVOGsow=="
  "resolved" "https://registry.npmmirror.com/@commitlint/ensure/-/ensure-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/types" "^17.8.1"
    "lodash.camelcase" "^4.3.0"
    "lodash.kebabcase" "^4.1.1"
    "lodash.snakecase" "^4.1.1"
    "lodash.startcase" "^4.4.0"
    "lodash.upperfirst" "^4.3.1"

"@commitlint/execute-rule@^17.8.1":
  "integrity" "sha512-JHVupQeSdNI6xzA9SqMF+p/JjrHTcrJdI02PwesQIDCIGUrv04hicJgCcws5nzaoZbROapPs0s6zeVHoxpMwFQ=="
  "resolved" "https://registry.npmmirror.com/@commitlint/execute-rule/-/execute-rule-17.8.1.tgz"
  "version" "17.8.1"

"@commitlint/format@^17.8.1":
  "integrity" "sha512-f3oMTyZ84M9ht7fb93wbCKmWxO5/kKSbwuYvS867duVomoOsgrgljkGGIztmT/srZnaiGbaK8+Wf8Ik2tSr5eg=="
  "resolved" "https://registry.npmmirror.com/@commitlint/format/-/format-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/types" "^17.8.1"
    "chalk" "^4.1.0"

"@commitlint/is-ignored@^17.8.1":
  "integrity" "sha512-UshMi4Ltb4ZlNn4F7WtSEugFDZmctzFpmbqvpyxD3la510J+PLcnyhf9chs7EryaRFJMdAKwsEKfNK0jL/QM4g=="
  "resolved" "https://registry.npmmirror.com/@commitlint/is-ignored/-/is-ignored-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/types" "^17.8.1"
    "semver" "7.5.4"

"@commitlint/lint@^17.8.1":
  "integrity" "sha512-aQUlwIR1/VMv2D4GXSk7PfL5hIaFSfy6hSHV94O8Y27T5q+DlDEgd/cZ4KmVI+MWKzFfCTiTuWqjfRSfdRllCA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/lint/-/lint-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/is-ignored" "^17.8.1"
    "@commitlint/parse" "^17.8.1"
    "@commitlint/rules" "^17.8.1"
    "@commitlint/types" "^17.8.1"

"@commitlint/load@^17.8.1", "@commitlint/load@>6.1.1":
  "integrity" "sha512-iF4CL7KDFstP1kpVUkT8K2Wl17h2yx9VaR1ztTc8vzByWWcbO/WaKwxsnCOqow9tVAlzPfo1ywk9m2oJ9ucMqA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/load/-/load-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/config-validator" "^17.8.1"
    "@commitlint/execute-rule" "^17.8.1"
    "@commitlint/resolve-extends" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    "@types/node" "20.5.1"
    "chalk" "^4.1.0"
    "cosmiconfig" "^8.0.0"
    "cosmiconfig-typescript-loader" "^4.0.0"
    "lodash.isplainobject" "^4.0.6"
    "lodash.merge" "^4.6.2"
    "lodash.uniq" "^4.5.0"
    "resolve-from" "^5.0.0"
    "ts-node" "^10.8.1"
    "typescript" "^4.6.4 || ^5.2.2"

"@commitlint/message@^17.8.1":
  "integrity" "sha512-6bYL1GUQsD6bLhTH3QQty8pVFoETfFQlMn2Nzmz3AOLqRVfNNtXBaSY0dhZ0dM6A2MEq4+2d7L/2LP8TjqGRkA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/message/-/message-17.8.1.tgz"
  "version" "17.8.1"

"@commitlint/parse@^17.8.1":
  "integrity" "sha512-/wLUickTo0rNpQgWwLPavTm7WbwkZoBy3X8PpkUmlSmQJyWQTj0m6bDjiykMaDt41qcUbfeFfaCvXfiR4EGnfw=="
  "resolved" "https://registry.npmmirror.com/@commitlint/parse/-/parse-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/types" "^17.8.1"
    "conventional-changelog-angular" "^6.0.0"
    "conventional-commits-parser" "^4.0.0"

"@commitlint/read@^17.8.1":
  "integrity" "sha512-Fd55Oaz9irzBESPCdMd8vWWgxsW3OWR99wOntBDHgf9h7Y6OOHjWEdS9Xzen1GFndqgyoaFplQS5y7KZe0kO2w=="
  "resolved" "https://registry.npmmirror.com/@commitlint/read/-/read-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/top-level" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    "fs-extra" "^11.0.0"
    "git-raw-commits" "^2.0.11"
    "minimist" "^1.2.6"

"@commitlint/resolve-extends@^17.8.1":
  "integrity" "sha512-W/ryRoQ0TSVXqJrx5SGkaYuAaE/BUontL1j1HsKckvM6e5ZaG0M9126zcwL6peKSuIetJi7E87PRQF8O86EW0Q=="
  "resolved" "https://registry.npmmirror.com/@commitlint/resolve-extends/-/resolve-extends-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/config-validator" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    "import-fresh" "^3.0.0"
    "lodash.mergewith" "^4.6.2"
    "resolve-from" "^5.0.0"
    "resolve-global" "^1.0.0"

"@commitlint/rules@^17.8.1":
  "integrity" "sha512-2b7OdVbN7MTAt9U0vKOYKCDsOvESVXxQmrvuVUZ0rGFMCrCPJWWP1GJ7f0lAypbDAhaGb8zqtdOr47192LBrIA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/rules/-/rules-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "@commitlint/ensure" "^17.8.1"
    "@commitlint/message" "^17.8.1"
    "@commitlint/to-lines" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    "execa" "^5.0.0"

"@commitlint/to-lines@^17.8.1":
  "integrity" "sha512-LE0jb8CuR/mj6xJyrIk8VLz03OEzXFgLdivBytoooKO5xLt5yalc8Ma5guTWobw998sbR3ogDd+2jed03CFmJA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/to-lines/-/to-lines-17.8.1.tgz"
  "version" "17.8.1"

"@commitlint/top-level@^17.8.1":
  "integrity" "sha512-l6+Z6rrNf5p333SHfEte6r+WkOxGlWK4bLuZKbtf/2TXRN+qhrvn1XE63VhD8Oe9oIHQ7F7W1nG2k/TJFhx2yA=="
  "resolved" "https://registry.npmmirror.com/@commitlint/top-level/-/top-level-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "find-up" "^5.0.0"

"@commitlint/types@^17.8.1":
  "integrity" "sha512-PXDQXkAmiMEG162Bqdh9ChML/GJZo6vU+7F03ALKDK8zYc6SuAr47LjG7hGYRqUOz+WK0dU7bQ0xzuqFMdxzeQ=="
  "resolved" "https://registry.npmmirror.com/@commitlint/types/-/types-17.8.1.tgz"
  "version" "17.8.1"
  dependencies:
    "chalk" "^4.1.0"

"@cspotcode/source-map-support@^0.8.0":
  "integrity" "sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw=="
  "resolved" "https://registry.npmmirror.com/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  "version" "0.8.1"
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@ctrl/tinycolor@^3.4.1":
  "integrity" "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA=="
  "resolved" "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz"
  "version" "3.6.1"

"@element-plus/icons-vue@^2.1.0", "@element-plus/icons-vue@^2.3.1":
  "integrity" "sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg=="
  "resolved" "https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz"
  "version" "2.3.1"

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
  "integrity" "sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA=="
  "resolved" "https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "eslint-visitor-keys" "^3.3.0"

"@eslint-community/regexpp@^4.4.0", "@eslint-community/regexpp@^4.6.1":
  "integrity" "sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA=="
  "resolved" "https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.10.0.tgz"
  "version" "4.10.0"

"@eslint/eslintrc@^2.1.4":
  "integrity" "sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ=="
  "resolved" "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.3.2"
    "espree" "^9.6.0"
    "globals" "^13.19.0"
    "ignore" "^5.2.0"
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "minimatch" "^3.1.2"
    "strip-json-comments" "^3.1.1"

"@eslint/js@8.56.0":
  "integrity" "sha512-gMsVel9D7f2HLkBma9VbtzZRehRogVRfbr++f06nL2vnCGCNlzOD+/MUov/F4p8myyAHspEhVobgjpX64q5m6A=="
  "resolved" "https://registry.npmmirror.com/@eslint/js/-/js-8.56.0.tgz"
  "version" "8.56.0"

"@fast-csv/format@4.3.5":
  "integrity" "sha512-8iRn6QF3I8Ak78lNAa+Gdl5MJJBM5vRHivFtMRUWINdevNo00K7OXxS2PshawLKTejVwieIlPmK5YlLu6w4u8A=="
  "resolved" "https://registry.npmmirror.com/@fast-csv/format/-/format-4.3.5.tgz"
  "version" "4.3.5"
  dependencies:
    "@types/node" "^14.0.1"
    "lodash.escaperegexp" "^4.1.2"
    "lodash.isboolean" "^3.0.3"
    "lodash.isequal" "^4.5.0"
    "lodash.isfunction" "^3.0.9"
    "lodash.isnil" "^4.0.0"

"@fast-csv/parse@4.3.6":
  "integrity" "sha512-uRsLYksqpbDmWaSmzvJcuApSEe38+6NQZBUsuAyMZKqHxH0g1wcJgsKUvN3WC8tewaqFjBMMGrkHmC+T7k8LvA=="
  "resolved" "https://registry.npmmirror.com/@fast-csv/parse/-/parse-4.3.6.tgz"
  "version" "4.3.6"
  dependencies:
    "@types/node" "^14.0.1"
    "lodash.escaperegexp" "^4.1.2"
    "lodash.groupby" "^4.6.0"
    "lodash.isfunction" "^3.0.9"
    "lodash.isnil" "^4.0.0"
    "lodash.isundefined" "^3.0.1"
    "lodash.uniq" "^4.5.0"

"@floating-ui/core@^1.4.2":
  "integrity" "sha512-Ii3MrfY/GAIN3OhXNzpCKaLxHQfJF9qvwq/kEJYdqDxeIHa01K8sldugal6TmeeXl+WMvhv9cnVzUTaFFJF09A=="
  "resolved" "https://registry.npmmirror.com/@floating-ui/core/-/core-1.5.2.tgz"
  "version" "1.5.2"
  dependencies:
    "@floating-ui/utils" "^0.1.3"

"@floating-ui/dom@^1.0.1":
  "integrity" "sha512-ClAbQnEqJAKCJOEbbLo5IUlZHkNszqhuxS4fHAVxRPXPya6Ysf2G8KypnYcOTpx6I8xcgF9bbHb6g/2KpbV8qA=="
  "resolved" "https://registry.npmmirror.com/@floating-ui/dom/-/dom-1.5.3.tgz"
  "version" "1.5.3"
  dependencies:
    "@floating-ui/core" "^1.4.2"
    "@floating-ui/utils" "^0.1.3"

"@floating-ui/utils@^0.1.3":
  "integrity" "sha512-OfX7E2oUDYxtBvsuS4e/jSn4Q9Qb6DzgeYtsAdkPZ47znpoNsMgZw0+tVijiv3uGNR6dgNlty6r9rzIzHjtd/A=="
  "resolved" "https://registry.npmmirror.com/@floating-ui/utils/-/utils-0.1.6.tgz"
  "version" "0.1.6"

"@humanwhocodes/config-array@^0.11.13":
  "integrity" "sha512-JSBDMiDKSzQVngfRjOdFXgFfklaXI4K9nLF49Auh21lmBWRLIK3+xTErTWD4KU54pb6coM6ESE7Awz/FNU3zgQ=="
  "resolved" "https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.11.13.tgz"
  "version" "0.11.13"
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.1"
    "debug" "^4.1.1"
    "minimatch" "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  "integrity" "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA=="
  "resolved" "https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  "version" "1.0.1"

"@humanwhocodes/object-schema@^2.0.1":
  "integrity" "sha512-dvuCeX5fC9dXgJn9t+X5atfmgQAzUOWqS1254Gh0m6i8wKd10ebXkfNKiRK+1GWi/yTvvLDHpoxLr0xxxeslWw=="
  "resolved" "https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-2.0.1.tgz"
  "version" "2.0.1"

"@jridgewell/resolve-uri@^3.0.3":
  "integrity" "sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.1.tgz"
  "version" "3.1.1"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.15":
  "integrity" "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
  "version" "1.4.15"

"@jridgewell/trace-mapping@0.3.9":
  "integrity" "sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  "version" "0.3.9"
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7":
  "integrity" "sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ=="
  "resolved" "https://registry.npmmirror.com/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz"
  "version" "2.11.7"

"@rollup/pluginutils@^4.1.2", "@rollup/pluginutils@^4.2.1":
  "integrity" "sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ=="
  "resolved" "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "estree-walker" "^2.0.1"
    "picomatch" "^2.2.2"

"@transloadit/prettier-bytes@0.0.7":
  "integrity" "sha512-VeJbUb0wEKbcwaSlj5n+LscBl9IPgLPkHVGBkh00cztv6X4L/TJXK58LzFuBKX7/GAfiGhIwH67YTLTlzvIzBA=="
  "resolved" "https://registry.npmmirror.com/@transloadit/prettier-bytes/-/prettier-bytes-0.0.7.tgz"
  "version" "0.0.7"

"@trysound/sax@0.2.0":
  "integrity" "sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA=="
  "resolved" "https://registry.npmmirror.com/@trysound/sax/-/sax-0.2.0.tgz"
  "version" "0.2.0"

"@tsconfig/node10@^1.0.7":
  "integrity" "sha512-jNsYVVxU8v5g43Erja32laIDHXeoNvFEpX33OK4d6hljo3jDhCBDhx5dhCCTMWUojscpAagGiRkBKxpdl9fxqA=="
  "resolved" "https://registry.npmmirror.com/@tsconfig/node10/-/node10-1.0.9.tgz"
  "version" "1.0.9"

"@tsconfig/node12@^1.0.7":
  "integrity" "sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag=="
  "resolved" "https://registry.npmmirror.com/@tsconfig/node12/-/node12-1.0.11.tgz"
  "version" "1.0.11"

"@tsconfig/node14@^1.0.0":
  "integrity" "sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow=="
  "resolved" "https://registry.npmmirror.com/@tsconfig/node14/-/node14-1.0.3.tgz"
  "version" "1.0.3"

"@tsconfig/node16@^1.0.2":
  "integrity" "sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA=="
  "resolved" "https://registry.npmmirror.com/@tsconfig/node16/-/node16-1.0.4.tgz"
  "version" "1.0.4"

"@types/event-emitter@^0.3.3":
  "integrity" "sha512-zx2/Gg0Eg7gwEiOIIh5w9TrhKKTeQh7CPCOPNc0el4pLSwzebA8SmnHwZs2dWlLONvyulykSwGSQxQHLhjGLvQ=="
  "resolved" "https://registry.npmmirror.com/@types/event-emitter/-/event-emitter-0.3.5.tgz"
  "version" "0.3.5"

"@types/json-schema@^7.0.9":
  "integrity" "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA=="
  "resolved" "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz"
  "version" "7.0.15"

"@types/lodash-es@*", "@types/lodash-es@^4.17.6":
  "integrity" "sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ=="
  "resolved" "https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.12.tgz"
  "version" "4.17.12"
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@^4.14.182":
  "integrity" "sha512-OvlIYQK9tNneDlS0VN54LLd5uiPCBOp7gS5Z0f1mjoJYBrtStzgmJBxONW3U6OZqdtNzZPmn9BS/7WI7BFFcFQ=="
  "resolved" "https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.202.tgz"
  "version" "4.14.202"

"@types/minimist@^1.2.0":
  "integrity" "sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag=="
  "resolved" "https://registry.npmmirror.com/@types/minimist/-/minimist-1.2.5.tgz"
  "version" "1.2.5"

"@types/node@*", "@types/node@>= 14", "@types/node@20.5.1":
  "integrity" "sha512-4tT2UrL5LBqDwoed9wZ6N3umC4Yhz3W3FloMmiiG4JwmUJWpie0c7lcnUNd4gtMKuDEO4wRVS8B6Xa0uMRsMKg=="
  "resolved" "https://registry.npmmirror.com/@types/node/-/node-20.5.1.tgz"
  "version" "20.5.1"

"@types/node@^14.0.1":
  "integrity" "sha512-fAtCfv4jJg+ExtXhvCkCqUKZ+4ok/JQk01qDKhL5BDDoS3AxKXhV5/MAVUZyQnSEd2GT92fkgZl0pz0Q0AzcIQ=="
  "resolved" "https://registry.npmmirror.com/@types/node/-/node-14.18.63.tgz"
  "version" "14.18.63"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA=="
  "resolved" "https://registry.npmmirror.com/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz"
  "version" "2.4.4"

"@types/raf@^3.4.0":
  "integrity" "sha512-c4YAvMedbPZ5tEyxzQdMoOhhJ4RD3rngZIdwC2/qDN3d7JpEhB6fiBRKVY1lg5B7Wk+uPBjn5f39j1/2MY1oOw=="
  "resolved" "https://registry.npmmirror.com/@types/raf/-/raf-3.4.3.tgz"
  "version" "3.4.3"

"@types/semver@^7.3.12":
  "integrity" "sha512-dn1l8LaMea/IjDoHNd9J52uBbInB796CDffS6VdIxvqYCPSG0V0DzHp76GpaWnlhg88uYyPbXCDIowa86ybd5A=="
  "resolved" "https://registry.npmmirror.com/@types/semver/-/semver-7.5.6.tgz"
  "version" "7.5.6"

"@types/svgo@^2.6.1":
  "integrity" "sha512-l4cmyPEckf8moNYHdJ+4wkHvFxjyW6ulm9l4YGaOxeyBWPhBOT0gvni1InpFPdzx1dKf/2s62qGITwxNWnPQng=="
  "resolved" "https://registry.npmmirror.com/@types/svgo/-/svgo-2.6.4.tgz"
  "version" "2.6.4"
  dependencies:
    "@types/node" "*"

"@types/web-bluetooth@^0.0.16":
  "integrity" "sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ=="
  "resolved" "https://registry.npmmirror.com/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz"
  "version" "0.0.16"

"@typescript-eslint/eslint-plugin@^5.32.0":
  "integrity" "sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag=="
  "resolved" "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.62.0.tgz"
  "version" "5.62.0"
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    "debug" "^4.3.4"
    "graphemer" "^1.4.0"
    "ignore" "^5.2.0"
    "natural-compare-lite" "^1.4.0"
    "semver" "^7.3.7"
    "tsutils" "^3.21.0"

"@typescript-eslint/parser@^5.0.0", "@typescript-eslint/parser@^5.32.0":
  "integrity" "sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA=="
  "resolved" "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-5.62.0.tgz"
  "version" "5.62.0"
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    "debug" "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  "integrity" "sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w=="
  "resolved" "https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-5.62.0.tgz"
  "version" "5.62.0"
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  "integrity" "sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew=="
  "resolved" "https://registry.npmmirror.com/@typescript-eslint/type-utils/-/type-utils-5.62.0.tgz"
  "version" "5.62.0"
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    "debug" "^4.3.4"
    "tsutils" "^3.21.0"

"@typescript-eslint/types@5.62.0":
  "integrity" "sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ=="
  "resolved" "https://registry.npmmirror.com/@typescript-eslint/types/-/types-5.62.0.tgz"
  "version" "5.62.0"

"@typescript-eslint/typescript-estree@5.62.0":
  "integrity" "sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA=="
  "resolved" "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-5.62.0.tgz"
  "version" "5.62.0"
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    "debug" "^4.3.4"
    "globby" "^11.1.0"
    "is-glob" "^4.0.3"
    "semver" "^7.3.7"
    "tsutils" "^3.21.0"

"@typescript-eslint/utils@5.62.0":
  "integrity" "sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ=="
  "resolved" "https://registry.npmmirror.com/@typescript-eslint/utils/-/utils-5.62.0.tgz"
  "version" "5.62.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    "eslint-scope" "^5.1.1"
    "semver" "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  "integrity" "sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw=="
  "resolved" "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-5.62.0.tgz"
  "version" "5.62.0"
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "eslint-visitor-keys" "^3.3.0"

"@ungap/structured-clone@^1.2.0":
  "integrity" "sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ=="
  "resolved" "https://registry.npmmirror.com/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
  "version" "1.2.0"

"@uppy/companion-client@^2.2.2":
  "integrity" "sha512-5mTp2iq97/mYSisMaBtFRry6PTgZA6SIL7LePteOV5x0/DxKfrZW3DEiQERJmYpHzy7k8johpm2gHnEKto56Og=="
  "resolved" "https://registry.npmmirror.com/@uppy/companion-client/-/companion-client-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "@uppy/utils" "^4.1.2"
    "namespace-emitter" "^2.0.1"

"@uppy/core@^2.0.3", "@uppy/core@^2.1.1", "@uppy/core@^2.1.4", "@uppy/core@^2.3.3":
  "integrity" "sha512-iWAqppC8FD8mMVqewavCz+TNaet6HPXitmGXpGGREGrakZ4FeuWytVdrelydzTdXx6vVKkOmI2FLztGg73sENQ=="
  "resolved" "https://registry.npmmirror.com/@uppy/core/-/core-2.3.4.tgz"
  "version" "2.3.4"
  dependencies:
    "@transloadit/prettier-bytes" "0.0.7"
    "@uppy/store-default" "^2.1.1"
    "@uppy/utils" "^4.1.3"
    "lodash.throttle" "^4.1.1"
    "mime-match" "^1.0.2"
    "namespace-emitter" "^2.0.1"
    "nanoid" "^3.1.25"
    "preact" "^10.5.13"

"@uppy/store-default@^2.1.1":
  "integrity" "sha512-xnpTxvot2SeAwGwbvmJ899ASk5tYXhmZzD/aCFsXePh/v8rNvR2pKlcQUH7cF/y4baUGq3FHO/daKCok/mpKqQ=="
  "resolved" "https://registry.npmmirror.com/@uppy/store-default/-/store-default-2.1.1.tgz"
  "version" "2.1.1"

"@uppy/utils@^4.1.2", "@uppy/utils@^4.1.3":
  "integrity" "sha512-nTuMvwWYobnJcytDO3t+D6IkVq/Qs4Xv3vyoEZ+Iaf8gegZP+rEyoaFT2CK5XLRMienPyqRqNbIfRuFaOWSIFw=="
  "resolved" "https://registry.npmmirror.com/@uppy/utils/-/utils-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "lodash.throttle" "^4.1.1"

"@uppy/xhr-upload@^2.0.3", "@uppy/xhr-upload@^2.0.7":
  "integrity" "sha512-YWOQ6myBVPs+mhNjfdWsQyMRWUlrDLMoaG7nvf/G6Y3GKZf8AyjFDjvvJ49XWQ+DaZOftGkHmF1uh/DBeGivJQ=="
  "resolved" "https://registry.npmmirror.com/@uppy/xhr-upload/-/xhr-upload-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "@uppy/companion-client" "^2.2.2"
    "@uppy/utils" "^4.1.2"
    "nanoid" "^3.1.25"

"@vitejs/plugin-vue@^3.0.0":
  "integrity" "sha512-E0tnaL4fr+qkdCNxJ+Xd0yM31UwMkQje76fsDVBBUCoGOUPexu2VDUYHL8P4CwV+zMvWw6nlRw19OnRKmYAJpw=="
  "resolved" "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-3.2.0.tgz"
  "version" "3.2.0"

"@volar/code-gen@0.38.9":
  "integrity" "sha512-n6LClucfA+37rQeskvh9vDoZV1VvCVNy++MAPKj2dT4FT+Fbmty/SDQqnsEBtdEe6E3OQctFvA/IcKsx3Mns0A=="
  "resolved" "https://registry.npmmirror.com/@volar/code-gen/-/code-gen-0.38.9.tgz"
  "version" "0.38.9"
  dependencies:
    "@volar/source-map" "0.38.9"

"@volar/source-map@0.38.9":
  "integrity" "sha512-ba0UFoHDYry+vwKdgkWJ6xlQT+8TFtZg1zj9tSjj4PykW1JZDuM0xplMotLun4h3YOoYfY9K1huY5gvxmrNLIw=="
  "resolved" "https://registry.npmmirror.com/@volar/source-map/-/source-map-0.38.9.tgz"
  "version" "0.38.9"

"@volar/vue-code-gen@0.38.9":
  "integrity" "sha512-tzj7AoarFBKl7e41MR006ncrEmNPHALuk8aG4WdDIaG387X5//5KhWC5Ff3ZfB2InGSeNT+CVUd74M0gS20rjA=="
  "resolved" "https://registry.npmmirror.com/@volar/vue-code-gen/-/vue-code-gen-0.38.9.tgz"
  "version" "0.38.9"
  dependencies:
    "@volar/code-gen" "0.38.9"
    "@volar/source-map" "0.38.9"
    "@vue/compiler-core" "^3.2.37"
    "@vue/compiler-dom" "^3.2.37"
    "@vue/shared" "^3.2.37"

"@volar/vue-typescript@0.38.9":
  "integrity" "sha512-iJMQGU91ADi98u8V1vXd2UBmELDAaeSP0ZJaFjwosClQdKlJQYc6MlxxKfXBZisHqfbhdtrGRyaryulnYtliZw=="
  "resolved" "https://registry.npmmirror.com/@volar/vue-typescript/-/vue-typescript-0.38.9.tgz"
  "version" "0.38.9"
  dependencies:
    "@volar/code-gen" "0.38.9"
    "@volar/source-map" "0.38.9"
    "@volar/vue-code-gen" "0.38.9"
    "@vue/compiler-sfc" "^3.2.37"
    "@vue/reactivity" "^3.2.37"

"@vue/compiler-core@^3.2.37", "@vue/compiler-core@3.4.4":
  "integrity" "sha512-U5AdCN+6skzh2bSJrkMj2KZsVkUpgK8/XlxjSRYQZhNPcvt9/kmgIMpFEiTyK+Dz5E1J+8o8//BEIX+bakgVSw=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@babel/parser" "^7.23.6"
    "@vue/shared" "3.4.4"
    "entities" "^4.5.0"
    "estree-walker" "^2.0.2"
    "source-map-js" "^1.0.2"

"@vue/compiler-dom@^3.2.37", "@vue/compiler-dom@3.4.4":
  "integrity" "sha512-iSwkdDULCN+Vr8z6uwdlL044GJ/nUmECxP9vu7MzEs4Qma0FwDLYvnvRcyO0ZITuu3Os4FptGUDnhi1kOLSaGw=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@vue/compiler-core" "3.4.4"
    "@vue/shared" "3.4.4"

"@vue/compiler-sfc@^3.2.29", "@vue/compiler-sfc@^3.2.37", "@vue/compiler-sfc@^3.3.8", "@vue/compiler-sfc@3.4.4":
  "integrity" "sha512-OTFcU6vUxUNHBcarzkp4g6d25nvcmDvFDzPRvSrIsByFFPRYN+y3b+j9HxYwt6nlWvGyFCe0roeJdJlfYxbCBg=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@babel/parser" "^7.23.6"
    "@vue/compiler-core" "3.4.4"
    "@vue/compiler-dom" "3.4.4"
    "@vue/compiler-ssr" "3.4.4"
    "@vue/shared" "3.4.4"
    "estree-walker" "^2.0.2"
    "magic-string" "^0.30.5"
    "postcss" "^8.4.32"
    "source-map-js" "^1.0.2"

"@vue/compiler-ssr@3.4.4":
  "integrity" "sha512-1DU9DflSSQlx/M61GEBN+NbT/anUki2ooDo9IXfTckCeKA/2IKNhY8KbG3x6zkd3KGrxzteC7de6QL88vEb41Q=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@vue/compiler-dom" "3.4.4"
    "@vue/shared" "3.4.4"

"@vue/devtools-api@^6.0.0-beta.11", "@vue/devtools-api@^6.5.0":
  "integrity" "sha512-+KpckaAQyfbvshdDW5xQylLni1asvNSGme1JFs8I1+/H5pHEhqUKMEQD/qn3Nx5+/nycBq11qAEi8lk+LXI2dA=="
  "resolved" "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.5.1.tgz"
  "version" "6.5.1"

"@vue/reactivity@^3.2.37", "@vue/reactivity@3.4.4":
  "integrity" "sha512-DFsuJBf6sfhd5SYzJmcBTUG9+EKqjF31Gsk1NJtnpJm9liSZ806XwGJUeNBVQIanax7ODV7Lmk/k17BgxXNuTg=="
  "resolved" "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@vue/shared" "3.4.4"

"@vue/runtime-core@3.4.4":
  "integrity" "sha512-zWWwNQAj5JdxrmOA1xegJm+c4VtyIbDEKgQjSb4va5v7gGTCh0ZjvLI+htGFdVXaO9bs2J3C81p5p+6jrPK8Bw=="
  "resolved" "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@vue/reactivity" "3.4.4"
    "@vue/shared" "3.4.4"

"@vue/runtime-dom@3.4.4":
  "integrity" "sha512-Nlh2ap1J/eJQ6R0g+AIRyGNwpTJQACN0dk8I8FRLH8Ev11DSvfcPOpn4+Kbg5xAMcuq0cHB8zFYxVrOgETrrvg=="
  "resolved" "https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@vue/runtime-core" "3.4.4"
    "@vue/shared" "3.4.4"
    "csstype" "^3.1.3"

"@vue/server-renderer@3.4.4":
  "integrity" "sha512-+AjoiKcC41k7SMJBYkDO9xs79/Of8DiThS9mH5l2MK+EY0to3psI0k+sElvVqQvsoZTjHMEuMz0AEgvm2T+CwA=="
  "resolved" "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@vue/compiler-ssr" "3.4.4"
    "@vue/shared" "3.4.4"

"@vue/shared@^3.2.37", "@vue/shared@3.4.4":
  "integrity" "sha512-abSgiVRhfjfl3JALR/cSuBl74hGJ3SePgf1mKzodf1eMWLwHZbfEGxT2cNJSsNiw44jEgrO7bNkhchaWA7RwNw=="
  "resolved" "https://registry.npmmirror.com/@vue/shared/-/shared-3.4.4.tgz"
  "version" "3.4.4"

"@vueup/vue-quill@alpha":
  "integrity" "sha512-Cn6li+LxOiPQSvmZE+F9jKGsemksQq2VDfTBdgFIlNfNv3+BcBN+DblQstj9NDV5BBF97BHmM2dk7E84yYJI+Q=="
  "resolved" "https://registry.npmmirror.com/@vueup/vue-quill/-/vue-quill-1.0.0-alpha.40.tgz"
  "version" "1.0.0-alpha.40"
  dependencies:
    "quill" "^1.3.7"
    "quill-delta" "^3.6.3"

"@vueuse/core@*", "@vueuse/core@^9.1.0", "@vueuse/core@^9.1.1":
  "integrity" "sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw=="
  "resolved" "https://registry.npmmirror.com/@vueuse/core/-/core-9.13.0.tgz"
  "version" "9.13.0"
  dependencies:
    "@types/web-bluetooth" "^0.0.16"
    "@vueuse/metadata" "9.13.0"
    "@vueuse/shared" "9.13.0"
    "vue-demi" "*"

"@vueuse/metadata@9.13.0":
  "integrity" "sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ=="
  "resolved" "https://registry.npmmirror.com/@vueuse/metadata/-/metadata-9.13.0.tgz"
  "version" "9.13.0"

"@vueuse/shared@9.13.0":
  "integrity" "sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw=="
  "resolved" "https://registry.npmmirror.com/@vueuse/shared/-/shared-9.13.0.tgz"
  "version" "9.13.0"
  dependencies:
    "vue-demi" "*"

"@wangeditor/basic-modules@^1.1.7", "@wangeditor/basic-modules@1.x":
  "integrity" "sha512-cY9CPkLJaqF05STqfpZKWG4LpxTMeGSIIF1fHvfm/mz+JXatCagjdkbxdikOuKYlxDdeqvOeBmsUBItufDLXZg=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/basic-modules/-/basic-modules-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "is-url" "^1.2.4"

"@wangeditor/code-highlight@^1.0.3":
  "integrity" "sha512-iazHwO14XpCuIWJNTQTikqUhGKyqj+dUNWJ9288Oym9M2xMVHvnsOmDU2sgUDWVy+pOLojReMPgXCsvvNlOOhw=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/code-highlight/-/code-highlight-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "prismjs" "^1.23.0"

"@wangeditor/core@^1.1.19", "@wangeditor/core@1.x":
  "integrity" "sha512-KevkB47+7GhVszyYF2pKGKtCSj/YzmClsD03C3zTt+9SR2XWT5T0e3yQqg8baZpcMvkjs1D8Dv4fk8ok/UaS2Q=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/core/-/core-1.1.19.tgz"
  "version" "1.1.19"
  dependencies:
    "@types/event-emitter" "^0.3.3"
    "event-emitter" "^0.3.5"
    "html-void-elements" "^2.0.0"
    "i18next" "^20.4.0"
    "scroll-into-view-if-needed" "^2.2.28"
    "slate-history" "^0.66.0"

"@wangeditor/editor-for-vue@^5.1.12":
  "integrity" "sha512-0Ds3D8I+xnpNWezAeO7HmPRgTfUxHLMd9JKcIw+QzvSmhC5xUHbpCcLU+KLmeBKTR/zffnS5GQo6qi3GhTMJWQ=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/editor-for-vue/-/editor-for-vue-5.1.12.tgz"
  "version" "5.1.12"

"@wangeditor/editor@^5.1.14", "@wangeditor/editor@>=5.1.0":
  "integrity" "sha512-0RxfeVTuK1tktUaPROnCoFfaHVJpRAIE2zdS0mpP+vq1axVQpLjM8+fCvKzqYIkH0Pg+C+44hJpe3VVroSkEuQ=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/editor/-/editor-5.1.23.tgz"
  "version" "5.1.23"
  dependencies:
    "@uppy/core" "^2.1.1"
    "@uppy/xhr-upload" "^2.0.3"
    "@wangeditor/basic-modules" "^1.1.7"
    "@wangeditor/code-highlight" "^1.0.3"
    "@wangeditor/core" "^1.1.19"
    "@wangeditor/list-module" "^1.0.5"
    "@wangeditor/table-module" "^1.1.4"
    "@wangeditor/upload-image-module" "^1.0.2"
    "@wangeditor/video-module" "^1.1.4"
    "dom7" "^3.0.0"
    "is-hotkey" "^0.2.0"
    "lodash.camelcase" "^4.3.0"
    "lodash.clonedeep" "^4.5.0"
    "lodash.debounce" "^4.0.8"
    "lodash.foreach" "^4.5.0"
    "lodash.isequal" "^4.5.0"
    "lodash.throttle" "^4.1.1"
    "lodash.toarray" "^4.4.0"
    "nanoid" "^3.2.0"
    "slate" "^0.72.0"
    "snabbdom" "^3.1.0"

"@wangeditor/list-module@^1.0.5":
  "integrity" "sha512-uDuYTP6DVhcYf7mF1pTlmNn5jOb4QtcVhYwSSAkyg09zqxI1qBqsfUnveeDeDqIuptSJhkh81cyxi+MF8sEPOQ=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/list-module/-/list-module-1.0.5.tgz"
  "version" "1.0.5"

"@wangeditor/table-module@^1.1.4":
  "integrity" "sha512-5saanU9xuEocxaemGdNi9t8MCDSucnykEC6jtuiT72kt+/Hhh4nERYx1J20OPsTCCdVr7hIyQenFD1iSRkIQ6w=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/table-module/-/table-module-1.1.4.tgz"
  "version" "1.1.4"

"@wangeditor/upload-image-module@^1.0.2":
  "integrity" "sha512-z81lk/v71OwPDYeQDxj6cVr81aDP90aFuywb8nPD6eQeECtOymrqRODjpO6VGvCVxVck8nUxBHtbxKtjgcwyiA=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/upload-image-module/-/upload-image-module-1.0.2.tgz"
  "version" "1.0.2"

"@wangeditor/video-module@^1.1.4":
  "integrity" "sha512-ZdodDPqKQrgx3IwWu4ZiQmXI8EXZ3hm2/fM6E3t5dB8tCaIGWQZhmqd6P5knfkRAd3z2+YRSRbxOGfoRSp/rLg=="
  "resolved" "https://registry.npmmirror.com/@wangeditor/video-module/-/video-module-1.1.4.tgz"
  "version" "1.1.4"

"acorn-jsx@^5.3.2":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^8.1.1":
  "integrity" "sha512-TgUZgYvqZprrl7YldZNoa9OciCAyZR+Ejm9eXzKCmjsF5IKp/wgQ7Z/ZpjpGTIUPwrHQIcYeI8qDh4PsEwxMbw=="
  "resolved" "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.3.1.tgz"
  "version" "8.3.1"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8.10.0", "acorn@^8.4.1", "acorn@^8.7.1", "acorn@^8.8.0", "acorn@^8.8.1", "acorn@^8.9.0":
  "integrity" "sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg=="
  "resolved" "https://registry.npmmirror.com/acorn/-/acorn-8.11.3.tgz"
  "version" "8.11.3"

"adler-32@~1.3.0":
  "integrity" "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A=="
  "resolved" "https://registry.npmmirror.com/adler-32/-/adler-32-1.3.1.tgz"
  "version" "1.3.1"

"ajv@^6.12.4":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.11.0":
  "integrity" "sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA=="
  "resolved" "https://registry.npmmirror.com/ajv/-/ajv-8.12.0.tgz"
  "version" "8.12.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ansi-escapes@^4.2.1":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-regex@^2.0.0":
  "integrity" "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA=="
  "resolved" "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^2.2.1":
  "integrity" "sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"archiver-utils@^2.1.0":
  "integrity" "sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw=="
  "resolved" "https://registry.npmmirror.com/archiver-utils/-/archiver-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "glob" "^7.1.4"
    "graceful-fs" "^4.2.0"
    "lazystream" "^1.0.0"
    "lodash.defaults" "^4.2.0"
    "lodash.difference" "^4.5.0"
    "lodash.flatten" "^4.4.0"
    "lodash.isplainobject" "^4.0.6"
    "lodash.union" "^4.6.0"
    "normalize-path" "^3.0.0"
    "readable-stream" "^2.0.0"

"archiver-utils@^3.0.4":
  "integrity" "sha512-KVgf4XQVrTjhyWmx6cte4RxonPLR9onExufI1jhvw/MQ4BB6IsZD5gT8Lq+u/+pRkWna/6JoHpiQioaqFP5Rzw=="
  "resolved" "https://registry.npmmirror.com/archiver-utils/-/archiver-utils-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "glob" "^7.2.3"
    "graceful-fs" "^4.2.0"
    "lazystream" "^1.0.0"
    "lodash.defaults" "^4.2.0"
    "lodash.difference" "^4.5.0"
    "lodash.flatten" "^4.4.0"
    "lodash.isplainobject" "^4.0.6"
    "lodash.union" "^4.6.0"
    "normalize-path" "^3.0.0"
    "readable-stream" "^3.6.0"

"archiver@^5.0.0":
  "integrity" "sha512-+25nxyyznAXF7Nef3y0EbBeqmGZgeN/BxHX29Rs39djAfaFalmQ89SE6CWyDCHzGL0yt/ycBtNOmGTW0FyGWNw=="
  "resolved" "https://registry.npmmirror.com/archiver/-/archiver-5.3.2.tgz"
  "version" "5.3.2"
  dependencies:
    "archiver-utils" "^2.1.0"
    "async" "^3.2.4"
    "buffer-crc32" "^0.2.1"
    "readable-stream" "^3.6.0"
    "readdir-glob" "^1.1.2"
    "tar-stream" "^2.2.0"
    "zip-stream" "^4.1.0"

"arg@^4.1.0":
  "integrity" "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA=="
  "resolved" "https://registry.npmmirror.com/arg/-/arg-4.1.3.tgz"
  "version" "4.1.3"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"arr-diff@^4.0.0":
  "integrity" "sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA=="
  "resolved" "https://registry.npmmirror.com/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="
  "resolved" "https://registry.npmmirror.com/arr-flatten/-/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q=="
  "resolved" "https://registry.npmmirror.com/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-ify@^1.0.0":
  "integrity" "sha512-c5AMf34bKdvPhQ7tBGhqkgKNUzMr4WUs+WDtC2ZUGOUncbxKMTvqxYctiseW3+L4bA8ec+GcZ6/A/FW4m8ukng=="
  "resolved" "https://registry.npmmirror.com/array-ify/-/array-ify-1.0.0.tgz"
  "version" "1.0.0"

"array-union@^2.1.0":
  "integrity" "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw=="
  "resolved" "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz"
  "version" "2.1.0"

"array-unique@^0.3.2":
  "integrity" "sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ=="
  "resolved" "https://registry.npmmirror.com/array-unique/-/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"arrify@^1.0.1":
  "integrity" "sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA=="
  "resolved" "https://registry.npmmirror.com/arrify/-/arrify-1.0.1.tgz"
  "version" "1.0.1"

"assign-symbols@^1.0.0":
  "integrity" "sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw=="
  "resolved" "https://registry.npmmirror.com/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"async-validator@^4.2.5":
  "integrity" "sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg=="
  "resolved" "https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz"
  "version" "4.2.5"

"async@^3.2.4":
  "integrity" "sha512-baNZyqaaLhyLVKm/DlvdW051MSgO6b8eVfIezl9E5PqWxFgzLm/wQntEW4zOytVburDEr0JlALEpdOFwvErLsg=="
  "resolved" "https://registry.npmmirror.com/async/-/async-3.2.5.tgz"
  "version" "3.2.5"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"at-least-node@^1.0.0":
  "integrity" "sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg=="
  "resolved" "https://registry.npmmirror.com/at-least-node/-/at-least-node-1.0.0.tgz"
  "version" "1.0.0"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmmirror.com/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"axios@^0.27.2":
  "integrity" "sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ=="
  "resolved" "https://registry.npmmirror.com/axios/-/axios-0.27.2.tgz"
  "version" "0.27.2"
  dependencies:
    "follow-redirects" "^1.14.9"
    "form-data" "^4.0.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg=="
  "resolved" "https://registry.npmmirror.com/base/-/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-arraybuffer@^1.0.2":
  "integrity" "sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ=="
  "resolved" "https://registry.npmmirror.com/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  "version" "1.0.2"

"base64-js@^1.3.1":
  "integrity" "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="
  "resolved" "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"big-integer@^1.6.17":
  "integrity" "sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg=="
  "resolved" "https://registry.npmmirror.com/big-integer/-/big-integer-1.6.52.tgz"
  "version" "1.6.52"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmmirror.com/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^2.0.0":
  "integrity" "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="
  "resolved" "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"binary@~0.3.0":
  "integrity" "sha512-D4H1y5KYwpJgK8wk1Cue5LLPgmwHKYSChkbspQg5JtVuR5ulGckxfR62H3AE9UDkdMC8yyXlqYihuz3Aqg2XZg=="
  "resolved" "https://registry.npmmirror.com/binary/-/binary-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "buffers" "~0.1.1"
    "chainsaw" "~0.1.0"

"bl@^4.0.3", "bl@^4.1.0":
  "integrity" "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w=="
  "resolved" "https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer" "^5.5.0"
    "inherits" "^2.0.4"
    "readable-stream" "^3.4.0"

"bluebird@^3.5.0":
  "integrity" "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="
  "resolved" "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bluebird@~3.4.1":
  "integrity" "sha512-iD3898SR7sWVRHbiQv+sHUtHnMvC1o3nW5rAcqnq3uOn07DSAppZYUkIGslDz6gXC7HfunPe7YVBgoEJASPcHA=="
  "resolved" "https://registry.npmmirror.com/bluebird/-/bluebird-3.4.7.tgz"
  "version" "3.4.7"

"boolbase@^1.0.0":
  "integrity" "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww=="
  "resolved" "https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace-expansion@^2.0.1":
  "integrity" "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA=="
  "resolved" "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "balanced-match" "^1.0.0"

"braces@^2.2.2":
  "integrity" "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="
  "resolved" "https://registry.npmmirror.com/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.2", "braces@~3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"btoa@^1.2.1":
  "integrity" "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g=="
  "resolved" "https://registry.npmmirror.com/btoa/-/btoa-1.2.1.tgz"
  "version" "1.2.1"

"buffer-crc32@^0.2.1", "buffer-crc32@^0.2.13":
  "integrity" "sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ=="
  "resolved" "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
  "version" "0.2.13"

"buffer-indexof-polyfill@~1.0.0":
  "integrity" "sha512-I7wzHwA3t1/lwXQh+A5PbNvJxgfo5r3xulgpYDB5zckTu/Z9oUK9biouBKQUjEqzaz3HnAT6TYoovmE+GqSf7A=="
  "resolved" "https://registry.npmmirror.com/buffer-indexof-polyfill/-/buffer-indexof-polyfill-1.0.2.tgz"
  "version" "1.0.2"

"buffer@^5.5.0":
  "integrity" "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="
  "resolved" "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"buffers@~0.1.1":
  "integrity" "sha512-9q/rDEGSb/Qsvv2qvzIzdluL5k7AaJOTrw23z9reQthrbF7is4CtlT0DXyO1oei2DCp4uojjzQ7igaSHp1kAEQ=="
  "resolved" "https://registry.npmmirror.com/buffers/-/buffers-0.1.1.tgz"
  "version" "0.1.1"

"cache-base@^1.0.1":
  "integrity" "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ=="
  "resolved" "https://registry.npmmirror.com/cache-base/-/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cachedir@2.3.0":
  "integrity" "sha512-A+Fezp4zxnit6FanDmv9EqXNAi3vt9DWp51/71UEhXukb7QUuvtv9344h91dyAxuTLoSYJFU299qzR3tzwPAhw=="
  "resolved" "https://registry.npmmirror.com/cachedir/-/cachedir-2.3.0.tgz"
  "version" "2.3.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha512-C3nQxfFZxFRVoJoGKKI8y3MOEo129NQ+FgQ08iye+Mk4zNZZGdjfs06bVTr+DBSlA66Q2VEcMki/cUCP4SercQ=="
  "resolved" "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.1"
    "set-function-length" "^1.1.1"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@^4.1.2":
  "integrity" "sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw=="
  "resolved" "https://registry.npmmirror.com/camel-case/-/camel-case-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "pascal-case" "^3.1.2"
    "tslib" "^2.0.3"

"camelcase-keys@^6.2.2":
  "integrity" "sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg=="
  "resolved" "https://registry.npmmirror.com/camelcase-keys/-/camelcase-keys-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "camelcase" "^5.3.1"
    "map-obj" "^4.0.0"
    "quick-lru" "^4.0.1"

"camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"canvg@^3.0.6":
  "integrity" "sha512-qwR2FRNO9NlzTeKIPIKpnTY6fqwuYSequ8Ru8c0YkYU7U0oW+hLUvWadLvAu1Rl72OMNiFhoLu4f8eUjQ7l/+Q=="
  "resolved" "https://registry.npmmirror.com/canvg/-/canvg-3.0.10.tgz"
  "version" "3.0.10"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    "core-js" "^3.8.3"
    "raf" "^3.4.1"
    "regenerator-runtime" "^0.13.7"
    "rgbcolor" "^1.0.1"
    "stackblur-canvas" "^2.0.0"
    "svg-pathdata" "^6.0.3"

"capital-case@^1.0.4":
  "integrity" "sha512-ds37W8CytHgwnhGGTi88pcPyR15qoNkOpYwmMMfnWqqWgESapLqvDx6huFjQ5vqWSn2Z06173XNA7LtMOeUh1A=="
  "resolved" "https://registry.npmmirror.com/capital-case/-/capital-case-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"
    "upper-case-first" "^2.0.2"

"cfb@~1.2.1":
  "integrity" "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA=="
  "resolved" "https://registry.npmmirror.com/cfb/-/cfb-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "adler-32" "~1.3.0"
    "crc-32" "~1.2.0"

"chainsaw@~0.1.0":
  "integrity" "sha512-75kWfWt6MEKNC8xYXIdRpDehRYY/tNSgwKaJq+dbbDcxORuVrrQ+SEHoWsniVn9XPYfP4gmdWIeDk/4YNp1rNQ=="
  "resolved" "https://registry.npmmirror.com/chainsaw/-/chainsaw-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "traverse" ">=0.3.0 <0.4"

"chalk@^1.1.3":
  "integrity" "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.4.1":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.4.2":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^4.0.0", "chalk@^4.1.0", "chalk@^4.1.1", "chalk@^4.1.2":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"change-case@^4.1.2":
  "integrity" "sha512-bSxY2ws9OtviILG1EiY5K7NNxkqg/JnRnFxLtKQ96JaviiIxi7djMrSd0ECT9AC+lttClmYwKw53BWpOMblo7A=="
  "resolved" "https://registry.npmmirror.com/change-case/-/change-case-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "camel-case" "^4.1.2"
    "capital-case" "^1.0.4"
    "constant-case" "^3.0.4"
    "dot-case" "^3.0.4"
    "header-case" "^2.0.4"
    "no-case" "^3.0.4"
    "param-case" "^3.0.4"
    "pascal-case" "^3.1.2"
    "path-case" "^3.0.4"
    "sentence-case" "^3.0.4"
    "snake-case" "^3.0.4"
    "tslib" "^2.0.3"

"chardet@^0.7.0":
  "integrity" "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA=="
  "resolved" "https://registry.npmmirror.com/chardet/-/chardet-0.7.0.tgz"
  "version" "0.7.0"

"chokidar@^3.5.3", "chokidar@>=2.0.0 <4.0.0", "chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw=="
  "resolved" "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"class-utils@^0.3.5":
  "integrity" "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg=="
  "resolved" "https://registry.npmmirror.com/class-utils/-/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"cli-cursor@^3.1.0":
  "integrity" "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw=="
  "resolved" "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-spinners@^2.5.0":
  "integrity" "sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg=="
  "resolved" "https://registry.npmmirror.com/cli-spinners/-/cli-spinners-2.9.2.tgz"
  "version" "2.9.2"

"cli-width@^3.0.0":
  "integrity" "sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw=="
  "resolved" "https://registry.npmmirror.com/cli-width/-/cli-width-3.0.0.tgz"
  "version" "3.0.0"

"clipboard@^2.0.10":
  "integrity" "sha512-C+0bbOqkezLIsmWSvlsXS0Q0bmkugu7jcfMIACB+RDEntIzQIkdr148we28AfSloQLRdZlYL/QYyrq05j/3Faw=="
  "resolved" "https://registry.npmmirror.com/clipboard/-/clipboard-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "good-listener" "^1.2.2"
    "select" "^1.1.2"
    "tiny-emitter" "^2.0.0"

"cliui@^8.0.1":
  "integrity" "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ=="
  "resolved" "https://registry.npmmirror.com/cliui/-/cliui-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.1"
    "wrap-ansi" "^7.0.0"

"clone@^1.0.2":
  "integrity" "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg=="
  "resolved" "https://registry.npmmirror.com/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w=="
  "resolved" "https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"codemirror@^5.65.9":
  "integrity" "sha512-br21LjYmSlVL0vFCPWPfhzUCT34FM/pAdK7rRIZwa0rrtrIdotvP4Oh4GUHsu2E3IrQMCfRkL/fN3ytMNxVQvg=="
  "resolved" "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.16.tgz"
  "version" "5.65.16"

"codepage@~1.15.0":
  "integrity" "sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA=="
  "resolved" "https://registry.npmmirror.com/codepage/-/codepage-1.15.0.tgz"
  "version" "1.15.0"

"collection-visit@^1.0.0":
  "integrity" "sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw=="
  "resolved" "https://registry.npmmirror.com/collection-visit/-/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.20.3":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^7.2.0":
  "integrity" "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw=="
  "resolved" "https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz"
  "version" "7.2.0"

"commitizen@^4.0.3", "commitizen@^4.2.5":
  "integrity" "sha512-H0iNtClNEhT0fotHvGV3E9tDejDeS04sN1veIebsKYGMuGscFaswRoYJKmT3eW85eIJAs0F28bG2+a/9wCOfPw=="
  "resolved" "https://registry.npmmirror.com/commitizen/-/commitizen-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "cachedir" "2.3.0"
    "cz-conventional-changelog" "3.3.0"
    "dedent" "0.7.0"
    "detect-indent" "6.1.0"
    "find-node-modules" "^2.1.2"
    "find-root" "1.1.0"
    "fs-extra" "9.1.0"
    "glob" "7.2.3"
    "inquirer" "8.2.5"
    "is-utf8" "^0.2.1"
    "lodash" "4.17.21"
    "minimist" "1.2.7"
    "strip-bom" "4.0.0"
    "strip-json-comments" "3.1.1"

"compare-func@^2.0.0":
  "integrity" "sha512-zHig5N+tPWARooBnb0Zx1MFcdfpyJrfTJ3Y5L+IFvUm8rM74hHz66z0gw0x4tijh5CorKkKUCnW82R2vmpeCRA=="
  "resolved" "https://registry.npmmirror.com/compare-func/-/compare-func-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "array-ify" "^1.0.0"
    "dot-prop" "^5.1.0"

"component-emitter@^1.2.1":
  "integrity" "sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ=="
  "resolved" "https://registry.npmmirror.com/component-emitter/-/component-emitter-1.3.1.tgz"
  "version" "1.3.1"

"compress-commons@^4.1.2":
  "integrity" "sha512-D3uMHtGc/fcO1Gt1/L7i1e33VOvD4A9hfQLP+6ewd+BvG/gQ84Yh4oftEhAdjSMgBgwGL+jsppT7JYNpo6MHHg=="
  "resolved" "https://registry.npmmirror.com/compress-commons/-/compress-commons-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "buffer-crc32" "^0.2.13"
    "crc32-stream" "^4.0.2"
    "normalize-path" "^3.0.0"
    "readable-stream" "^3.6.0"

"compute-scroll-into-view@^1.0.20":
  "integrity" "sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg=="
  "resolved" "https://registry.npmmirror.com/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz"
  "version" "1.0.20"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"consola@^2.15.3":
  "integrity" "sha512-9vAdYbHj6x2fLKC4+oPH0kFzY/orMZyG2Aj+kNylHxKGJ/Ed4dpNyAQYwJOdqO4zdM7XpVHmyejQDcQHrnuXbw=="
  "resolved" "https://registry.npmmirror.com/consola/-/consola-2.15.3.tgz"
  "version" "2.15.3"

"console@^0.7.2":
  "integrity" "sha512-+JSDwGunA4MTEgAV/4VBKwUHonP8CzJ/6GIuwPi6acKFqFfHUdSGCm89ZxZ5FfGWdZfkdgAroy5bJ5FSeN/t4g=="
  "resolved" "https://registry.npmmirror.com/console/-/console-0.7.2.tgz"
  "version" "0.7.2"

"constant-case@^3.0.4":
  "integrity" "sha512-I2hSBi7Vvs7BEuJDr5dDHfzb/Ruj3FyvFyh7KLilAjNQw3Be+xgqUBA2W6scVEcL0hL1dwPRtIqEPVUCKkSsyQ=="
  "resolved" "https://registry.npmmirror.com/constant-case/-/constant-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"
    "upper-case" "^2.0.2"

"conventional-changelog-angular@^6.0.0":
  "integrity" "sha512-6qLgrBF4gueoC7AFVHu51nHL9pF9FRjXrH+ceVf7WmAfH3gs+gEYOkvxhjMPjZu57I4AGUGoNTY8V7Hrgf1uqg=="
  "resolved" "https://registry.npmmirror.com/conventional-changelog-angular/-/conventional-changelog-angular-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "compare-func" "^2.0.0"

"conventional-changelog-conventionalcommits@^6.1.0":
  "integrity" "sha512-3cS3GEtR78zTfMzk0AizXKKIdN4OvSh7ibNz6/DPbhWWQu7LqE/8+/GqSodV+sywUR2gpJAdP/1JFf4XtN7Zpw=="
  "resolved" "https://registry.npmmirror.com/conventional-changelog-conventionalcommits/-/conventional-changelog-conventionalcommits-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "compare-func" "^2.0.0"

"conventional-commit-types@^3.0.0":
  "integrity" "sha512-SmmCYnOniSsAa9GqWOeLqc179lfr5TRu5b4QFDkbsrJ5TZjPJx85wtOr3zn+1dbeNiXDKGPbZ72IKbPhLXh/Lg=="
  "resolved" "https://registry.npmmirror.com/conventional-commit-types/-/conventional-commit-types-3.0.0.tgz"
  "version" "3.0.0"

"conventional-commits-parser@^4.0.0":
  "integrity" "sha512-WRv5j1FsVM5FISJkoYMR6tPk07fkKT0UodruX4je86V4owk451yjXAKzKAPOs9l7y59E2viHUS9eQ+dfUA9NSg=="
  "resolved" "https://registry.npmmirror.com/conventional-commits-parser/-/conventional-commits-parser-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-text-path" "^1.0.1"
    "JSONStream" "^1.3.5"
    "meow" "^8.1.2"
    "split2" "^3.2.2"

"copy-descriptor@^0.1.0":
  "integrity" "sha512-XgZ0pFcakEUlbwQEVNg3+QAis1FyTL3Qel9FYy8pSkQqoG3PNoT0bOCQtOXcOkur21r2Eq2kI+IE+gsmAEVlYw=="
  "resolved" "https://registry.npmmirror.com/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"core-js@^3.6.0", "core-js@^3.6.5", "core-js@^3.8.3":
  "integrity" "sha512-ntakECeqg81KqMueeGJ79Q5ZgQNR+6eaE8sxGCx62zMbAIj65q+uYvatToew3m6eAGdU4gNZwpZ34NMe4GYswg=="
  "resolved" "https://registry.npmmirror.com/core-js/-/core-js-3.35.0.tgz"
  "version" "3.35.0"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cors@^2.8.5":
  "integrity" "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g=="
  "resolved" "https://registry.npmmirror.com/cors/-/cors-2.8.5.tgz"
  "version" "2.8.5"
  dependencies:
    "object-assign" "^4"
    "vary" "^1"

"cosmiconfig-typescript-loader@^4.0.0":
  "integrity" "sha512-BabizFdC3wBHhbI4kJh0VkQP9GkBfoHPydD0COMce1nJ1kJAB3F2TmJ/I7diULBKtmEWSwEbuN/KDtgnmUUVmw=="
  "resolved" "https://registry.npmmirror.com/cosmiconfig-typescript-loader/-/cosmiconfig-typescript-loader-4.4.0.tgz"
  "version" "4.4.0"

"cosmiconfig@^8.0.0", "cosmiconfig@>=7":
  "integrity" "sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA=="
  "resolved" "https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  "version" "8.3.6"
  dependencies:
    "import-fresh" "^3.3.0"
    "js-yaml" "^4.1.0"
    "parse-json" "^5.2.0"
    "path-type" "^4.0.0"

"crc-32@^1.2.0", "crc-32@~1.2.0", "crc-32@~1.2.1":
  "integrity" "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ=="
  "resolved" "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.2.tgz"
  "version" "1.2.2"

"crc32-stream@^4.0.2":
  "integrity" "sha512-NT7w2JVU7DFroFdYkeq8cywxrgjPHWkdX1wjpRQXPX5Asews3tA+Ght6lddQO5Mkumffp3X7GEqku3epj2toIw=="
  "resolved" "https://registry.npmmirror.com/crc32-stream/-/crc32-stream-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "crc-32" "^1.2.0"
    "readable-stream" "^3.4.0"

"create-require@^1.1.0":
  "integrity" "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ=="
  "resolved" "https://registry.npmmirror.com/create-require/-/create-require-1.1.1.tgz"
  "version" "1.1.1"

"cropperjs@^1.5.6":
  "integrity" "sha512-F4wsi+XkDHCOMrHMYjrTEE4QBOrsHHN5/2VsVAaRq8P7E5z7xQpT75S+f/9WikmBEailas3+yo+6zPIomW+NOA=="
  "resolved" "https://registry.npmmirror.com/cropperjs/-/cropperjs-1.6.1.tgz"
  "version" "1.6.1"

"cross-spawn@^7.0.2", "cross-spawn@^7.0.3":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"css-line-break@^2.1.0":
  "integrity" "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w=="
  "resolved" "https://registry.npmmirror.com/css-line-break/-/css-line-break-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "utrie" "^1.0.2"

"css-select@^4.1.3":
  "integrity" "sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ=="
  "resolved" "https://registry.npmmirror.com/css-select/-/css-select-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^6.0.1"
    "domhandler" "^4.3.1"
    "domutils" "^2.8.0"
    "nth-check" "^2.0.1"

"css-tree@^1.1.2", "css-tree@^1.1.3":
  "integrity" "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q=="
  "resolved" "https://registry.npmmirror.com/css-tree/-/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-what@^6.0.1":
  "integrity" "sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw=="
  "resolved" "https://registry.npmmirror.com/css-what/-/css-what-6.1.0.tgz"
  "version" "6.1.0"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssfilter@0.0.10":
  "integrity" "sha512-FAaLDaplstoRsDR8XGYH51znUN0UY7nMc6Z9/fvE8EXGwvJE9hu7W2vHwx1+bd6gCYnln9nLbzxFTrcO9YQDZw=="
  "resolved" "https://registry.npmmirror.com/cssfilter/-/cssfilter-0.0.10.tgz"
  "version" "0.0.10"

"csso@^4.2.0":
  "integrity" "sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA=="
  "resolved" "https://registry.npmmirror.com/csso/-/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"csstype@^3.1.3":
  "integrity" "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="
  "resolved" "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz"
  "version" "3.1.3"

"cz-conventional-changelog@3.3.0":
  "integrity" "sha512-U466fIzU5U22eES5lTNiNbZ+d8dfcHcssH4o7QsdWaCcRs/feIPCxKYSWkYBNs5mny7MvEfwpTLWjvbm94hecw=="
  "resolved" "https://registry.npmmirror.com/cz-conventional-changelog/-/cz-conventional-changelog-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "chalk" "^2.4.1"
    "commitizen" "^4.0.3"
    "conventional-commit-types" "^3.0.0"
    "lodash.map" "^4.5.1"
    "longest" "^2.0.1"
    "word-wrap" "^1.0.3"
  optionalDependencies:
    "@commitlint/load" ">6.1.1"

"cz-git@^1.3.12":
  "integrity" "sha512-XL5nXiqbW10YswQqWODrbwyWhydw3mLhP8OBrXyl8u4sIjTf/6mhGsizd6FA2VgPc4tZaNGguFoUyDj7vIBouw=="
  "resolved" "https://registry.npmmirror.com/cz-git/-/cz-git-1.8.0.tgz"
  "version" "1.8.0"

"czg@^1.3.12":
  "integrity" "sha512-vLX3AIUaE30C/qkeuL+g1fcdjZ72vBdON12E4V4vmuTKKe7OMoQ2MxBYbWhl5OP28+ggCrajxGBk6bdjIn5LgQ=="
  "resolved" "https://registry.npmmirror.com/czg/-/czg-1.8.0.tgz"
  "version" "1.8.0"

"d@^1.0.1", "d@1":
  "integrity" "sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA=="
  "resolved" "https://registry.npmmirror.com/d/-/d-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "es5-ext" "^0.10.50"
    "type" "^1.0.1"

"dargs@^7.0.0":
  "integrity" "sha512-2iy1EkLdlBzQGvbweYRFxmFath8+K7+AKB0TlhHWkNuH+TmovaMH/Wp7V7R4u7f4SnX3OgLsU9t1NI9ioDnUpg=="
  "resolved" "https://registry.npmmirror.com/dargs/-/dargs-7.0.0.tgz"
  "version" "7.0.0"

"dart-sass@^1.25.0":
  "integrity" "sha512-syNOAstJXAmvD3RifcDk3fiPMyYE2fY8so6w9gf2/wNlKpG0zyH+oiXubEYVOy1WAWkzOc72pbAxwx+3OU4JJA=="
  "resolved" "https://registry.npmmirror.com/dart-sass/-/dart-sass-1.25.0.tgz"
  "version" "1.25.0"
  dependencies:
    "chokidar" ">=2.0.0 <4.0.0"

"dayjs@^1.11.3", "dayjs@^1.11.4", "dayjs@^1.8.34":
  "integrity" "sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ=="
  "resolved" "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.10.tgz"
  "version" "1.11.10"

"debug@^2.2.0":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^4.1.1", "debug@^4.3.2", "debug@^4.3.3", "debug@^4.3.4":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"decamelize-keys@^1.1.0":
  "integrity" "sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg=="
  "resolved" "https://registry.npmmirror.com/decamelize-keys/-/decamelize-keys-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "decamelize" "^1.1.0"
    "map-obj" "^1.0.0"

"decamelize@^1.1.0":
  "integrity" "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="
  "resolved" "https://registry.npmmirror.com/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ=="
  "resolved" "https://registry.npmmirror.com/decode-uri-component/-/decode-uri-component-0.2.2.tgz"
  "version" "0.2.2"

"decompress-response@^6.0.0":
  "integrity" "sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ=="
  "resolved" "https://registry.npmmirror.com/decompress-response/-/decompress-response-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "mimic-response" "^3.1.0"

"dedent@0.7.0":
  "integrity" "sha512-Q6fKUPqnAHAyhiUgFU7BUzLiv0kd8saH9al7tnu5Q/okj6dnupxyTgFIBjVzJATdfIAm9NAsvXNzjaKa+bxVyA=="
  "resolved" "https://registry.npmmirror.com/dedent/-/dedent-0.7.0.tgz"
  "version" "0.7.0"

"deep-equal@^1.0.1":
  "integrity" "sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg=="
  "resolved" "https://registry.npmmirror.com/deep-equal/-/deep-equal-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-arguments" "^1.1.1"
    "is-date-object" "^1.0.5"
    "is-regex" "^1.1.4"
    "object-is" "^1.1.5"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.5.1"

"deep-is@^0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@1.3.2":
  "integrity" "sha512-qjMjTrk+RKv/sp4RPDpV5CnKhxjFI9p+GkLBOls5A8EEElldYWCWA9zceAkmfd0xIo2aU1nxiaLFoiya2sb6Cg=="
  "resolved" "https://registry.npmmirror.com/deepmerge/-/deepmerge-1.3.2.tgz"
  "version" "1.3.2"

"defaults@^1.0.3":
  "integrity" "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A=="
  "resolved" "https://registry.npmmirror.com/defaults/-/defaults-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "clone" "^1.0.2"

"define-data-property@^1.0.1", "define-data-property@^1.1.1":
  "integrity" "sha512-E7uGkTzkk1d0ByLeSc6ZsFS79Axg+m1P/VsgYsxHgiuc3tFSj+MjMIwe90FC4lOAZzNBdY7kkO2P2wKdsQ1vgQ=="
  "resolved" "https://registry.npmmirror.com/define-data-property/-/define-data-property-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "get-intrinsic" "^1.2.1"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.0"

"define-properties@^1.1.3", "define-properties@^1.2.0":
  "integrity" "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg=="
  "resolved" "https://registry.npmmirror.com/define-properties/-/define-properties-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "define-data-property" "^1.0.1"
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"define-property@^0.2.5":
  "integrity" "sha512-Rr7ADjQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA=="
  "resolved" "https://registry.npmmirror.com/define-property/-/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA=="
  "resolved" "https://registry.npmmirror.com/define-property/-/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ=="
  "resolved" "https://registry.npmmirror.com/define-property/-/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegate@^3.1.2":
  "integrity" "sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw=="
  "resolved" "https://registry.npmmirror.com/delegate/-/delegate-3.2.0.tgz"
  "version" "3.2.0"

"detect-file@^1.0.0":
  "integrity" "sha512-DtCOLG98P007x7wiiOmfI0fi3eIKyWiLTGJ2MDnVi/E04lWGbf+JzrRHMm0rgIIZJGtHpKpbVgLWHrv8xXpc3Q=="
  "resolved" "https://registry.npmmirror.com/detect-file/-/detect-file-1.0.0.tgz"
  "version" "1.0.0"

"detect-indent@6.1.0":
  "integrity" "sha512-reYkTUJAZb9gUuZ2RvVCNhVHdg62RHnJ7WJl8ftMi4diZ6NWlciOzQN88pUhSELEwflJht4oQDv0F0BMlwaYtA=="
  "resolved" "https://registry.npmmirror.com/detect-indent/-/detect-indent-6.1.0.tgz"
  "version" "6.1.0"

"diff@^4.0.1":
  "integrity" "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A=="
  "resolved" "https://registry.npmmirror.com/diff/-/diff-4.0.2.tgz"
  "version" "4.0.2"

"dir-glob@^3.0.1":
  "integrity" "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA=="
  "resolved" "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"doctrine@^3.0.0":
  "integrity" "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w=="
  "resolved" "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-serializer@^1.0.1":
  "integrity" "sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag=="
  "resolved" "https://registry.npmmirror.com/dom-serializer/-/dom-serializer-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"dom-serializer@0":
  "integrity" "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g=="
  "resolved" "https://registry.npmmirror.com/dom-serializer/-/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"dom7@^3.0.0":
  "integrity" "sha512-oNlcUdHsC4zb7Msx7JN3K0Nro1dzJ48knvBOnDPKJ2GV9wl1i5vydJZUSyOfrkKFDZEud/jBsTk92S/VGSAe/g=="
  "resolved" "https://registry.npmmirror.com/dom7/-/dom7-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ssr-window" "^3.0.0-alpha.1"

"domelementtype@^1.3.1", "domelementtype@1":
  "integrity" "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w=="
  "resolved" "https://registry.npmmirror.com/domelementtype/-/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="
  "resolved" "https://registry.npmmirror.com/domelementtype/-/domelementtype-2.3.0.tgz"
  "version" "2.3.0"

"domhandler@^2.3.0":
  "integrity" "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA=="
  "resolved" "https://registry.npmmirror.com/domhandler/-/domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"domhandler@^4.2.0", "domhandler@^4.3.1":
  "integrity" "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ=="
  "resolved" "https://registry.npmmirror.com/domhandler/-/domhandler-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "domelementtype" "^2.2.0"

"dompurify@^2.2.0":
  "integrity" "sha512-kxxKlPEDa6Nc5WJi+qRgPbOAbgTpSULL+vI3NUXsZMlkJxTqYI9wg5ZTay2sFrdZRWHPWNi+EdAhcJf81WtoMQ=="
  "resolved" "https://registry.npmmirror.com/dompurify/-/dompurify-2.4.7.tgz"
  "version" "2.4.7"

"domready@1.0.8":
  "integrity" "sha512-uIzsOJUNk+AdGE9a6VDeessoMCzF8RrZvJCX/W8QtyfgdR6Uofn/MvRonih3OtCO79b2VDzDOymuiABrQ4z3XA=="
  "resolved" "https://registry.npmmirror.com/domready/-/domready-1.0.8.tgz"
  "version" "1.0.8"

"domutils@^1.5.1":
  "integrity" "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg=="
  "resolved" "https://registry.npmmirror.com/domutils/-/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@^2.8.0":
  "integrity" "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A=="
  "resolved" "https://registry.npmmirror.com/domutils/-/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"dot-case@^3.0.4":
  "integrity" "sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w=="
  "resolved" "https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"

"dot-prop@^5.1.0":
  "integrity" "sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q=="
  "resolved" "https://registry.npmmirror.com/dot-prop/-/dot-prop-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "is-obj" "^2.0.0"

"duplexer2@~0.1.4":
  "integrity" "sha512-asLFVfWWtJ90ZyOUHMqk7/S2w2guQKxUI2itj3d92ADHhxUSbCMGi1f1cBcJ7xM1To+pE/Khbwo1yuNbMEPKeA=="
  "resolved" "https://registry.npmmirror.com/duplexer2/-/duplexer2-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "readable-stream" "^2.0.2"

"echarts@^5.3.1":
  "integrity" "sha512-mYKxLxhzy6zyTi/FaEbJMOZU1ULGEQHaeIeuMR5L+JnJTpz+YR03mnnpBhbR4+UYJAgiXgpyTVLffPAjOTLkZA=="
  "resolved" "https://registry.npmmirror.com/echarts/-/echarts-5.4.3.tgz"
  "version" "5.4.3"
  dependencies:
    "tslib" "2.3.0"
    "zrender" "5.4.4"

"element-in-view@^0.1.0":
  "integrity" "sha512-bMX/nPADfY8Mgs70kG+SGJ4vo7GCOsQaFVhUHG738gzRG6OrKCp2ZbV63LE5jsgEh8tDqxvXvLeslel5HF6lrA=="
  "resolved" "https://registry.npmmirror.com/element-in-view/-/element-in-view-0.1.0.tgz"
  "version" "0.1.0"

"element-plus@^2.4.2":
  "integrity" "sha512-TlKubXJgxwhER0dw+8ULn9hr9kZjraV4R6Q/eidwWUwCKxwXYPBGmMKsZ/85tlxlhMYbcLZd/YZh6G3QkHX4fg=="
  "resolved" "https://registry.npmmirror.com/element-plus/-/element-plus-2.4.4.tgz"
  "version" "2.4.4"
  dependencies:
    "@ctrl/tinycolor" "^3.4.1"
    "@element-plus/icons-vue" "^2.3.1"
    "@floating-ui/dom" "^1.0.1"
    "@popperjs/core" "npm:@sxzz/popperjs-es@^2.11.7"
    "@types/lodash" "^4.14.182"
    "@types/lodash-es" "^4.17.6"
    "@vueuse/core" "^9.1.0"
    "async-validator" "^4.2.5"
    "dayjs" "^1.11.3"
    "escape-html" "^1.0.3"
    "lodash" "^4.17.21"
    "lodash-es" "^4.17.21"
    "lodash-unified" "^1.0.2"
    "memoize-one" "^6.0.0"
    "normalize-wheel-es" "^1.2.0"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://registry.npmmirror.com/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"end-of-stream@^1.4.1":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"entities@^1.1.1":
  "integrity" "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w=="
  "resolved" "https://registry.npmmirror.com/entities/-/entities-1.1.2.tgz"
  "version" "1.1.2"

"entities@^2.0.0":
  "integrity" "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A=="
  "resolved" "https://registry.npmmirror.com/entities/-/entities-2.2.0.tgz"
  "version" "2.2.0"

"entities@^4.5.0":
  "integrity" "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="
  "resolved" "https://registry.npmmirror.com/entities/-/entities-4.5.0.tgz"
  "version" "4.5.0"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"es-module-lexer@^0.9.3":
  "integrity" "sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ=="
  "resolved" "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-0.9.3.tgz"
  "version" "0.9.3"

"es5-ext@^0.10.35", "es5-ext@^0.10.50", "es5-ext@~0.10.14":
  "integrity" "sha512-BHLqn0klhEpnOKSrzn/Xsz2UIW8j+cGmo9JLzr8BiUapV8hPL9+FliFqjwr9ngW7jWdnxv6eO+/LqyhJVqgrjA=="
  "resolved" "https://registry.npmmirror.com/es5-ext/-/es5-ext-0.10.62.tgz"
  "version" "0.10.62"
  dependencies:
    "es6-iterator" "^2.0.3"
    "es6-symbol" "^3.1.3"
    "next-tick" "^1.1.0"

"es6-iterator@^2.0.3":
  "integrity" "sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g=="
  "resolved" "https://registry.npmmirror.com/es6-iterator/-/es6-iterator-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "d" "1"
    "es5-ext" "^0.10.35"
    "es6-symbol" "^3.1.1"

"es6-symbol@^3.1.1", "es6-symbol@^3.1.3":
  "integrity" "sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA=="
  "resolved" "https://registry.npmmirror.com/es6-symbol/-/es6-symbol-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "d" "^1.0.1"
    "ext" "^1.1.2"

"esbuild-windows-64@0.15.18":
  "integrity" "sha512-qinug1iTTaIIrCorAUjR0fcBk24fjzEedFYhhispP8Oc7SFvs+XeW3YpAKiKp8dRpizl4YYAhxMjlftAMJiaUw=="
  "resolved" "https://registry.npmmirror.com/esbuild-windows-64/-/esbuild-windows-64-0.15.18.tgz"
  "version" "0.15.18"

"esbuild@^0.15.9", "esbuild@>=0.13":
  "integrity" "sha512-x/R72SmW3sSFRm5zrrIjAhCeQSAWoni3CmHEqfQrZIQTM3lVCdehdwuIqaOtfC2slvpdlLa62GYoN8SxT23m6Q=="
  "resolved" "https://registry.npmmirror.com/esbuild/-/esbuild-0.15.18.tgz"
  "version" "0.15.18"
  optionalDependencies:
    "@esbuild/android-arm" "0.15.18"
    "@esbuild/linux-loong64" "0.15.18"
    "esbuild-android-64" "0.15.18"
    "esbuild-android-arm64" "0.15.18"
    "esbuild-darwin-64" "0.15.18"
    "esbuild-darwin-arm64" "0.15.18"
    "esbuild-freebsd-64" "0.15.18"
    "esbuild-freebsd-arm64" "0.15.18"
    "esbuild-linux-32" "0.15.18"
    "esbuild-linux-64" "0.15.18"
    "esbuild-linux-arm" "0.15.18"
    "esbuild-linux-arm64" "0.15.18"
    "esbuild-linux-mips64le" "0.15.18"
    "esbuild-linux-ppc64le" "0.15.18"
    "esbuild-linux-riscv64" "0.15.18"
    "esbuild-linux-s390x" "0.15.18"
    "esbuild-netbsd-64" "0.15.18"
    "esbuild-openbsd-64" "0.15.18"
    "esbuild-sunos-64" "0.15.18"
    "esbuild-windows-32" "0.15.18"
    "esbuild-windows-64" "0.15.18"
    "esbuild-windows-arm64" "0.15.18"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "https://registry.npmmirror.com/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@^1.0.3":
  "integrity" "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="
  "resolved" "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^1.0.5":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"escape-string-regexp@^5.0.0":
  "integrity" "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz"
  "version" "5.0.0"

"escape-string-regexp@1.0.5":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"eslint-config-prettier@^8.5.0":
  "integrity" "sha512-SM8AMJdeQqRYT9O9zguiruQZaN7+z+E4eAP9oiLNGKMtomwaB1E9dcgUD6ZAn/eQAb52USbvezbiljfZUhbJcg=="
  "resolved" "https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.10.0.tgz"
  "version" "8.10.0"

"eslint-plugin-prettier@^4.2.1":
  "integrity" "sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ=="
  "resolved" "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-vue@^9.3.0":
  "integrity" "sha512-CPDqTOG2K4Ni2o4J5wixkLVNwgctKXFu6oBpVJlpNq7f38lh9I80pRTouZSJ2MAebPJlINU/KTFSXyQfBUlymA=="
  "resolved" "https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-9.19.2.tgz"
  "version" "9.19.2"
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    "natural-compare" "^1.4.0"
    "nth-check" "^2.1.1"
    "postcss-selector-parser" "^6.0.13"
    "semver" "^7.5.4"
    "vue-eslint-parser" "^9.3.1"
    "xml-name-validator" "^4.0.0"

"eslint-scope@^5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-scope@^7.1.1":
  "integrity" "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg=="
  "resolved" "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.2.tgz"
  "version" "7.2.2"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-scope@^7.2.2":
  "integrity" "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg=="
  "resolved" "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.2.tgz"
  "version" "7.2.2"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-visitor-keys@^3.3.0":
  "integrity" "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  "version" "3.4.3"

"eslint-visitor-keys@^3.4.1":
  "integrity" "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  "version" "3.4.3"

"eslint-visitor-keys@^3.4.3":
  "integrity" "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  "version" "3.4.3"

"eslint@*", "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^6.2.0 || ^7.0.0 || ^8.0.0", "eslint@^8.21.0", "eslint@>=6.0.0", "eslint@>=7.0.0", "eslint@>=7.28.0":
  "integrity" "sha512-Go19xM6T9puCOWntie1/P997aXxFsOi37JIHRWI514Hc6ZnaHGKY9xFhrU65RT6CcBEzZoGG1e6Nq+DT04ZtZQ=="
  "resolved" "https://registry.npmmirror.com/eslint/-/eslint-8.56.0.tgz"
  "version" "8.56.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.56.0"
    "@humanwhocodes/config-array" "^0.11.13"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    "ajv" "^6.12.4"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.3.2"
    "doctrine" "^3.0.0"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^7.2.2"
    "eslint-visitor-keys" "^3.4.3"
    "espree" "^9.6.1"
    "esquery" "^1.4.2"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "find-up" "^5.0.0"
    "glob-parent" "^6.0.2"
    "globals" "^13.19.0"
    "graphemer" "^1.4.0"
    "ignore" "^5.2.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "is-path-inside" "^3.0.3"
    "js-yaml" "^4.1.0"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.1.2"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.3"
    "strip-ansi" "^6.0.1"
    "text-table" "^0.2.0"

"espree@^9.3.1", "espree@^9.6.0", "espree@^9.6.1":
  "integrity" "sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ=="
  "resolved" "https://registry.npmmirror.com/espree/-/espree-9.6.1.tgz"
  "version" "9.6.1"
  dependencies:
    "acorn" "^8.9.0"
    "acorn-jsx" "^5.3.2"
    "eslint-visitor-keys" "^3.4.1"

"esquery@^1.4.0", "esquery@^1.4.2":
  "integrity" "sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg=="
  "resolved" "https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0", "estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estree-walker@^2.0.1", "estree-walker@^2.0.2":
  "integrity" "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="
  "resolved" "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz"
  "version" "2.0.2"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@^1.8.1":
  "integrity" "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg=="
  "resolved" "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-emitter@^0.3.5":
  "integrity" "sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA=="
  "resolved" "https://registry.npmmirror.com/event-emitter/-/event-emitter-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"

"eventemitter3@^2.0.3":
  "integrity" "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg=="
  "resolved" "https://registry.npmmirror.com/eventemitter3/-/eventemitter3-2.0.3.tgz"
  "version" "2.0.3"

"exceljs@^4.3.0":
  "integrity" "sha512-XctvKaEMaj1Ii9oDOqbW/6e1gXknSY4g/aLCDicOXqBE4M0nRWkUu0PTp++UPNzoFY12BNHMfs/VadKIS6llvg=="
  "resolved" "https://registry.npmmirror.com/exceljs/-/exceljs-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "archiver" "^5.0.0"
    "dayjs" "^1.8.34"
    "fast-csv" "^4.3.1"
    "jszip" "^3.10.1"
    "readable-stream" "^3.6.0"
    "saxes" "^5.0.1"
    "tmp" "^0.2.0"
    "unzipper" "^0.10.11"
    "uuid" "^8.3.0"

"execa@^5.0.0":
  "integrity" "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg=="
  "resolved" "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"expand-brackets@^2.1.4":
  "integrity" "sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA=="
  "resolved" "https://registry.npmmirror.com/expand-brackets/-/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"expand-tilde@^2.0.0", "expand-tilde@^2.0.2":
  "integrity" "sha512-A5EmesHW6rfnZ9ysHQjPdJRni0SRar0tjtG5MNtm9n5TUvsYU8oozprtRD4AqHxcZWWlVuAmQo2nWKfN9oyjTw=="
  "resolved" "https://registry.npmmirror.com/expand-tilde/-/expand-tilde-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "homedir-polyfill" "^1.0.1"

"ext@^1.1.2":
  "integrity" "sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw=="
  "resolved" "https://registry.npmmirror.com/ext/-/ext-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "type" "^2.7.2"

"extend-shallow@^2.0.1":
  "integrity" "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug=="
  "resolved" "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0":
  "integrity" "sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q=="
  "resolved" "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend-shallow@^3.0.2":
  "integrity" "sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q=="
  "resolved" "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@^3.0.2":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@^3.0.3":
  "integrity" "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew=="
  "resolved" "https://registry.npmmirror.com/external-editor/-/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "^0.7.0"
    "iconv-lite" "^0.4.24"
    "tmp" "^0.0.33"

"extglob@^2.0.2":
  "integrity" "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw=="
  "resolved" "https://registry.npmmirror.com/extglob/-/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"fast-csv@^4.3.1":
  "integrity" "sha512-2RNSpuwwsJGP0frGsOmTb9oUF+VkFSM4SyLTDgwf2ciHWTarN0lQTC+F2f/t5J9QjW+c65VFIAAu85GsvMIusw=="
  "resolved" "https://registry.npmmirror.com/fast-csv/-/fast-csv-4.3.6.tgz"
  "version" "4.3.6"
  dependencies:
    "@fast-csv/format" "4.3.5"
    "@fast-csv/parse" "4.3.6"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw=="
  "resolved" "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz"
  "version" "1.3.0"

"fast-diff@1.1.2":
  "integrity" "sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig=="
  "resolved" "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.1.2.tgz"
  "version" "1.1.2"

"fast-glob@^3.2.11", "fast-glob@^3.2.12", "fast-glob@^3.2.9":
  "integrity" "sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow=="
  "resolved" "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha512-ifCoaXsDrsdkWTtiNJX5uzHDsrck5TzfKKDcuFFTIrrc/BS076qgEIfoIy1VeZqViznfKiysPYTh/QeHtnIsYA=="
  "resolved" "https://registry.npmmirror.com/fastq/-/fastq-1.16.0.tgz"
  "version" "1.16.0"
  dependencies:
    "reusify" "^1.0.4"

"fflate@^0.4.8":
  "integrity" "sha512-FJqqoDBR00Mdj9ppamLa/Y7vxm+PRmNWA67N846RvsoYVMKB4q3y/de5PA7gUmRMYK/8CMz2GDZQmCRN1wBcWA=="
  "resolved" "https://registry.npmmirror.com/fflate/-/fflate-0.4.8.tgz"
  "version" "0.4.8"

"figures@^3.0.0":
  "integrity" "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg=="
  "resolved" "https://registry.npmmirror.com/figures/-/figures-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^6.0.1":
  "integrity" "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg=="
  "resolved" "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"file-saver@^2.0.5":
  "integrity" "sha512-P9bmyZ3h/PRG+Nzga+rbdI4OEpNDzAVyy74uVO9ATgzLK6VtAsYybF/+TOCvrc0MO793d6+42lLyZTw7/ArVzA=="
  "resolved" "https://registry.npmmirror.com/file-saver/-/file-saver-2.0.5.tgz"
  "version" "2.0.5"

"fill-range@^4.0.0":
  "integrity" "sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ=="
  "resolved" "https://registry.npmmirror.com/fill-range/-/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"find-node-modules@^2.1.2":
  "integrity" "sha512-UC2I2+nx1ZuOBclWVNdcnbDR5dlrOdVb7xNjmT/lHE+LsgztWks3dG7boJ37yTS/venXw84B/mAW9uHVoC5QRg=="
  "resolved" "https://registry.npmmirror.com/find-node-modules/-/find-node-modules-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "findup-sync" "^4.0.0"
    "merge" "^2.1.1"

"find-root@1.1.0":
  "integrity" "sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng=="
  "resolved" "https://registry.npmmirror.com/find-root/-/find-root-1.1.0.tgz"
  "version" "1.1.0"

"find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"find-up@^5.0.0":
  "integrity" "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng=="
  "resolved" "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"findup-sync@^4.0.0":
  "integrity" "sha512-6jvvn/12IC4quLBL1KNokxC7wWTvYncaVUYSoxWw7YykPLuRrnv4qdHcSOywOI5RpkOVGeQRtWM8/q+G6W6qfQ=="
  "resolved" "https://registry.npmmirror.com/findup-sync/-/findup-sync-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "detect-file" "^1.0.0"
    "is-glob" "^4.0.0"
    "micromatch" "^4.0.2"
    "resolve-dir" "^1.0.1"

"flat-cache@^3.0.4":
  "integrity" "sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw=="
  "resolved" "https://registry.npmmirror.com/flat-cache/-/flat-cache-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "flatted" "^3.2.9"
    "keyv" "^4.5.3"
    "rimraf" "^3.0.2"

"flatted@^3.2.9":
  "integrity" "sha512-36yxDn5H7OFZQla0/jFJmbIKTdZAQHngCedGxiMmpNfEZM0sdEeT+WczLQrjK6D7o2aiyLYDnkw0R3JK0Qv1RQ=="
  "resolved" "https://registry.npmmirror.com/flatted/-/flatted-3.2.9.tgz"
  "version" "3.2.9"

"follow-redirects@^1.14.9":
  "integrity" "sha512-Cr4D/5wlrb0z9dgERpUL3LrmPKVDsETIJhaCMeDfuFYcqa5bldGV6wBsAN6X/vxlXQtFBMrXdXxdL8CbDTGniw=="
  "resolved" "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.4.tgz"
  "version" "1.15.4"

"for-in@^1.0.2":
  "integrity" "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ=="
  "resolved" "https://registry.npmmirror.com/for-in/-/for-in-1.0.2.tgz"
  "version" "1.0.2"

"form-data@^4.0.0":
  "integrity" "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww=="
  "resolved" "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"frac@~1.1.2":
  "integrity" "sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA=="
  "resolved" "https://registry.npmmirror.com/frac/-/frac-1.1.2.tgz"
  "version" "1.1.2"

"fragment-cache@^0.2.1":
  "integrity" "sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA=="
  "resolved" "https://registry.npmmirror.com/fragment-cache/-/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fs-constants@^1.0.0":
  "integrity" "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow=="
  "resolved" "https://registry.npmmirror.com/fs-constants/-/fs-constants-1.0.0.tgz"
  "version" "1.0.0"

"fs-extra@^10.0.0":
  "integrity" "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ=="
  "resolved" "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-extra@^11.0.0":
  "integrity" "sha512-PmDi3uwK5nFuXh7XDTlVnS17xJS7vW36is2+w3xcv8SVxiB4NyATf4ctkVY5bkSjX0Y4nbvZCq1/EjtEyr9ktw=="
  "resolved" "https://registry.npmmirror.com/fs-extra/-/fs-extra-11.2.0.tgz"
  "version" "11.2.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-extra@9.1.0":
  "integrity" "sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ=="
  "resolved" "https://registry.npmmirror.com/fs-extra/-/fs-extra-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "at-least-node" "^1.0.0"
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs.realpath@^1.0.0":
  "integrity" "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fstream@^1.0.12":
  "integrity" "sha512-WvJ193OHa0GHPEL+AycEJgxvBEwyfRkN1vhjca23OaPVMCaLCXTd5qAu82AjTcgP1UJmytkOKb63Ypde7raDIg=="
  "resolved" "https://registry.npmmirror.com/fstream/-/fstream-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "graceful-fs" "^4.1.2"
    "inherits" "~2.0.0"
    "mkdirp" ">=0.5 0"
    "rimraf" "2"

"function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"functions-have-names@^1.2.3":
  "integrity" "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ=="
  "resolved" "https://registry.npmmirror.com/functions-have-names/-/functions-have-names-1.2.3.tgz"
  "version" "1.2.3"

"fuse.js@^6.4.6", "fuse.js@^6.6.2":
  "integrity" "sha512-cJaJkxCCxC8qIIcPBF9yGxY0W/tVZS3uEISDxhYIdtk8OL93pe+6Zj7LjCqVV4dzbqcriOZ+kQ/NE4RXZHsIGA=="
  "resolved" "https://registry.npmmirror.com/fuse.js/-/fuse.js-6.6.2.tgz"
  "version" "6.6.2"

"get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.3", "get-intrinsic@^1.2.1", "get-intrinsic@^1.2.2":
  "integrity" "sha512-0gSo4ml/0j98Y3lngkFEot/zhiCeWsbYIlZ+uZOVgzLyLaUw7wxUL+nCTP0XJvJg1AXulJRI3UJi8GsbDuxdGA=="
  "resolved" "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "function-bind" "^1.1.2"
    "has-proto" "^1.0.1"
    "has-symbols" "^1.0.3"
    "hasown" "^2.0.0"

"get-stream@^6.0.0":
  "integrity" "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg=="
  "resolved" "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA=="
  "resolved" "https://registry.npmmirror.com/get-value/-/get-value-2.0.6.tgz"
  "version" "2.0.6"

"git-raw-commits@^2.0.11":
  "integrity" "sha512-VnctFhw+xfj8Va1xtfEqCUD2XDrbAPSJx+hSrE5K7fGdjZruW7XV+QOrN7LF/RJyvspRiD2I0asWsxFp0ya26A=="
  "resolved" "https://registry.npmmirror.com/git-raw-commits/-/git-raw-commits-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "dargs" "^7.0.0"
    "lodash" "^4.17.15"
    "meow" "^8.0.0"
    "split2" "^3.0.0"
    "through2" "^4.0.0"

"glob-parent@^5.1.2", "glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob@^7.1.3", "glob@^7.1.4", "glob@^7.2.3", "glob@7.2.3":
  "integrity" "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  "resolved" "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@^8.0.1":
  "integrity" "sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ=="
  "resolved" "https://registry.npmmirror.com/glob/-/glob-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^5.0.1"
    "once" "^1.3.0"

"global-dirs@^0.1.1":
  "integrity" "sha512-NknMLn7F2J7aflwFOlGdNIuCDpN3VGoSoB+aap3KABFWbHVn1TCgFC+np23J8W2BiZbjfEw3BFBycSMv1AFblg=="
  "resolved" "https://registry.npmmirror.com/global-dirs/-/global-dirs-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "ini" "^1.3.4"

"global-modules@^1.0.0":
  "integrity" "sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg=="
  "resolved" "https://registry.npmmirror.com/global-modules/-/global-modules-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "global-prefix" "^1.0.1"
    "is-windows" "^1.0.1"
    "resolve-dir" "^1.0.0"

"global-prefix@^1.0.1":
  "integrity" "sha512-5lsx1NUDHtSjfg0eHlmYvZKv8/nVqX4ckFbM+FrGcQ+04KWcWFo9P5MxPZYSzUvyzmdTbI7Eix8Q4IbELDqzKg=="
  "resolved" "https://registry.npmmirror.com/global-prefix/-/global-prefix-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "expand-tilde" "^2.0.2"
    "homedir-polyfill" "^1.0.1"
    "ini" "^1.3.4"
    "is-windows" "^1.0.1"
    "which" "^1.2.14"

"globals@^13.19.0":
  "integrity" "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ=="
  "resolved" "https://registry.npmmirror.com/globals/-/globals-13.24.0.tgz"
  "version" "13.24.0"
  dependencies:
    "type-fest" "^0.20.2"

"globby@^11.1.0":
  "integrity" "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g=="
  "resolved" "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz"
  "version" "11.1.0"
  dependencies:
    "array-union" "^2.1.0"
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.2.9"
    "ignore" "^5.2.0"
    "merge2" "^1.4.1"
    "slash" "^3.0.0"

"good-listener@^1.2.2":
  "integrity" "sha512-goW1b+d9q/HIwbVYZzZ6SsTr4IgE+WA44A0GmPIQstuOrgsFcT7VEJ48nmr9GaRtNu0XTKacFLGnBPAM6Afouw=="
  "resolved" "https://registry.npmmirror.com/good-listener/-/good-listener-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "delegate" "^3.1.2"

"gopd@^1.0.1":
  "integrity" "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA=="
  "resolved" "https://registry.npmmirror.com/gopd/-/gopd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-intrinsic" "^1.1.3"

"graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.0", "graceful-fs@^4.2.2":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"graphemer@^1.4.0":
  "integrity" "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag=="
  "resolved" "https://registry.npmmirror.com/graphemer/-/graphemer-1.4.0.tgz"
  "version" "1.4.0"

"hard-rejection@^2.1.0":
  "integrity" "sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA=="
  "resolved" "https://registry.npmmirror.com/hard-rejection/-/hard-rejection-2.1.0.tgz"
  "version" "2.1.0"

"has-ansi@^2.0.0":
  "integrity" "sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg=="
  "resolved" "https://registry.npmmirror.com/has-ansi/-/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-flag@^1.0.0":
  "integrity" "sha512-DyYHfIYwAJmjAjSSPKANxI8bFY9YtFrgkAfinBojQ8YJTOuOuav64tMUJv584SES4xl74PmuaevIyaLESHdTAA=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-1.0.0.tgz"
  "version" "1.0.0"

"has-flag@^3.0.0":
  "integrity" "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-property-descriptors@^1.0.0":
  "integrity" "sha512-VsX8eaIewvas0xnvinAe9bw4WfIeODpGYikiWYLH+dma0Jw6KHYqWiWfhQlgOVK8D6PvjubK5Uc4P0iIhIcNVg=="
  "resolved" "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-intrinsic" "^1.2.2"

"has-proto@^1.0.1":
  "integrity" "sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg=="
  "resolved" "https://registry.npmmirror.com/has-proto/-/has-proto-1.0.1.tgz"
  "version" "1.0.1"

"has-symbols@^1.0.2", "has-symbols@^1.0.3":
  "integrity" "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="
  "resolved" "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-tostringtag@^1.0.0":
  "integrity" "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ=="
  "resolved" "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has-value@^0.3.1":
  "integrity" "sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q=="
  "resolved" "https://registry.npmmirror.com/has-value/-/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw=="
  "resolved" "https://registry.npmmirror.com/has-value/-/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha512-J8S0cEdWuQbqD9//tlZxiMuMNmxB8PlEwvYwuxsTmR1G5RXUePEX/SJn7aD0GMLieuZYSwNH0cQuJGwnYunXRQ=="
  "resolved" "https://registry.npmmirror.com/has-values/-/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ=="
  "resolved" "https://registry.npmmirror.com/has-values/-/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"hasown@^2.0.0":
  "integrity" "sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA=="
  "resolved" "https://registry.npmmirror.com/hasown/-/hasown-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "function-bind" "^1.1.2"

"he@^1.1.1":
  "integrity" "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="
  "resolved" "https://registry.npmmirror.com/he/-/he-1.2.0.tgz"
  "version" "1.2.0"

"header-case@^2.0.4":
  "integrity" "sha512-H/vuk5TEEVZwrR0lp2zed9OCo1uAILMlx0JEMgC26rzyJJ3N1v6XkwHHXJQdR2doSjcGPM6OKPYoJgf0plJ11Q=="
  "resolved" "https://registry.npmmirror.com/header-case/-/header-case-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "capital-case" "^1.0.4"
    "tslib" "^2.0.3"

"homedir-polyfill@^1.0.1":
  "integrity" "sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA=="
  "resolved" "https://registry.npmmirror.com/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "parse-passwd" "^1.0.0"

"hosted-git-info@^2.1.4":
  "integrity" "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw=="
  "resolved" "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hosted-git-info@^4.0.1":
  "integrity" "sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA=="
  "resolved" "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "lru-cache" "^6.0.0"

"html-void-elements@^2.0.0":
  "integrity" "sha512-0quDb7s97CfemeJAnW9wC0hw78MtW7NU3hqtCD75g2vFlDLt36llsYD7uB7SUzojLMP24N5IatXf7ylGXiGG9A=="
  "resolved" "https://registry.npmmirror.com/html-void-elements/-/html-void-elements-2.0.1.tgz"
  "version" "2.0.1"

"html2canvas@^1.0.0-rc.5", "html2canvas@^1.4.1":
  "integrity" "sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA=="
  "resolved" "https://registry.npmmirror.com/html2canvas/-/html2canvas-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "css-line-break" "^2.1.0"
    "text-segmentation" "^1.0.3"

"htmlparser2@^3.8.3":
  "integrity" "sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ=="
  "resolved" "https://registry.npmmirror.com/htmlparser2/-/htmlparser2-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "domelementtype" "^1.3.1"
    "domhandler" "^2.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^3.1.1"

"human-signals@^2.1.0":
  "integrity" "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw=="
  "resolved" "https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"i18next@^20.4.0":
  "integrity" "sha512-yCMYTMEJ9ihCwEQQ3phLo7I/Pwycf8uAx+sRHwwk5U9Aui/IZYgQRyMqXafQOw5QQ7DM1Z+WyEXWIqSuJHhG2A=="
  "resolved" "https://registry.npmmirror.com/i18next/-/i18next-20.6.1.tgz"
  "version" "20.6.1"
  dependencies:
    "@babel/runtime" "^7.12.0"

"icons-vue@^1.0.9":
  "integrity" "sha512-9/yH38wV4fRmPq3vncy5pofA0jfNlT8UDzXJOtRLGn8ap8Gy6B9YBO4Unj6pz5jg4MdmN49D6IiqDzqr5Ec2IQ=="
  "resolved" "https://registry.npmmirror.com/icons-vue/-/icons-vue-1.0.9.tgz"
  "version" "1.0.9"
  dependencies:
    "typescript" "^4.4.3"

"iconv-lite@^0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"ieee754@^1.1.13":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ignore@^5.2.0":
  "integrity" "sha512-g7dmpshy+gD7mh88OC9NwSGTKoc3kyLAZQRU1mt53Aw/vnvfXnbC+F/7F7QoYVKbV+KNvJx8wArewKy1vXMtlg=="
  "resolved" "https://registry.npmmirror.com/ignore/-/ignore-5.3.0.tgz"
  "version" "5.3.0"

"image-size@^0.5.1":
  "integrity" "sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ=="
  "resolved" "https://registry.npmmirror.com/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"immediate@~3.0.5":
  "integrity" "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="
  "resolved" "https://registry.npmmirror.com/immediate/-/immediate-3.0.6.tgz"
  "version" "3.0.6"

"immer@^9.0.6":
  "integrity" "sha512-bc4NBHqOqSfRW7POMkHd51LvClaeMXpm8dx0e8oE2GORbq5aRK7Bxl4FyzVLdGtLmvLKL7BTDBG5ACQm4HWjTA=="
  "resolved" "https://registry.npmmirror.com/immer/-/immer-9.0.21.tgz"
  "version" "9.0.21"

"immutable@^4.0.0":
  "integrity" "sha512-fsXeu4J4i6WNWSikpI88v/PcVflZz+6kMhUfIwc5SY+poQRPnaf5V7qds6SUyUN3cVxEzuCab7QIoLOQ+DQ1wA=="
  "resolved" "https://registry.npmmirror.com/immutable/-/immutable-4.3.4.tgz"
  "version" "4.3.4"

"import-fresh@^3.0.0", "import-fresh@^3.2.1", "import-fresh@^3.3.0":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="
  "resolved" "https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"inflight@^1.0.4":
  "integrity" "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.0", "inherits@~2.0.3", "inherits@2":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"ini@^1.3.4":
  "integrity" "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="
  "resolved" "https://registry.npmmirror.com/ini/-/ini-1.3.8.tgz"
  "version" "1.3.8"

"inquirer@8.2.5":
  "integrity" "sha512-QAgPDQMEgrDssk1XiwwHoOGYF9BAbUcc1+j+FhEvaOt8/cKRqyLn0U5qA6F74fGhTMGxf92pOvPBeh29jQJDTQ=="
  "resolved" "https://registry.npmmirror.com/inquirer/-/inquirer-8.2.5.tgz"
  "version" "8.2.5"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.1.1"
    "cli-cursor" "^3.1.0"
    "cli-width" "^3.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.21"
    "mute-stream" "0.0.8"
    "ora" "^5.4.1"
    "run-async" "^2.4.0"
    "rxjs" "^7.5.5"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "through" "^2.3.6"
    "wrap-ansi" "^7.0.0"

"is-accessor-descriptor@^1.0.1":
  "integrity" "sha512-YBUanLI8Yoihw923YeFUS5fs0fF2f5TSFTNiYAAzhhDscDa3lEqYuz1pDOEP5KvX94I9ey3vsqjJcLVFVU+3QA=="
  "resolved" "https://registry.npmmirror.com/is-accessor-descriptor/-/is-accessor-descriptor-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hasown" "^2.0.0"

"is-arguments@^1.1.1":
  "integrity" "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA=="
  "resolved" "https://registry.npmmirror.com/is-arguments/-/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="
  "resolved" "https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://registry.npmmirror.com/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-core-module@^2.13.0", "is-core-module@^2.5.0":
  "integrity" "sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw=="
  "resolved" "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.13.1.tgz"
  "version" "2.13.1"
  dependencies:
    "hasown" "^2.0.0"

"is-data-descriptor@^1.0.1":
  "integrity" "sha512-bc4NlCDiCr28U4aEsQ3Qs2491gVq4V8G7MQyws968ImqjKuYtTJXrl7Vq7jsN7Ly/C3xj5KWFrY7sHNeDkAzXw=="
  "resolved" "https://registry.npmmirror.com/is-data-descriptor/-/is-data-descriptor-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hasown" "^2.0.0"

"is-date-object@^1.0.5":
  "integrity" "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  "resolved" "https://registry.npmmirror.com/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha512-C3grZTvObeN1xud4cRWl366OMXZTj0+HGyk4hvfpx4ZHt1Pb60ANSXqCK7pdOTeUQpRzECBSTphqvD7U+l22Eg=="
  "resolved" "https://registry.npmmirror.com/is-descriptor/-/is-descriptor-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "is-accessor-descriptor" "^1.0.1"
    "is-data-descriptor" "^1.0.1"

"is-descriptor@^1.0.0", "is-descriptor@^1.0.2":
  "integrity" "sha512-JCNNGbwWZEVaSPtS45mdtrneRWJFp07LLmykxeFV5F6oBvNF8vHSfJuJgoT472pSfk+Mf8VnlrspaFBHWM8JAw=="
  "resolved" "https://registry.npmmirror.com/is-descriptor/-/is-descriptor-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "is-accessor-descriptor" "^1.0.1"
    "is-data-descriptor" "^1.0.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw=="
  "resolved" "https://registry.npmmirror.com/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA=="
  "resolved" "https://registry.npmmirror.com/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-hotkey@^0.2.0":
  "integrity" "sha512-UknnZK4RakDmTgz4PI1wIph5yxSs/mvChWs9ifnlXsKuXgWmOkY/hAE0H/k2MIqH0RlRye0i1oC07MCRSD28Mw=="
  "resolved" "https://registry.npmmirror.com/is-hotkey/-/is-hotkey-0.2.0.tgz"
  "version" "0.2.0"

"is-interactive@^1.0.0":
  "integrity" "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w=="
  "resolved" "https://registry.npmmirror.com/is-interactive/-/is-interactive-1.0.0.tgz"
  "version" "1.0.0"

"is-number@^3.0.0":
  "integrity" "sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg=="
  "resolved" "https://registry.npmmirror.com/is-number/-/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^2.0.0":
  "integrity" "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w=="
  "resolved" "https://registry.npmmirror.com/is-obj/-/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-path-inside@^3.0.3":
  "integrity" "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ=="
  "resolved" "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz"
  "version" "3.0.3"

"is-plain-obj@^1.1", "is-plain-obj@^1.1.0":
  "integrity" "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg=="
  "resolved" "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^2.0.3":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-plain-object@^2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-plain-object@^5.0.0":
  "integrity" "sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q=="
  "resolved" "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-5.0.0.tgz"
  "version" "5.0.0"

"is-regex@^1.1.4":
  "integrity" "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  "resolved" "https://registry.npmmirror.com/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-text-path@^1.0.1":
  "integrity" "sha512-xFuJpne9oFz5qDaodwmmG08e3CawH/2ZV8Qqza1Ko7Sk8POWbkRdwIoAWVhqvq0XeUzANEhKo2n0IXUGBm7A/w=="
  "resolved" "https://registry.npmmirror.com/is-text-path/-/is-text-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "text-extensions" "^1.0.0"

"is-unicode-supported@^0.1.0":
  "integrity" "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw=="
  "resolved" "https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  "version" "0.1.0"

"is-url@^1.2.4":
  "integrity" "sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww=="
  "resolved" "https://registry.npmmirror.com/is-url/-/is-url-1.2.4.tgz"
  "version" "1.2.4"

"is-utf8@^0.2.1":
  "integrity" "sha512-rMYPYvCzsXywIsldgLaSoPlw5PfoB/ssr7hY4pLfcodrA5M/eArza1a9VmTiNIBNMjOGr1Ow9mTyU2o69U6U9Q=="
  "resolved" "https://registry.npmmirror.com/is-utf8/-/is-utf8-0.2.1.tgz"
  "version" "0.2.1"

"is-windows@^1.0.1", "is-windows@^1.0.2":
  "integrity" "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="
  "resolved" "https://registry.npmmirror.com/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA=="
  "resolved" "https://registry.npmmirror.com/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^2.1.0":
  "integrity" "sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA=="
  "resolved" "https://registry.npmmirror.com/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0":
  "integrity" "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg=="
  "resolved" "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isobject@^3.0.1":
  "integrity" "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg=="
  "resolved" "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"js-base64@^2.1.9":
  "integrity" "sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ=="
  "resolved" "https://registry.npmmirror.com/js-base64/-/js-base64-2.6.4.tgz"
  "version" "2.6.4"

"js-binary-schema-parser@^2.0.2":
  "integrity" "sha512-xezGJmOb4lk/M1ZZLTR/jaBHQ4gG/lqQnJqdIv4721DMggsa1bDVlHXNeHYogaIEHD9vCRv0fcL4hMA+Coarkg=="
  "resolved" "https://registry.npmmirror.com/js-binary-schema-parser/-/js-binary-schema-parser-2.0.3.tgz"
  "version" "2.0.3"

"js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"json-buffer@3.0.1":
  "integrity" "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ=="
  "resolved" "https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz"
  "version" "3.0.1"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json5@^1.0.1":
  "integrity" "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA=="
  "resolved" "https://registry.npmmirror.com/json5/-/json5-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minimist" "^1.2.0"

"jsonc-parser@^3.2.0":
  "integrity" "sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w=="
  "resolved" "https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.2.0.tgz"
  "version" "3.2.0"

"jsonfile@^6.0.1":
  "integrity" "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ=="
  "resolved" "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "universalify" "^2.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonparse@^1.2.0":
  "integrity" "sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg=="
  "resolved" "https://registry.npmmirror.com/jsonparse/-/jsonparse-1.3.1.tgz"
  "version" "1.3.1"

"JSONStream@^1.3.5":
  "integrity" "sha512-E+iruNOY8VV9s4JEbe1aNEm6MiszPRr/UfcHMz0TQh1BXSxHK+ASV1R6W4HpjBhSeS+54PIsAMCBmwD06LLsqQ=="
  "resolved" "https://registry.npmmirror.com/JSONStream/-/JSONStream-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "jsonparse" "^1.2.0"
    "through" ">=2.2.7 <3"

"jspdf@^2.5.1":
  "integrity" "sha512-hXObxz7ZqoyhxET78+XR34Xu2qFGrJJ2I2bE5w4SM8eFaFEkW2xcGRVUss360fYelwRSid/jT078kbNvmoW0QA=="
  "resolved" "https://registry.npmmirror.com/jspdf/-/jspdf-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "@babel/runtime" "^7.14.0"
    "atob" "^2.1.2"
    "btoa" "^1.2.1"
    "fflate" "^0.4.8"
  optionalDependencies:
    "canvg" "^3.0.6"
    "core-js" "^3.6.0"
    "dompurify" "^2.2.0"
    "html2canvas" "^1.0.0-rc.5"

"jszip@^3.10.1", "jszip@^3.9.1":
  "integrity" "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g=="
  "resolved" "https://registry.npmmirror.com/jszip/-/jszip-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "lie" "~3.3.0"
    "pako" "~1.0.2"
    "readable-stream" "~2.3.6"
    "setimmediate" "^1.0.5"

"keyv@^4.5.3":
  "integrity" "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw=="
  "resolved" "https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz"
  "version" "4.5.4"
  dependencies:
    "json-buffer" "3.0.1"

"kind-of@^3.0.2":
  "integrity" "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "integrity" "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw=="
  "resolved" "https://registry.npmmirror.com/kind-of/-/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.2":
  "integrity" "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="
  "resolved" "https://registry.npmmirror.com/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.2", "kind-of@^6.0.3":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"lazystream@^1.0.0":
  "integrity" "sha512-b94GiNHQNy6JNTrt5w6zNyffMrNkXZb3KTkCZJb2V1xaEGCk093vkZ2jk3tpaeP33/OiXC+WvK9AxUebnf5nbw=="
  "resolved" "https://registry.npmmirror.com/lazystream/-/lazystream-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "readable-stream" "^2.0.5"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"lie@~3.3.0":
  "integrity" "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ=="
  "resolved" "https://registry.npmmirror.com/lie/-/lie-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "immediate" "~3.0.5"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"listenercount@~1.0.1":
  "integrity" "sha512-3mk/Zag0+IJxeDrxSgaDPy4zZ3w05PRZeJNnlWhzFz5OkX49J4krc+A8X2d2M69vGMBEX0uyl8M+W+8gH+kBqQ=="
  "resolved" "https://registry.npmmirror.com/listenercount/-/listenercount-1.0.1.tgz"
  "version" "1.0.1"

"loader-utils@^1.1.0":
  "integrity" "sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg=="
  "resolved" "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"local-pkg@^0.4.2":
  "integrity" "sha512-SFppqq5p42fe2qcZQqqEOiVRXl+WCP1MdT6k7BDEW1j++sp5fIY+/fdRQitvKgB5BrBcmrs5m/L0v2FrU5MY1g=="
  "resolved" "https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.3.tgz"
  "version" "0.4.3"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"locate-path@^6.0.0":
  "integrity" "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw=="
  "resolved" "https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"lodash-es@*", "lodash-es@^4.17.21":
  "integrity" "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw=="
  "resolved" "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz"
  "version" "4.17.21"

"lodash-unified@^1.0.2":
  "integrity" "sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ=="
  "resolved" "https://registry.npmmirror.com/lodash-unified/-/lodash-unified-1.0.3.tgz"
  "version" "1.0.3"

"lodash.camelcase@^4.3.0":
  "integrity" "sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA=="
  "resolved" "https://registry.npmmirror.com/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  "version" "4.3.0"

"lodash.clonedeep@^4.5.0":
  "integrity" "sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ=="
  "resolved" "https://registry.npmmirror.com/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
  "version" "4.5.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow=="
  "resolved" "https://registry.npmmirror.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.defaults@^4.2.0":
  "integrity" "sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ=="
  "resolved" "https://registry.npmmirror.com/lodash.defaults/-/lodash.defaults-4.2.0.tgz"
  "version" "4.2.0"

"lodash.difference@^4.5.0":
  "integrity" "sha512-dS2j+W26TQ7taQBGN8Lbbq04ssV3emRw4NY58WErlTO29pIqS0HmoT5aJ9+TUQ1N3G+JOZSji4eugsWwGp9yPA=="
  "resolved" "https://registry.npmmirror.com/lodash.difference/-/lodash.difference-4.5.0.tgz"
  "version" "4.5.0"

"lodash.escaperegexp@^4.1.2":
  "integrity" "sha512-TM9YBvyC84ZxE3rgfefxUWiQKLilstD6k7PTGt6wfbtXF8ixIJLOL3VYyV/z+ZiPLsVxAsKAFVwWlWeb2Y8Yyw=="
  "resolved" "https://registry.npmmirror.com/lodash.escaperegexp/-/lodash.escaperegexp-4.1.2.tgz"
  "version" "4.1.2"

"lodash.flatten@^4.4.0":
  "integrity" "sha512-C5N2Z3DgnnKr0LOpv/hKCgKdb7ZZwafIrsesve6lmzvZIRZRGaZ/l6Q8+2W7NaT+ZwO3fFlSCzCzrDCFdJfZ4g=="
  "resolved" "https://registry.npmmirror.com/lodash.flatten/-/lodash.flatten-4.4.0.tgz"
  "version" "4.4.0"

"lodash.foreach@^4.5.0":
  "integrity" "sha512-aEXTF4d+m05rVOAUG3z4vZZ4xVexLKZGF0lIxuHZ1Hplpk/3B6Z1+/ICICYRLm7c41Z2xiejbkCkJoTlypoXhQ=="
  "resolved" "https://registry.npmmirror.com/lodash.foreach/-/lodash.foreach-4.5.0.tgz"
  "version" "4.5.0"

"lodash.groupby@^4.6.0":
  "integrity" "sha512-5dcWxm23+VAoz+awKmBaiBvzox8+RqMgFhi7UvX9DHZr2HdxHXM/Wrf8cfKpsW37RNrvtPn6hSwNqurSILbmJw=="
  "resolved" "https://registry.npmmirror.com/lodash.groupby/-/lodash.groupby-4.6.0.tgz"
  "version" "4.6.0"

"lodash.isboolean@^3.0.3":
  "integrity" "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg=="
  "resolved" "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  "version" "3.0.3"

"lodash.isequal@^4.5.0":
  "integrity" "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ=="
  "resolved" "https://registry.npmmirror.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz"
  "version" "4.5.0"

"lodash.isfunction@^3.0.9":
  "integrity" "sha512-AirXNj15uRIMMPihnkInB4i3NHeb4iBtNg9WRWuK2o31S+ePwwNmDPaTL3o7dTJ+VXNZim7rFs4rxN4YU1oUJw=="
  "resolved" "https://registry.npmmirror.com/lodash.isfunction/-/lodash.isfunction-3.0.9.tgz"
  "version" "3.0.9"

"lodash.isnil@^4.0.0":
  "integrity" "sha512-up2Mzq3545mwVnMhTDMdfoG1OurpA/s5t88JmQX809eH3C8491iu2sfKhTfhQtKY78oPNhiaHJUpT/dUDAAtng=="
  "resolved" "https://registry.npmmirror.com/lodash.isnil/-/lodash.isnil-4.0.0.tgz"
  "version" "4.0.0"

"lodash.isplainobject@^4.0.6":
  "integrity" "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="
  "resolved" "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  "version" "4.0.6"

"lodash.isundefined@^3.0.1":
  "integrity" "sha512-MXB1is3s899/cD8jheYYE2V9qTHwKvt+npCwpD+1Sxm3Q3cECXCiYHjeHWXNwr6Q0SOBPrYUDxendrO6goVTEA=="
  "resolved" "https://registry.npmmirror.com/lodash.isundefined/-/lodash.isundefined-3.0.1.tgz"
  "version" "3.0.1"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g=="
  "resolved" "https://registry.npmmirror.com/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.map@^4.5.1":
  "integrity" "sha512-worNHGKLDetmcEYDvh2stPCrrQRkP20E4l0iIS7F8EvzMqBBi7ltvFN5m1HvTf1P7Jk1txKhvFcmYsCr8O2F1Q=="
  "resolved" "https://registry.npmmirror.com/lodash.map/-/lodash.map-4.6.0.tgz"
  "version" "4.6.0"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.mergewith@^4.6.2":
  "integrity" "sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ=="
  "resolved" "https://registry.npmmirror.com/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz"
  "version" "4.6.2"

"lodash.snakecase@^4.1.1":
  "integrity" "sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw=="
  "resolved" "https://registry.npmmirror.com/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.startcase@^4.4.0":
  "integrity" "sha512-+WKqsK294HMSc2jEbNgpHpd0JfIBhp7rEV4aqXWqFr6AlXov+SlcgB1Fv01y2kGe3Gc8nMW7VA0SrGuSkRfIEg=="
  "resolved" "https://registry.npmmirror.com/lodash.startcase/-/lodash.startcase-4.4.0.tgz"
  "version" "4.4.0"

"lodash.throttle@^4.1.1":
  "integrity" "sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ=="
  "resolved" "https://registry.npmmirror.com/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  "version" "4.1.1"

"lodash.toarray@^4.4.0":
  "integrity" "sha512-QyffEA3i5dma5q2490+SgCvDN0pXLmRGSyAANuVi0HQ01Pkfr9fuoKQW8wm1wGBnJITs/mS7wQvS6VshUEBFCw=="
  "resolved" "https://registry.npmmirror.com/lodash.toarray/-/lodash.toarray-4.4.0.tgz"
  "version" "4.4.0"

"lodash.union@^4.6.0":
  "integrity" "sha512-c4pB2CdGrGdjMKYLA+XiRDO7Y0PRQbm/Gzg8qMj+QH+pFVAoTp5sBpO0odL3FjoPCGjK96p6qsP+yQoiLoOBcw=="
  "resolved" "https://registry.npmmirror.com/lodash.union/-/lodash.union-4.6.0.tgz"
  "version" "4.6.0"

"lodash.uniq@^4.5.0":
  "integrity" "sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ=="
  "resolved" "https://registry.npmmirror.com/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash.upperfirst@^4.3.1":
  "integrity" "sha512-sReKOYJIJf74dhJONhU4e0/shzi1trVbSWDOhKYE5XV2O+H7Sb2Dihwuc7xWxVl+DgFPyTqIN3zMfT9cq5iWDg=="
  "resolved" "https://registry.npmmirror.com/lodash.upperfirst/-/lodash.upperfirst-4.3.1.tgz"
  "version" "4.3.1"

"lodash@*", "lodash@^4.17.15", "lodash@^4.17.21", "lodash@4.17.21":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"lodash@^4.17.4":
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.4.tgz"
  "version" "4.17.4"

"log-symbols@^4.1.0":
  "integrity" "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg=="
  "resolved" "https://registry.npmmirror.com/log-symbols/-/log-symbols-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "chalk" "^4.1.0"
    "is-unicode-supported" "^0.1.0"

"longest@^2.0.1":
  "integrity" "sha512-Ajzxb8CM6WAnFjgiloPsI3bF+WCxcvhdIG3KNA2KN962+tdBsHcuQ4k4qX/EcS/2CRkcc0iAkR956Nib6aXU/Q=="
  "resolved" "https://registry.npmmirror.com/longest/-/longest-2.0.1.tgz"
  "version" "2.0.1"

"lower-case@^2.0.2":
  "integrity" "sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg=="
  "resolved" "https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "tslib" "^2.0.3"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"magic-string@^0.25.7":
  "integrity" "sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ=="
  "resolved" "https://registry.npmmirror.com/magic-string/-/magic-string-0.25.9.tgz"
  "version" "0.25.9"
  dependencies:
    "sourcemap-codec" "^1.4.8"

"magic-string@^0.26.2":
  "integrity" "sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow=="
  "resolved" "https://registry.npmmirror.com/magic-string/-/magic-string-0.26.7.tgz"
  "version" "0.26.7"
  dependencies:
    "sourcemap-codec" "^1.4.8"

"magic-string@^0.26.4":
  "integrity" "sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow=="
  "resolved" "https://registry.npmmirror.com/magic-string/-/magic-string-0.26.7.tgz"
  "version" "0.26.7"
  dependencies:
    "sourcemap-codec" "^1.4.8"

"magic-string@^0.30.5":
  "integrity" "sha512-7xlpfBaQaP/T6Vh8MO/EqXSW5En6INHEvEXQiuff7Gku0PWjU3uf6w/j9o7O+SpB5fOAkrI5HeoNgwjEO0pFsA=="
  "resolved" "https://registry.npmmirror.com/magic-string/-/magic-string-0.30.5.tgz"
  "version" "0.30.5"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

"make-error@^1.1.1":
  "integrity" "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw=="
  "resolved" "https://registry.npmmirror.com/make-error/-/make-error-1.3.6.tgz"
  "version" "1.3.6"

"map-cache@^0.2.2":
  "integrity" "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg=="
  "resolved" "https://registry.npmmirror.com/map-cache/-/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-obj@^1.0.0":
  "integrity" "sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg=="
  "resolved" "https://registry.npmmirror.com/map-obj/-/map-obj-1.0.1.tgz"
  "version" "1.0.1"

"map-obj@^4.0.0":
  "integrity" "sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ=="
  "resolved" "https://registry.npmmirror.com/map-obj/-/map-obj-4.3.0.tgz"
  "version" "4.3.0"

"map-visit@^1.0.0":
  "integrity" "sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w=="
  "resolved" "https://registry.npmmirror.com/map-visit/-/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"mavon-editor@^2.10.4":
  "integrity" "sha512-CFsBLkgt/KZBDg+SJYe2fyYv4zClY149PiwpH0rDAiiP4ae1XNs0GC8nBsoTeipsHcebDLN1QMkt3bUsnMDjQw=="
  "resolved" "https://registry.npmmirror.com/mavon-editor/-/mavon-editor-2.10.4.tgz"
  "version" "2.10.4"
  dependencies:
    "xss" "^1.0.6"

"md-editor-v3@^1.11.3":
  "integrity" "sha512-koJl8NpB8zYK8rkR/1VVNdvifzA7ZBIJWDYvUCY5fW5TTGBKyweMo4knmG+GTKkJLwbxO7TAFuvE5IkRzlOcxQ=="
  "resolved" "https://registry.npmmirror.com/md-editor-v3/-/md-editor-v3-1.11.14.tgz"
  "version" "1.11.14"

"mdn-data@2.0.14":
  "integrity" "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="
  "resolved" "https://registry.npmmirror.com/mdn-data/-/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"memoize-one@^6.0.0":
  "integrity" "sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw=="
  "resolved" "https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz"
  "version" "6.0.0"

"meow@^8.0.0", "meow@^8.1.2":
  "integrity" "sha512-r85E3NdZ+mpYk1C6RjPFEMSE+s1iZMuHtsHAqY0DT3jZczl0diWUZ8g6oU7h0M9cD2EL+PzaYghhCLzR0ZNn5Q=="
  "resolved" "https://registry.npmmirror.com/meow/-/meow-8.1.2.tgz"
  "version" "8.1.2"
  dependencies:
    "@types/minimist" "^1.2.0"
    "camelcase-keys" "^6.2.2"
    "decamelize-keys" "^1.1.0"
    "hard-rejection" "^2.1.0"
    "minimist-options" "4.1.0"
    "normalize-package-data" "^3.0.0"
    "read-pkg-up" "^7.0.1"
    "redent" "^3.0.0"
    "trim-newlines" "^3.0.0"
    "type-fest" "^0.18.0"
    "yargs-parser" "^20.2.3"

"merge-images@^1.1.0":
  "integrity" "sha512-hEGvgnTdXr08uzGvEArxRsKpy7WmozM73YaSi4s5IYF4LxrhANpqfHaz9CgBZ5+0+s2NsjPnPdStz3aCc0Yulw=="
  "resolved" "https://registry.npmmirror.com/merge-images/-/merge-images-1.2.0.tgz"
  "version" "1.2.0"

"merge-options@1.0.1":
  "integrity" "sha512-iuPV41VWKWBIOpBsjoxjDZw8/GbSfZ2mk7N1453bwMrfzdrIk7EzBd+8UVR6rkw67th7xnk9Dytl3J+lHPdxvg=="
  "resolved" "https://registry.npmmirror.com/merge-options/-/merge-options-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-obj" "^1.1"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge@^2.1.1":
  "integrity" "sha512-jz+Cfrg9GWOZbQAnDQ4hlVnQky+341Yk5ru8bZSe6sIDTCIg8n9i/u7hSQGSVOF3C7lH6mGtqjkiT9G4wFLL0w=="
  "resolved" "https://registry.npmmirror.com/merge/-/merge-2.1.1.tgz"
  "version" "2.1.1"

"merge2@^1.3.0", "merge2@^1.4.1":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"micromatch@^4.0.2", "micromatch@^4.0.4":
  "integrity" "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA=="
  "resolved" "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"micromatch@3.1.0":
  "integrity" "sha512-3StSelAE+hnRvMs8IdVW7Uhk8CVed5tp+kLLGlBP6WiRAXS21GPGu/Nat4WNPXj2Eoc24B02SaeoyozPMfj0/g=="
  "resolved" "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.2.2"
    "define-property" "^1.0.0"
    "extend-shallow" "^2.0.1"
    "extglob" "^2.0.2"
    "fragment-cache" "^0.2.1"
    "kind-of" "^5.0.2"
    "nanomatch" "^1.2.1"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-match@^1.0.2":
  "integrity" "sha512-VXp/ugGDVh3eCLOBCiHZMYWQaTNUHv2IJrut+yXA6+JbLPXHglHwfS/5A5L0ll+jkCY7fIzRJcH6OIunF+c6Cg=="
  "resolved" "https://registry.npmmirror.com/mime-match/-/mime-match-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "wildcard" "^1.1.0"

"mime-types@^2.1.12":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mimic-response@^3.1.0":
  "integrity" "sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ=="
  "resolved" "https://registry.npmmirror.com/mimic-response/-/mimic-response-3.1.0.tgz"
  "version" "3.1.0"

"min-indent@^1.0.0":
  "integrity" "sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg=="
  "resolved" "https://registry.npmmirror.com/min-indent/-/min-indent-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.5", "minimatch@^3.1.1", "minimatch@^3.1.2":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^5.0.1":
  "integrity" "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g=="
  "resolved" "https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimatch@^5.1.0":
  "integrity" "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g=="
  "resolved" "https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimist-options@4.1.0":
  "integrity" "sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A=="
  "resolved" "https://registry.npmmirror.com/minimist-options/-/minimist-options-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "arrify" "^1.0.1"
    "is-plain-obj" "^1.1.0"
    "kind-of" "^6.0.3"

"minimist@^1.2.0", "minimist@^1.2.6":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"minimist@1.2.7":
  "integrity" "sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g=="
  "resolved" "https://registry.npmmirror.com/minimist/-/minimist-1.2.7.tgz"
  "version" "1.2.7"

"mitt@1.1.2":
  "integrity" "sha512-3btxP0O9iGADGWAkteQ8mzDtEspZqu4I32y4GZYCV5BrwtzdcRpF4dQgNdJadCrbBx7Lu6Sq9AVrerMHR0Hkmw=="
  "resolved" "https://registry.npmmirror.com/mitt/-/mitt-1.1.2.tgz"
  "version" "1.1.2"

"mixin-deep@^1.2.0":
  "integrity" "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA=="
  "resolved" "https://registry.npmmirror.com/mixin-deep/-/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@>=0.5 0":
  "integrity" "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw=="
  "resolved" "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"mlly@^0.5.16":
  "integrity" "sha512-Rn+ai4G+CQXptDFSRNnChEgNr+xAEauYhwRvpPl/UHStTlgkIftplgJRsA2OXPuoUn86K4XAjB26+x5CEvVb6A=="
  "resolved" "https://registry.npmmirror.com/mlly/-/mlly-0.5.17.tgz"
  "version" "0.5.17"
  dependencies:
    "acorn" "^8.8.1"
    "pathe" "^1.0.0"
    "pkg-types" "^1.0.0"
    "ufo" "^1.0.0"

"mlly@^1.2.0":
  "integrity" "sha512-i/Ykufi2t1EZ6NaPLdfnZk2AX8cs0d+mTzVKuPfqPKPatxLApaBoxJQ9x1/uckXtrS/U5oisPMDkNs0yQTaBRg=="
  "resolved" "https://registry.npmmirror.com/mlly/-/mlly-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "acorn" "^8.10.0"
    "pathe" "^1.1.1"
    "pkg-types" "^1.0.3"
    "ufo" "^1.3.0"

"ms@2.0.0":
  "integrity" "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="
  "resolved" "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"mute-stream@0.0.8":
  "integrity" "sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA=="
  "resolved" "https://registry.npmmirror.com/mute-stream/-/mute-stream-0.0.8.tgz"
  "version" "0.0.8"

"namespace-emitter@^2.0.1":
  "integrity" "sha512-N/sMKHniSDJBjfrkbS/tpkPj4RAbvW3mr8UAzvlMHyun93XEm83IAvhWtJVHo+RHn/oO8Job5YN4b+wRjSVp5g=="
  "resolved" "https://registry.npmmirror.com/namespace-emitter/-/namespace-emitter-2.0.1.tgz"
  "version" "2.0.1"

"nanoid@^3.1.25", "nanoid@^3.2.0", "nanoid@^3.3.7":
  "integrity" "sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g=="
  "resolved" "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.7.tgz"
  "version" "3.3.7"

"nanomatch@^1.2.1":
  "integrity" "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA=="
  "resolved" "https://registry.npmmirror.com/nanomatch/-/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare-lite@^1.4.0":
  "integrity" "sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g=="
  "resolved" "https://registry.npmmirror.com/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz"
  "version" "1.4.0"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"next-tick@^1.1.0":
  "integrity" "sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ=="
  "resolved" "https://registry.npmmirror.com/next-tick/-/next-tick-1.1.0.tgz"
  "version" "1.1.0"

"no-case@^3.0.4":
  "integrity" "sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg=="
  "resolved" "https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "lower-case" "^2.0.2"
    "tslib" "^2.0.3"

"normalize-package-data@^2.5.0":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-package-data@^3.0.0":
  "integrity" "sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA=="
  "resolved" "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "hosted-git-info" "^4.0.1"
    "is-core-module" "^2.5.0"
    "semver" "^7.3.4"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-wheel-es@^1.2.0":
  "integrity" "sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw=="
  "resolved" "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz"
  "version" "1.2.0"

"npm-run-path@^4.0.1":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nprogress@^0.2.0":
  "integrity" "sha512-I19aIingLgR1fmhftnbWWO3dXc0hSxqHQHQb3H8m+K3TnEn/iSeTZZOyvKXWqQESMwuUVnatlCnZdLBZZt2VSA=="
  "resolved" "https://registry.npmmirror.com/nprogress/-/nprogress-0.2.0.tgz"
  "version" "0.2.0"

"nth-check@^2.0.1", "nth-check@^2.1.1":
  "integrity" "sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w=="
  "resolved" "https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "boolbase" "^1.0.0"

"object-assign@^4", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ=="
  "resolved" "https://registry.npmmirror.com/object-copy/-/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-inspect@^1.9.0":
  "integrity" "sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ=="
  "resolved" "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.1.tgz"
  "version" "1.13.1"

"object-is@^1.1.5":
  "integrity" "sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw=="
  "resolved" "https://registry.npmmirror.com/object-is/-/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA=="
  "resolved" "https://registry.npmmirror.com/object-visit/-/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.pick@^1.3.0":
  "integrity" "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ=="
  "resolved" "https://registry.npmmirror.com/object.pick/-/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmmirror.com/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^5.1.0", "onetime@^5.1.2":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"optionator@^0.9.3":
  "integrity" "sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg=="
  "resolved" "https://registry.npmmirror.com/optionator/-/optionator-0.9.3.tgz"
  "version" "0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap" "^1.2.3"
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"

"ora@^5.4.1":
  "integrity" "sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ=="
  "resolved" "https://registry.npmmirror.com/ora/-/ora-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bl" "^4.1.0"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-spinners" "^2.5.0"
    "is-interactive" "^1.0.0"
    "is-unicode-supported" "^0.1.0"
    "log-symbols" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "wcwidth" "^1.0.1"

"os-tmpdir@~1.0.2":
  "integrity" "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g=="
  "resolved" "https://registry.npmmirror.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-limit@^3.0.2":
  "integrity" "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ=="
  "resolved" "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-locate@^5.0.0":
  "integrity" "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw=="
  "resolved" "https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~1.0.2":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://registry.npmmirror.com/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"param-case@^3.0.4":
  "integrity" "sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A=="
  "resolved" "https://registry.npmmirror.com/param-case/-/param-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "dot-case" "^3.0.4"
    "tslib" "^2.0.3"

"parchment@^1.1.4":
  "integrity" "sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg=="
  "resolved" "https://registry.npmmirror.com/parchment/-/parchment-1.1.4.tgz"
  "version" "1.1.4"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parenthesis@^3.1.5":
  "integrity" "sha512-KF/U8tk54BgQewkJPvB4s/US3VQY68BRDpH638+7O/n58TpnwiwnOtGIOsT2/i+M78s61BBpeC83STB88d8sqw=="
  "resolved" "https://registry.npmmirror.com/parenthesis/-/parenthesis-3.1.8.tgz"
  "version" "3.1.8"

"parse-json@^5.0.0", "parse-json@^5.2.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse-passwd@^1.0.0":
  "integrity" "sha512-1Y1A//QUXEZK7YKz+rD9WydcE1+EuPr6ZBgKecAB8tmoW6UFv0NREVJe1p+jRxtThkcbbKkfwIbWJe/IeE6m2Q=="
  "resolved" "https://registry.npmmirror.com/parse-passwd/-/parse-passwd-1.0.0.tgz"
  "version" "1.0.0"

"pascal-case@^3.1.2":
  "integrity" "sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g=="
  "resolved" "https://registry.npmmirror.com/pascal-case/-/pascal-case-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"

"pascalcase@^0.1.1":
  "integrity" "sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw=="
  "resolved" "https://registry.npmmirror.com/pascalcase/-/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@^1.0.1":
  "integrity" "sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g=="
  "resolved" "https://registry.npmmirror.com/path-browserify/-/path-browserify-1.0.1.tgz"
  "version" "1.0.1"

"path-case@^3.0.4":
  "integrity" "sha512-qO4qCFjXqVTrcbPt/hQfhTQ+VhFsqNKOPtytgNKkKxSoEp3XPUQ8ObFuePylOIok5gjn69ry8XiULxCwot3Wfg=="
  "resolved" "https://registry.npmmirror.com/path-case/-/path-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "dot-case" "^3.0.4"
    "tslib" "^2.0.3"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@^6.2.0":
  "integrity" "sha512-JLyh7xT1kizaEvcaXOQwOc2/Yhw6KZOvPf1S8401UyLk86CU79LN3vl7ztXGm/pZ+YjoyAJ4rxmHwbkBXJX+yw=="
  "resolved" "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-6.2.1.tgz"
  "version" "6.2.1"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"pathe@^0.2.0":
  "integrity" "sha512-sTitTPYnn23esFR3RlqYBWn4c45WGeLcsKzQiUpXJAyfcWkolvlYpV8FLo7JishK946oQwMFUCHXQ9AjGPKExw=="
  "resolved" "https://registry.npmmirror.com/pathe/-/pathe-0.2.0.tgz"
  "version" "0.2.0"

"pathe@^0.3.8":
  "integrity" "sha512-6Y6s0vT112P3jD8dGfuS6r+lpa0qqNrLyHPOwvXMnyNTQaYiwgau2DP3aNDsR13xqtGj7rrPo+jFUATpU6/s+g=="
  "resolved" "https://registry.npmmirror.com/pathe/-/pathe-0.3.9.tgz"
  "version" "0.3.9"

"pathe@^1.0.0":
  "integrity" "sha512-d+RQGp0MAYTIaDBIMmOfMwz3E+LOZnxx1HZd5R18mmCZY0QBlK0LDZfPc8FW8Ed2DlvsuE6PRjroDY+wg4+j/Q=="
  "resolved" "https://registry.npmmirror.com/pathe/-/pathe-1.1.1.tgz"
  "version" "1.1.1"

"pathe@^1.1.0", "pathe@^1.1.1":
  "integrity" "sha512-d+RQGp0MAYTIaDBIMmOfMwz3E+LOZnxx1HZd5R18mmCZY0QBlK0LDZfPc8FW8Ed2DlvsuE6PRjroDY+wg4+j/Q=="
  "resolved" "https://registry.npmmirror.com/pathe/-/pathe-1.1.1.tgz"
  "version" "1.1.1"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picocolors@^1.0.0":
  "integrity" "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="
  "resolved" "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.2.2", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pinia-plugin-persistedstate@^2.1.1":
  "integrity" "sha512-bQcpv47jk3ISl+InuJWsFaS/K7pRZ97kfoD2WCf/suhnlLy48k3BnFM2tI6YZ1xMsDaPv4yOsaPuPAUuSmEO2Q=="
  "resolved" "https://registry.npmmirror.com/pinia-plugin-persistedstate/-/pinia-plugin-persistedstate-2.4.0.tgz"
  "version" "2.4.0"

"pinia@^2.0.0", "pinia@^2.0.21":
  "integrity" "sha512-+C2AHFtcFqjPih0zpYuvof37SFxMQ7OEG2zV9jRI12i9BOy3YQVAHwdKtyyc8pDcDyIc33WCIsZaCFWU7WWxGQ=="
  "resolved" "https://registry.npmmirror.com/pinia/-/pinia-2.1.7.tgz"
  "version" "2.1.7"
  dependencies:
    "@vue/devtools-api" "^6.5.0"
    "vue-demi" ">=0.14.5"

"pkg-types@^1.0.0", "pkg-types@^1.0.3":
  "integrity" "sha512-nN7pYi0AQqJnoLPC9eHFQ8AcyaixBUOwvqc5TDnIKCMEE6I0y8P7OKA7fPexsXGCGxQDl/cmrLAp26LhcwxZ4A=="
  "resolved" "https://registry.npmmirror.com/pkg-types/-/pkg-types-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "jsonc-parser" "^3.2.0"
    "mlly" "^1.2.0"
    "pathe" "^1.1.0"

"posix-character-classes@^0.1.0":
  "integrity" "sha512-xTgYBc3fuo7Yt7JbiuFxSYGToMoz8fLoE6TC9Wx1P/u+LfeThMOAqmuyECnlBaaJb+u1m9hHiXUEtwW4OzfUJg=="
  "resolved" "https://registry.npmmirror.com/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-prefix-selector@^1.6.0":
  "integrity" "sha512-rdVMIi7Q4B0XbXqNUEI+Z4E+pueiu/CS5E6vRCQommzdQ/sgsS4dK42U7GX8oJR+TJOtT+Qv3GkNo6iijUMp3Q=="
  "resolved" "https://registry.npmmirror.com/postcss-prefix-selector/-/postcss-prefix-selector-1.16.0.tgz"
  "version" "1.16.0"

"postcss-selector-parser@^6.0.13":
  "integrity" "sha512-rEYkQOMUCEMhsKbK66tbEU9QVIxbhN18YiniAwA7XQYTVBqrBy+P2p5JcdqsHgKM2zWylp8d7J6eszocfds5Sw=="
  "resolved" "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.15.tgz"
  "version" "6.0.15"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss@^5.2.17":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmmirror.com/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^8.4.18", "postcss@^8.4.32", "postcss@>4 <9":
  "integrity" "sha512-D/kj5JNu6oo2EIy+XL/26JEDTlIbB8hw85G8StOE6L74RQAVVP5rej6wxCNqyMbR4RkPfqvezVbPw81Ngd6Kcw=="
  "resolved" "https://registry.npmmirror.com/postcss/-/postcss-8.4.32.tgz"
  "version" "8.4.32"
  dependencies:
    "nanoid" "^3.3.7"
    "picocolors" "^1.0.0"
    "source-map-js" "^1.0.2"

"posthtml-parser@^0.2.0", "posthtml-parser@^0.2.1":
  "integrity" "sha512-nPC53YMqJnc/+1x4fRYFfm81KV2V+G9NZY+hTohpYg64Ay7NemWWcV4UWuy/SgMupqQ3kJ88M/iRfZmSnxT+pw=="
  "resolved" "https://registry.npmmirror.com/posthtml-parser/-/posthtml-parser-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "htmlparser2" "^3.8.3"
    "isobject" "^2.1.0"

"posthtml-rename-id@^1.0":
  "integrity" "sha512-UKXf9OF/no8WZo9edRzvuMenb6AD5hDLzIepJW+a4oJT+T/Lx7vfMYWT4aWlGNQh0WMhnUx1ipN9OkZ9q+ddEw=="
  "resolved" "https://registry.npmmirror.com/posthtml-rename-id/-/posthtml-rename-id-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "escape-string-regexp" "1.0.5"

"posthtml-render@^1.0.5", "posthtml-render@^1.0.6":
  "integrity" "sha512-W1779iVHGfq0Fvh2PROhCe2QhB8mEErgqzo1wpIt36tCgChafP+hbXIhLDOM8ePJrZcFs0vkNEtdibEWVqChqw=="
  "resolved" "https://registry.npmmirror.com/posthtml-render/-/posthtml-render-1.4.0.tgz"
  "version" "1.4.0"

"posthtml-svg-mode@^1.0.3":
  "integrity" "sha512-hEqw9NHZ9YgJ2/0G7CECOeuLQKZi8HjWLkBaSVtOWjygQ9ZD8P7tqeowYs7WrFdKsWEKG7o+IlsPY8jrr0CJpQ=="
  "resolved" "https://registry.npmmirror.com/posthtml-svg-mode/-/posthtml-svg-mode-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "merge-options" "1.0.1"
    "posthtml" "^0.9.2"
    "posthtml-parser" "^0.2.1"
    "posthtml-render" "^1.0.6"

"posthtml@^0.9.2":
  "integrity" "sha512-spBB5sgC4cv2YcW03f/IAUN1pgDJWNWD8FzkyY4mArLUMJW+KlQhlmUdKAHQuPfb00Jl5xIfImeOsf6YL8QK7Q=="
  "resolved" "https://registry.npmmirror.com/posthtml/-/posthtml-0.9.2.tgz"
  "version" "0.9.2"
  dependencies:
    "posthtml-parser" "^0.2.0"
    "posthtml-render" "^1.0.5"

"preact@^10.5.13":
  "integrity" "sha512-nHHTeFVBTHRGxJXKkKu5hT8C/YWBkPso4/Gad6xuj5dbptt9iF9NZr9pHbPhBrnT2klheu7mHTxTZ/LjwJiEiQ=="
  "resolved" "https://registry.npmmirror.com/preact/-/preact-10.19.3.tgz"
  "version" "10.19.3"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="
  "resolved" "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^2.7.1", "prettier@>=2.0.0":
  "integrity" "sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q=="
  "resolved" "https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz"
  "version" "2.8.8"

"print-js@^1.6.0":
  "integrity" "sha512-BfnOIzSKbqGRtO4o0rnj/K3681BSd2QUrsIZy/+WdCIugjIswjmx3lDEZpXB2ruGf9d4b3YNINri81+J0FsBWg=="
  "resolved" "https://registry.npmmirror.com/print-js/-/print-js-1.6.0.tgz"
  "version" "1.6.0"

"prismjs@^1.23.0":
  "integrity" "sha512-Kx/1w86q/epKcmte75LNrEoT+lX8pBpavuAbvJWRXar7Hz8jrtF+e3vY751p0R8H9HdArwaCTNDDzHg/ScJK1Q=="
  "resolved" "https://registry.npmmirror.com/prismjs/-/prismjs-1.29.0.tgz"
  "version" "1.29.0"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmmirror.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"punycode@^2.1.0":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"qs@^6.11.2":
  "integrity" "sha512-tDNIz22aBzCDxLtVH++VnTfzxlfeK5CbqohpSqpJgj1Wg/cQbStNAz3NuqCs5vV+pjBsK4x4pN9HlVh7rcYRiA=="
  "resolved" "https://registry.npmmirror.com/qs/-/qs-6.11.2.tgz"
  "version" "6.11.2"
  dependencies:
    "side-channel" "^1.0.4"

"query-string@^4.3.2":
  "integrity" "sha512-O2XLNDBIg1DnTOa+2XrIwSiXEV8h2KImXUnjhhn2+UsvZ+Es2uyd5CCRTNQlDGbzUQOW3aYCBx9rVA6dzsiY7Q=="
  "resolved" "https://registry.npmmirror.com/query-string/-/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"quick-lru@^4.0.1":
  "integrity" "sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g=="
  "resolved" "https://registry.npmmirror.com/quick-lru/-/quick-lru-4.0.1.tgz"
  "version" "4.0.1"

"quill-delta@^3.6.2", "quill-delta@^3.6.3":
  "integrity" "sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg=="
  "resolved" "https://registry.npmmirror.com/quill-delta/-/quill-delta-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "deep-equal" "^1.0.1"
    "extend" "^3.0.2"
    "fast-diff" "1.1.2"

"quill-image-drop-module@^1.0.3":
  "integrity" "sha512-HP0Y2kb3nQk1QbRKZzEe1j3mArRQerN5B/U/MlXrOnxmhy3m/xYmVv0YoE13vWnGnBOIcoXGJ/9fi7l6AwsP8Q=="
  "resolved" "https://registry.npmmirror.com/quill-image-drop-module/-/quill-image-drop-module-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "quill" "^1.2.2"

"quill-image-resize-module@^3.0.0":
  "integrity" "sha512-1TZBnUxU/WIx5dPyVjQ9yN7C6mLZSp04HyWBEMqT320DIq4MW4JgzlOPDZX5ZpBM3bU6sacU4kTLUc8VgYQZYw=="
  "resolved" "https://registry.npmmirror.com/quill-image-resize-module/-/quill-image-resize-module-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "lodash" "^4.17.4"
    "quill" "^1.2.2"
    "raw-loader" "^0.5.1"

"quill@^1.2.2", "quill@^1.3.4", "quill@^1.3.7":
  "integrity" "sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g=="
  "resolved" "https://registry.npmmirror.com/quill/-/quill-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "clone" "^2.1.1"
    "deep-equal" "^1.0.1"
    "eventemitter3" "^2.0.3"
    "extend" "^3.0.2"
    "parchment" "^1.1.4"
    "quill-delta" "^3.6.2"

"raf@^3.4.1":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "https://registry.npmmirror.com/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"raw-loader@^0.5.1":
  "resolved" "https://registry.npmjs.org/raw-loader/-/raw-loader-0.5.1.tgz"
  "version" "0.5.1"

"read-pkg-up@^7.0.1":
  "integrity" "sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg=="
  "resolved" "https://registry.npmmirror.com/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "find-up" "^4.1.0"
    "read-pkg" "^5.2.0"
    "type-fest" "^0.8.1"

"read-pkg@^5.2.0":
  "integrity" "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg=="
  "resolved" "https://registry.npmmirror.com/read-pkg/-/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.0":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.2":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.5":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.0", "readable-stream@^3.1.1", "readable-stream@^3.4.0", "readable-stream@^3.6.0", "readable-stream@3":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@~2.3.6":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readdir-glob@^1.1.2":
  "integrity" "sha512-v05I2k7xN8zXvPD9N+z/uhXPaj0sUFCe2rcWZIpBsqxfP7xXFQ0tipAd/wjj1YxWyWtUS5IDJpOG82JKt2EAVA=="
  "resolved" "https://registry.npmmirror.com/readdir-glob/-/readdir-glob-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "minimatch" "^5.1.0"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"redent@^3.0.0":
  "integrity" "sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg=="
  "resolved" "https://registry.npmmirror.com/redent/-/redent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "indent-string" "^4.0.0"
    "strip-indent" "^3.0.0"

"regenerator-runtime@^0.13.7":
  "integrity" "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg=="
  "resolved" "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"regenerator-runtime@^0.14.0":
  "integrity" "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="
  "resolved" "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  "version" "0.14.1"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A=="
  "resolved" "https://registry.npmmirror.com/regex-not/-/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.5.1":
  "integrity" "sha512-sy6TXMN+hnP/wMy+ISxg3krXx7BAtWVO4UouuCN/ziM9UEne0euamVNafDfvC83bRNr95y0V5iijeDQFUNpvrg=="
  "resolved" "https://registry.npmmirror.com/regexp.prototype.flags/-/regexp.prototype.flags-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.2.0"
    "set-function-name" "^2.0.0"

"repeat-element@^1.1.2":
  "integrity" "sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ=="
  "resolved" "https://registry.npmmirror.com/repeat-element/-/repeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w=="
  "resolved" "https://registry.npmmirror.com/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmmirror.com/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"resize-detector@^0.3.0":
  "integrity" "sha512-R/tCuvuOHQ8o2boRP6vgx8hXCCy87H1eY9V5imBYeVNyNVpuL9ciReSccLj2gDcax9+2weXy3bc8Vv+NRXeEvQ=="
  "resolved" "https://registry.npmmirror.com/resize-detector/-/resize-detector-0.3.0.tgz"
  "version" "0.3.0"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmmirror.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-dir@^1.0.0", "resolve-dir@^1.0.1":
  "integrity" "sha512-R7uiTjECzvOsWSfdM0QKFNBVFcK27aHOUwdvK53BcW8zqnGdYp0Fbj82cy54+2A4P2tFM22J5kRfe1R+lM/1yg=="
  "resolved" "https://registry.npmmirror.com/resolve-dir/-/resolve-dir-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "expand-tilde" "^2.0.0"
    "global-modules" "^1.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0", "resolve-from@5.0.0":
  "integrity" "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw=="
  "resolved" "https://registry.npmmirror.com/resolve-from/-/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-global@^1.0.0", "resolve-global@1.0.0":
  "integrity" "sha512-zFa12V4OLtT5XUX/Q4VLvTfBf+Ok0SPc1FNGM/z9ctUdiU618qwKpWnd0CHs3+RqROfyEg/DhuHbMWYqcgljEw=="
  "resolved" "https://registry.npmmirror.com/resolve-global/-/resolve-global-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "global-dirs" "^0.1.1"

"resolve-url@^0.2.1":
  "integrity" "sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg=="
  "resolved" "https://registry.npmmirror.com/resolve-url/-/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.10.0", "resolve@^1.22.1":
  "integrity" "sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw=="
  "resolved" "https://registry.npmmirror.com/resolve/-/resolve-1.22.8.tgz"
  "version" "1.22.8"
  dependencies:
    "is-core-module" "^2.13.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"restore-cursor@^3.1.0":
  "integrity" "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA=="
  "resolved" "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg=="
  "resolved" "https://registry.npmmirror.com/ret/-/ret-0.1.15.tgz"
  "version" "0.1.15"

"reusify@^1.0.4":
  "integrity" "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="
  "resolved" "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rgbcolor@^1.0.1":
  "integrity" "sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw=="
  "resolved" "https://registry.npmmirror.com/rgbcolor/-/rgbcolor-1.0.1.tgz"
  "version" "1.0.1"

"rimraf@^3.0.0", "rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rimraf@2":
  "integrity" "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w=="
  "resolved" "https://registry.npmmirror.com/rimraf/-/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rollup@^2.50.0", "rollup@^2.79.1":
  "integrity" "sha512-uKxbd0IhMZOhjAiD5oAFp7BqvkA4Dv47qpOCtaNvng4HBwdbWtdOh8f5nZNuk2rp51PMGk3bzfWu5oayNEuYnw=="
  "resolved" "https://registry.npmmirror.com/rollup/-/rollup-2.79.1.tgz"
  "version" "2.79.1"
  optionalDependencies:
    "fsevents" "~2.3.2"

"run-async@^2.4.0":
  "integrity" "sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ=="
  "resolved" "https://registry.npmmirror.com/run-async/-/run-async-2.4.1.tgz"
  "version" "2.4.1"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"rxjs@^7.5.5":
  "integrity" "sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg=="
  "resolved" "https://registry.npmmirror.com/rxjs/-/rxjs-7.8.1.tgz"
  "version" "7.8.1"
  dependencies:
    "tslib" "^2.1.0"

"safe-buffer@~5.1.0", "safe-buffer@~5.1.1":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@~5.2.0":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-regex@^1.1.0":
  "integrity" "sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg=="
  "resolved" "https://registry.npmmirror.com/safe-regex/-/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@>= 2.1.2 < 3":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sass@*", "sass@^1.54.0":
  "integrity" "sha512-rzj2soDeZ8wtE2egyLXgOOHQvaC2iosZrkF6v3EUG+tBwEvhqUCzm0VP3k9gHF9LXbSrRhT5SksoI56Iw8NPnQ=="
  "resolved" "https://registry.npmmirror.com/sass/-/sass-1.69.7.tgz"
  "version" "1.69.7"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"
    "immutable" "^4.0.0"
    "source-map-js" ">=0.6.2 <2.0.0"

"saxes@^5.0.1":
  "integrity" "sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw=="
  "resolved" "https://registry.npmmirror.com/saxes/-/saxes-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "xmlchars" "^2.2.0"

"scroll-into-view-if-needed@^2.2.28":
  "integrity" "sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA=="
  "resolved" "https://registry.npmmirror.com/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz"
  "version" "2.2.31"
  dependencies:
    "compute-scroll-into-view" "^1.0.20"

"scule@^0.3.2":
  "integrity" "sha512-zIvPdjOH8fv8CgrPT5eqtxHQXmPNnV/vHJYffZhE43KZkvULvpCTvOt1HPlFaCZx287INL9qaqrZg34e8NgI4g=="
  "resolved" "https://registry.npmmirror.com/scule/-/scule-0.3.2.tgz"
  "version" "0.3.2"

"select@^1.1.2":
  "integrity" "sha512-OwpTSOfy6xSs1+pwcNrv0RBMOzI39Lp3qQKUTPVVPRjCdNa5JH/oPRiqsesIskK8TVgmRiHwO4KXlV2Li9dANA=="
  "resolved" "https://registry.npmmirror.com/select/-/select-1.1.2.tgz"
  "version" "1.1.2"

"semver@^7.3.4", "semver@^7.3.6", "semver@^7.3.7", "semver@^7.5.4", "semver@7.5.4":
  "integrity" "sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.5.4.tgz"
  "version" "7.5.4"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-5.7.2.tgz"
  "version" "5.7.2"

"sentence-case@^3.0.4":
  "integrity" "sha512-8LS0JInaQMCRoQ7YUytAo/xUu5W2XnQxV2HI/6uM6U7CITS1RqPElr30V6uIqyMKM9lJGRVFy5/4CuzcixNYSg=="
  "resolved" "https://registry.npmmirror.com/sentence-case/-/sentence-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"
    "upper-case-first" "^2.0.2"

"set-function-length@^1.1.1":
  "integrity" "sha512-VoaqjbBJKiWtg4yRcKBQ7g7wnGnLV3M8oLvVWwOk2PdYY6PEFegR1vezXR0tw6fZGF9csVakIRjrJiy2veSBFQ=="
  "resolved" "https://registry.npmmirror.com/set-function-length/-/set-function-length-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "define-data-property" "^1.1.1"
    "get-intrinsic" "^1.2.1"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.0"

"set-function-name@^2.0.0":
  "integrity" "sha512-tMNCiqYVkXIZgc2Hnoy2IvC/f8ezc5koaRFkCjrpWzGpCd3qbZXPzVy9MAZzK1ch/X0jvSkojys3oqJN0qCmdA=="
  "resolved" "https://registry.npmmirror.com/set-function-name/-/set-function-name-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "define-data-property" "^1.0.1"
    "functions-have-names" "^1.2.3"
    "has-property-descriptors" "^1.0.0"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw=="
  "resolved" "https://registry.npmmirror.com/set-value/-/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.5", "setimmediate@~1.0.4":
  "integrity" "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA=="
  "resolved" "https://registry.npmmirror.com/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"side-channel@^1.0.4":
  "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  "resolved" "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.2", "signal-exit@^3.0.3":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"signature_pad@^3.0.0-beta.3":
  "integrity" "sha512-cOf2NhVuTiuNqe2X/ycEmizvCDXk0DoemhsEpnkcGnA4kS5iJYTCqZ9As7tFBbsch45Q1EdX61833+6sjJ8rrw=="
  "resolved" "https://registry.npmmirror.com/signature_pad/-/signature_pad-3.0.0-beta.4.tgz"
  "version" "3.0.0-beta.4"

"simple-concat@^1.0.0":
  "integrity" "sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q=="
  "resolved" "https://registry.npmmirror.com/simple-concat/-/simple-concat-1.0.1.tgz"
  "version" "1.0.1"

"simple-get@^4.0.1":
  "integrity" "sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA=="
  "resolved" "https://registry.npmmirror.com/simple-get/-/simple-get-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "decompress-response" "^6.0.0"
    "once" "^1.3.1"
    "simple-concat" "^1.0.0"

"slash@^3.0.0":
  "integrity" "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q=="
  "resolved" "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz"
  "version" "3.0.0"

"slate-history@^0.66.0":
  "integrity" "sha512-6MWpxGQZiMvSINlCbMW43E2YBSVMCMCIwQfBzGssjWw4kb0qfvj0pIdblWNRQZD0hR6WHP+dHHgGSeVdMWzfng=="
  "resolved" "https://registry.npmmirror.com/slate-history/-/slate-history-0.66.0.tgz"
  "version" "0.66.0"
  dependencies:
    "is-plain-object" "^5.0.0"

"slate@^0.72.0", "slate@>=0.65.3":
  "integrity" "sha512-/nJwTswQgnRurpK+bGJFH1oM7naD5qDmHd89JyiKNT2oOKD8marW0QSBtuFnwEbL5aGCS8AmrhXQgNOsn4osAw=="
  "resolved" "https://registry.npmmirror.com/slate/-/slate-0.72.8.tgz"
  "version" "0.72.8"
  dependencies:
    "immer" "^9.0.6"
    "is-plain-object" "^5.0.0"
    "tiny-warning" "^1.0.3"

"snabbdom@^3.1.0":
  "integrity" "sha512-wHMNIOjkm/YNE5EM3RCbr/+DVgPg6AqQAX1eOxO46zYNvCXjKP5Y865tqQj3EXnaMBjkxmQA5jFuDpDK/dbfiA=="
  "resolved" "https://registry.npmmirror.com/snabbdom/-/snabbdom-3.5.1.tgz"
  "version" "3.5.1"

"snake-case@^3.0.4":
  "integrity" "sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg=="
  "resolved" "https://registry.npmmirror.com/snake-case/-/snake-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "dot-case" "^3.0.4"
    "tslib" "^2.0.3"

"snapdragon-node@^2.0.1":
  "integrity" "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw=="
  "resolved" "https://registry.npmmirror.com/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ=="
  "resolved" "https://registry.npmmirror.com/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg=="
  "resolved" "https://registry.npmmirror.com/snapdragon/-/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sortablejs@1.14.0":
  "integrity" "sha512-pBXvQCs5/33fdN1/39pPL0NZF20LeRbLQ5jtnheIPN9JQAaufGjKdWduZn4U7wCtVuzKhmRkI0DFYHYRbB2H1w=="
  "resolved" "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.14.0.tgz"
  "version" "1.14.0"

"source-map-js@^1.0.2", "source-map-js@>=0.6.2 <2.0.0":
  "integrity" "sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw=="
  "resolved" "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz"
  "version" "1.0.2"

"source-map-resolve@^0.5.0":
  "integrity" "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw=="
  "resolved" "https://registry.npmmirror.com/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-url@^0.4.0":
  "integrity" "sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw=="
  "resolved" "https://registry.npmmirror.com/source-map-url/-/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.6":
  "integrity" "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ=="
  "resolved" "https://registry.npmmirror.com/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"sourcemap-codec@^1.4.8":
  "integrity" "sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA=="
  "resolved" "https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  "version" "1.4.8"

"spdx-correct@^3.0.0":
  "integrity" "sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA=="
  "resolved" "https://registry.npmmirror.com/spdx-correct/-/spdx-correct-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A=="
  "resolved" "https://registry.npmmirror.com/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q=="
  "resolved" "https://registry.npmmirror.com/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-eWN+LnM3GR6gPu35WxNgbGl8rmY1AEmoMDvL/QD6zYmPWgywxWqJWNdLGT+ke8dKNWrcYgYjPpG5gbTfghP8rw=="
  "resolved" "https://registry.npmmirror.com/spdx-license-ids/-/spdx-license-ids-3.0.16.tgz"
  "version" "3.0.16"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw=="
  "resolved" "https://registry.npmmirror.com/split-string/-/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"split2@^3.0.0", "split2@^3.2.2":
  "integrity" "sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg=="
  "resolved" "https://registry.npmmirror.com/split2/-/split2-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "readable-stream" "^3.0.0"

"splitpanes@^3.1.1":
  "integrity" "sha512-r3Mq2ITFQ5a2VXLOy4/Sb2Ptp7OfEO8YIbhVJqJXoFc9hc5nTXXkCvtVDjIGbvC0vdE7tse+xTM9BMjsszP6bw=="
  "resolved" "https://registry.npmmirror.com/splitpanes/-/splitpanes-3.1.5.tgz"
  "version" "3.1.5"

"ssf@~0.11.2":
  "integrity" "sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g=="
  "resolved" "https://registry.npmmirror.com/ssf/-/ssf-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "frac" "~1.1.2"

"ssr-window@^3.0.0-alpha.1":
  "integrity" "sha512-q+8UfWDg9Itrg0yWK7oe5p/XRCJpJF9OBtXfOPgSJl+u3Xd5KI328RUEvUqSMVM9CiQUEf1QdBzJMkYGErj9QA=="
  "resolved" "https://registry.npmmirror.com/ssr-window/-/ssr-window-3.0.0.tgz"
  "version" "3.0.0"

"stable@^0.1.8":
  "integrity" "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w=="
  "resolved" "https://registry.npmmirror.com/stable/-/stable-0.1.8.tgz"
  "version" "0.1.8"

"stackblur-canvas@^2.0.0":
  "integrity" "sha512-8S1aIA+UoF6erJYnglGPug6MaHYGo1Ot7h5fuXx4fUPvcvQfcdw2o/ppCse63+eZf8PPidSu4v1JnmEVtEDnpg=="
  "resolved" "https://registry.npmmirror.com/stackblur-canvas/-/stackblur-canvas-2.6.0.tgz"
  "version" "2.6.0"

"static-extend@^0.1.1":
  "integrity" "sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g=="
  "resolved" "https://registry.npmmirror.com/static-extend/-/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"strict-uri-encode@^1.0.0":
  "integrity" "sha512-R3f198pcvnB+5IpnBlRkphuE9n46WyVl8I39W/ZUTZLz4nqSP/oLYUrcnJrw462Ds8he4YKMov2efsTIw1BDGQ=="
  "resolved" "https://registry.npmmirror.com/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.1.1":
  "integrity" "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  "resolved" "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-split-by@^1.0.0":
  "integrity" "sha512-KaJKY+hfpzNyet/emP81PJA9hTVSfxNLS9SFTWxdCnnW1/zOOwiV248+EfoX7IQFcBaOp4G5YE6xTJMF+pLg6A=="
  "resolved" "https://registry.npmmirror.com/string-split-by/-/string-split-by-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "parenthesis" "^3.1.5"

"string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"strip-ansi@^3.0.0":
  "integrity" "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg=="
  "resolved" "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-bom@4.0.0":
  "integrity" "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w=="
  "resolved" "https://registry.npmmirror.com/strip-bom/-/strip-bom-4.0.0.tgz"
  "version" "4.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^3.0.0":
  "integrity" "sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ=="
  "resolved" "https://registry.npmmirror.com/strip-indent/-/strip-indent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "min-indent" "^1.0.0"

"strip-json-comments@^3.1.1", "strip-json-comments@3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"strip-literal@^0.4.2":
  "integrity" "sha512-pv48ybn4iE1O9RLgCAN0iU4Xv7RlBTiit6DKmMiErbs9x1wH6vXBs45tWc0H5wUIF6TLTrKweqkmYF/iraQKNw=="
  "resolved" "https://registry.npmmirror.com/strip-literal/-/strip-literal-0.4.2.tgz"
  "version" "0.4.2"
  dependencies:
    "acorn" "^8.8.0"

"supports-color@^2.0.0":
  "integrity" "sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^3.2.3":
  "integrity" "sha512-Jds2VIYDrlp5ui7t8abHN2bjAu4LV/q4N2KivFPpGH0lrka0BMq/33AmECUXlKPcHigkNaqfXRENFju+rlcy+A=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "^1.0.0"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-baker-runtime@^1.4.7":
  "integrity" "sha512-Zorfwwj5+lWjk/oxwSMsRdS2sPQQdTmmsvaSpzU+i9ZWi3zugHLt6VckWfnswphQP0LmOel3nggpF5nETbt6xw=="
  "resolved" "https://registry.npmmirror.com/svg-baker-runtime/-/svg-baker-runtime-1.4.7.tgz"
  "version" "1.4.7"
  dependencies:
    "deepmerge" "1.3.2"
    "mitt" "1.1.2"
    "svg-baker" "^1.7.0"

"svg-baker@^1.5.0", "svg-baker@^1.7.0", "svg-baker@1.7.0":
  "integrity" "sha512-nibslMbkXOIkqKVrfcncwha45f97fGuAOn1G99YwnwTj8kF9YiM6XexPcUso97NxOm6GsP0SIvYVIosBis1xLg=="
  "resolved" "https://registry.npmmirror.com/svg-baker/-/svg-baker-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "bluebird" "^3.5.0"
    "clone" "^2.1.1"
    "he" "^1.1.1"
    "image-size" "^0.5.1"
    "loader-utils" "^1.1.0"
    "merge-options" "1.0.1"
    "micromatch" "3.1.0"
    "postcss" "^5.2.17"
    "postcss-prefix-selector" "^1.6.0"
    "posthtml-rename-id" "^1.0"
    "posthtml-svg-mode" "^1.0.3"
    "query-string" "^4.3.2"
    "traverse" "^0.6.6"

"svg-pathdata@^6.0.3":
  "integrity" "sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw=="
  "resolved" "https://registry.npmmirror.com/svg-pathdata/-/svg-pathdata-6.0.3.tgz"
  "version" "6.0.3"

"svg-sprite-loader@^6.0.11":
  "integrity" "sha512-TedsTf8wsHH6HgdwKjUveDZRC6q5gPloYV8A8/zZaRWP929J7x6TzQ6MvZFl+YYDJuJ0Akyuu/vNVJ+fbPuYXg=="
  "resolved" "https://registry.npmmirror.com/svg-sprite-loader/-/svg-sprite-loader-6.0.11.tgz"
  "version" "6.0.11"
  dependencies:
    "bluebird" "^3.5.0"
    "deepmerge" "1.3.2"
    "domready" "1.0.8"
    "escape-string-regexp" "1.0.5"
    "loader-utils" "^1.1.0"
    "svg-baker" "^1.5.0"
    "svg-baker-runtime" "^1.4.7"
    "url-slug" "2.0.0"

"svgo@^2.8.0":
  "integrity" "sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg=="
  "resolved" "https://registry.npmmirror.com/svgo/-/svgo-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "@trysound/sax" "0.2.0"
    "commander" "^7.2.0"
    "css-select" "^4.1.3"
    "css-tree" "^1.1.3"
    "csso" "^4.2.0"
    "picocolors" "^1.0.0"
    "stable" "^0.1.8"

"tar-stream@^2.2.0":
  "integrity" "sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ=="
  "resolved" "https://registry.npmmirror.com/tar-stream/-/tar-stream-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "bl" "^4.0.3"
    "end-of-stream" "^1.4.1"
    "fs-constants" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^3.1.1"

"text-extensions@^1.0.0":
  "integrity" "sha512-wiBrwC1EhBelW12Zy26JeOUkQ5mRu+5o8rpsJk5+2t+Y5vE7e842qtZDQ2g1NpX/29HdyFeJ4nSIhI47ENSxlQ=="
  "resolved" "https://registry.npmmirror.com/text-extensions/-/text-extensions-1.9.0.tgz"
  "version" "1.9.0"

"text-segmentation@^1.0.3":
  "integrity" "sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw=="
  "resolved" "https://registry.npmmirror.com/text-segmentation/-/text-segmentation-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "utrie" "^1.0.2"

"text-table@^0.2.0":
  "integrity" "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw=="
  "resolved" "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"throttleit@^1.0.0":
  "integrity" "sha512-vDZpf9Chs9mAdfY046mcPt8fg5QSZr37hEH4TXYBnDF+izxgrbRGUAAaBvIk/fJm9aOFCGFd1EsNg5AZCbnQCQ=="
  "resolved" "https://registry.npmmirror.com/throttleit/-/throttleit-1.0.1.tgz"
  "version" "1.0.1"

"through@^2.3.6", "through@>=2.2.7 <3":
  "integrity" "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "https://registry.npmmirror.com/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^4.0.0":
  "integrity" "sha512-iOqSav00cVxEEICeD7TjLB1sueEL+81Wpzp2bY17uZjZN0pWZPuo4suZ/61VujxmqSGFfgOcNuTZ85QJwNZQpw=="
  "resolved" "https://registry.npmmirror.com/through2/-/through2-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "readable-stream" "3"

"tiny-emitter@^2.0.0":
  "integrity" "sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q=="
  "resolved" "https://registry.npmmirror.com/tiny-emitter/-/tiny-emitter-2.1.0.tgz"
  "version" "2.1.0"

"tiny-warning@^1.0.3":
  "integrity" "sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA=="
  "resolved" "https://registry.npmmirror.com/tiny-warning/-/tiny-warning-1.0.3.tgz"
  "version" "1.0.3"

"tmp@^0.0.33":
  "integrity" "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw=="
  "resolved" "https://registry.npmmirror.com/tmp/-/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"tmp@^0.2.0":
  "integrity" "sha512-76SUhtfqR2Ijn+xllcI5P1oyannHNHByD80W1q447gU3mp9G9PSpGdWmjUOHRDPiHYacIk66W7ubDTuPF3BEtQ=="
  "resolved" "https://registry.npmmirror.com/tmp/-/tmp-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "rimraf" "^3.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg=="
  "resolved" "https://registry.npmmirror.com/to-object-path/-/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg=="
  "resolved" "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1":
  "integrity" "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw=="
  "resolved" "https://registry.npmmirror.com/to-regex/-/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"traverse@^0.6.6":
  "integrity" "sha512-aXJDbk6SnumuaZSANd21XAo15ucCDE38H4fkqiGsc3MhCK+wOlZvLP9cB/TvpHT0mOyWgC4Z8EwRlzqYSUzdsA=="
  "resolved" "https://registry.npmmirror.com/traverse/-/traverse-0.6.8.tgz"
  "version" "0.6.8"

"traverse@>=0.3.0 <0.4":
  "integrity" "sha512-iawgk0hLP3SxGKDfnDJf8wTz4p2qImnyihM5Hh/sGvQ3K37dPi/w8sRhdNIxYA1TwFwc5mDhIJq+O0RsvXBKdQ=="
  "resolved" "https://registry.npmmirror.com/traverse/-/traverse-0.3.9.tgz"
  "version" "0.3.9"

"trim-newlines@^3.0.0":
  "integrity" "sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw=="
  "resolved" "https://registry.npmmirror.com/trim-newlines/-/trim-newlines-3.0.1.tgz"
  "version" "3.0.1"

"ts-md5@^1.3.1":
  "integrity" "sha512-DiwiXfwvcTeZ5wCE0z+2A9EseZsztaiZtGrtSaY5JOD7ekPnR/GoIVD5gXZAlK9Na9Kvpo9Waz5rW64WKAWApg=="
  "resolved" "https://registry.npmmirror.com/ts-md5/-/ts-md5-1.3.1.tgz"
  "version" "1.3.1"

"ts-node@^10.8.1", "ts-node@>=10":
  "integrity" "sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ=="
  "resolved" "https://registry.npmmirror.com/ts-node/-/ts-node-10.9.2.tgz"
  "version" "10.9.2"
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    "acorn" "^8.4.1"
    "acorn-walk" "^8.1.1"
    "arg" "^4.1.0"
    "create-require" "^1.1.0"
    "diff" "^4.0.1"
    "make-error" "^1.1.1"
    "v8-compile-cache-lib" "^3.0.1"
    "yn" "3.1.1"

"tslib@^1.8.1":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^2.0.3", "tslib@^2.1.0", "tslib@2.3.0":
  "integrity" "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="
  "resolved" "https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz"
  "version" "2.3.0"

"tsutils@^3.21.0":
  "integrity" "sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA=="
  "resolved" "https://registry.npmmirror.com/tsutils/-/tsutils-3.21.0.tgz"
  "version" "3.21.0"
  dependencies:
    "tslib" "^1.8.1"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-fest@^0.18.0":
  "integrity" "sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.18.1.tgz"
  "version" "0.18.1"

"type-fest@^0.20.2":
  "integrity" "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^0.21.3":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.6.0":
  "integrity" "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"type@^1.0.1":
  "integrity" "sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg=="
  "resolved" "https://registry.npmmirror.com/type/-/type-1.2.0.tgz"
  "version" "1.2.0"

"type@^2.7.2":
  "integrity" "sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw=="
  "resolved" "https://registry.npmmirror.com/type/-/type-2.7.2.tgz"
  "version" "2.7.2"

"typescript@*", "typescript@^4.4.3", "typescript@^4.6.4", "typescript@^4.6.4 || ^5.2.2", "typescript@>=2.7", "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta", "typescript@>=4", "typescript@>=4.4.4", "typescript@>=4.9.5":
  "integrity" "sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g=="
  "resolved" "https://registry.npmmirror.com/typescript/-/typescript-4.9.5.tgz"
  "version" "4.9.5"

"ufo@^1.0.0", "ufo@^1.3.0":
  "integrity" "sha512-o+ORpgGwaYQXgqGDwd+hkS4PuZ3QnmqMMxRuajK/a38L6fTpcE5GPIfrf+L/KemFzfUpeUQc1rRS1iDBozvnFA=="
  "resolved" "https://registry.npmmirror.com/ufo/-/ufo-1.3.2.tgz"
  "version" "1.3.2"

"unidecode@0.1.8":
  "integrity" "sha512-SdoZNxCWpN2tXTCrGkPF/0rL2HEq+i2gwRG1ReBvx8/0yTzC3enHfugOf8A9JBShVwwrRIkLX0YcDUGbzjbVCA=="
  "resolved" "https://registry.npmmirror.com/unidecode/-/unidecode-0.1.8.tgz"
  "version" "0.1.8"

"unimport@^0.6.4":
  "integrity" "sha512-MWkaPYvN0j+6jfEuiVFhfmy+aOtgAP11CozSbu/I3Cx+8ybjXIueB7GVlKofHabtjzSlPeAvWKJSFjHWsG2JaA=="
  "resolved" "https://registry.npmmirror.com/unimport/-/unimport-0.6.8.tgz"
  "version" "0.6.8"
  dependencies:
    "@rollup/pluginutils" "^4.2.1"
    "escape-string-regexp" "^5.0.0"
    "fast-glob" "^3.2.12"
    "local-pkg" "^0.4.2"
    "magic-string" "^0.26.4"
    "mlly" "^0.5.16"
    "pathe" "^0.3.8"
    "scule" "^0.3.2"
    "strip-literal" "^0.4.2"
    "unplugin" "^0.9.6"

"union-value@^1.0.0":
  "integrity" "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg=="
  "resolved" "https://registry.npmmirror.com/union-value/-/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"universalify@^2.0.0":
  "integrity" "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw=="
  "resolved" "https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz"
  "version" "2.0.1"

"unplugin-auto-import@^0.10.3":
  "integrity" "sha512-tODQr7ZBnsBZ9lKaz2mqszKVi/4ALuLtS4gc1xwpcsBav5TCAl0HFSMuai1qL4AkYEwD2HPqK04LocCyK+D0KQ=="
  "resolved" "https://registry.npmmirror.com/unplugin-auto-import/-/unplugin-auto-import-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@antfu/utils" "^0.5.2"
    "@rollup/pluginutils" "^4.2.1"
    "local-pkg" "^0.4.2"
    "magic-string" "^0.26.2"
    "unimport" "^0.6.4"
    "unplugin" "^0.8.0"

"unplugin-vue-components@^0.21.2":
  "integrity" "sha512-HBU+EuesDj/HRs7EtYH7gBACljVhqLylltrCLModRmCToIIrrNvMh54aylUt4AD4qiwylgOx4Vgb9sBlrIcRDw=="
  "resolved" "https://registry.npmmirror.com/unplugin-vue-components/-/unplugin-vue-components-0.21.2.tgz"
  "version" "0.21.2"
  dependencies:
    "@antfu/utils" "^0.5.2"
    "@rollup/pluginutils" "^4.2.1"
    "chokidar" "^3.5.3"
    "debug" "^4.3.4"
    "fast-glob" "^3.2.11"
    "local-pkg" "^0.4.2"
    "magic-string" "^0.26.2"
    "minimatch" "^5.1.0"
    "resolve" "^1.22.1"
    "unplugin" "^0.7.2"

"unplugin-vue-define-options@^0.7.3":
  "integrity" "sha512-VbexYR8m2v/TLi49+F7Yf3rO2EyS0EkrXjJxqym6W0NxOzom9zdmRUR+av4UAu4GruhMumJc/9ITS1Wj+rozjg=="
  "resolved" "https://registry.npmmirror.com/unplugin-vue-define-options/-/unplugin-vue-define-options-0.7.3.tgz"
  "version" "0.7.3"
  dependencies:
    "@rollup/pluginutils" "^4.2.1"
    "@vue/compiler-sfc" "^3.2.37"
    "unplugin" "^0.8.0"

"unplugin@^0.7.2":
  "integrity" "sha512-m7thX4jP8l5sETpLdUASoDOGOcHaOVtgNyrYlToyQUvILUtEzEnngRBrHnAX3IKqooJVmXpoa/CwQ/QqzvGaHQ=="
  "resolved" "https://registry.npmmirror.com/unplugin/-/unplugin-0.7.2.tgz"
  "version" "0.7.2"
  dependencies:
    "acorn" "^8.7.1"
    "chokidar" "^3.5.3"
    "webpack-sources" "^3.2.3"
    "webpack-virtual-modules" "^0.4.4"

"unplugin@^0.8.0":
  "integrity" "sha512-o7rUZoPLG1fH4LKinWgb77gDtTE6mw/iry0Pq0Z5UPvZ9+HZ1/4+7fic7t58s8/CGkPrDpGq+RltO+DmswcR4g=="
  "resolved" "https://registry.npmmirror.com/unplugin/-/unplugin-0.8.1.tgz"
  "version" "0.8.1"
  dependencies:
    "acorn" "^8.8.0"
    "chokidar" "^3.5.3"
    "webpack-sources" "^3.2.3"
    "webpack-virtual-modules" "^0.4.4"

"unplugin@^0.9.6":
  "integrity" "sha512-YYLtfoNiie/lxswy1GOsKXgnLJTE27la/PeCGznSItk+8METYZErO+zzV9KQ/hXhPwzIJsfJ4s0m1Rl7ZCWZ4Q=="
  "resolved" "https://registry.npmmirror.com/unplugin/-/unplugin-0.9.6.tgz"
  "version" "0.9.6"
  dependencies:
    "acorn" "^8.8.0"
    "chokidar" "^3.5.3"
    "webpack-sources" "^3.2.3"
    "webpack-virtual-modules" "^0.4.5"

"unset-value@^1.0.0":
  "integrity" "sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ=="
  "resolved" "https://registry.npmmirror.com/unset-value/-/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"unzipper@^0.10.11":
  "integrity" "sha512-ti4wZj+0bQTiX2KmKWuwj7lhV+2n//uXEotUmGuQqrbVZSEGFMbI68+c6JCQ8aAmUWYvtHEz2A8K6wXvueR/6g=="
  "resolved" "https://registry.npmmirror.com/unzipper/-/unzipper-0.10.14.tgz"
  "version" "0.10.14"
  dependencies:
    "big-integer" "^1.6.17"
    "binary" "~0.3.0"
    "bluebird" "~3.4.1"
    "buffer-indexof-polyfill" "~1.0.0"
    "duplexer2" "~0.1.4"
    "fstream" "^1.0.12"
    "graceful-fs" "^4.2.2"
    "listenercount" "~1.0.1"
    "readable-stream" "~2.3.6"
    "setimmediate" "~1.0.4"

"upper-case-first@^2.0.2":
  "integrity" "sha512-514ppYHBaKwfJRK/pNC6c/OxfGa0obSnAl106u97Ed0I625Nin96KAjttZF6ZL3e1XLtphxnqrOi9iWgm+u+bg=="
  "resolved" "https://registry.npmmirror.com/upper-case-first/-/upper-case-first-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "tslib" "^2.0.3"

"upper-case@^2.0.2":
  "integrity" "sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg=="
  "resolved" "https://registry.npmmirror.com/upper-case/-/upper-case-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "tslib" "^2.0.3"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg=="
  "resolved" "https://registry.npmmirror.com/urix/-/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-slug@2.0.0":
  "integrity" "sha512-aiNmSsVgrjCiJ2+KWPferjT46YFKoE8i0YX04BlMVDue022Xwhg/zYlnZ6V9/mP3p8Wj7LEp0myiTkC/p6sxew=="
  "resolved" "https://registry.npmmirror.com/url-slug/-/url-slug-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unidecode" "0.1.8"

"use@^3.1.0":
  "integrity" "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="
  "resolved" "https://registry.npmmirror.com/use/-/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utrie@^1.0.2":
  "integrity" "sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw=="
  "resolved" "https://registry.npmmirror.com/utrie/-/utrie-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "base64-arraybuffer" "^1.0.2"

"uuid@^8.3.0":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v8-compile-cache-lib@^3.0.1":
  "integrity" "sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg=="
  "resolved" "https://registry.npmmirror.com/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  "version" "3.0.1"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://registry.npmmirror.com/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@^1":
  "integrity" "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg=="
  "resolved" "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vite-plugin-compression@^0.5.1":
  "integrity" "sha512-5QJKBDc+gNYVqL/skgFAP81Yuzo9R+EAf19d+EtsMF/i8kFUpNi3J/H01QD3Oo8zBQn+NzoCIFkpPLynoOzaJg=="
  "resolved" "https://registry.npmmirror.com/vite-plugin-compression/-/vite-plugin-compression-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "chalk" "^4.1.2"
    "debug" "^4.3.3"
    "fs-extra" "^10.0.0"

"vite-plugin-style-import@^2.0.0":
  "integrity" "sha512-qtoHQae5dSUQPo/rYz/8p190VU5y19rtBaeV7ryLa/AYAU/e9CG89NrN/3+k7MR8mJy/GPIu91iJ3zk9foUOSA=="
  "resolved" "https://registry.npmmirror.com/vite-plugin-style-import/-/vite-plugin-style-import-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@rollup/pluginutils" "^4.1.2"
    "change-case" "^4.1.2"
    "console" "^0.7.2"
    "es-module-lexer" "^0.9.3"
    "fs-extra" "^10.0.0"
    "magic-string" "^0.25.7"
    "pathe" "^0.2.0"

"vite-plugin-svg-icons@^2.0.1":
  "integrity" "sha512-6ktD+DhV6Rz3VtedYvBKKVA2eXF+sAQVaKkKLDSqGUfnhqXl3bj5PPkVTl3VexfTuZy66PmINi8Q6eFnVfRUmA=="
  "resolved" "https://registry.npmmirror.com/vite-plugin-svg-icons/-/vite-plugin-svg-icons-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/svgo" "^2.6.1"
    "cors" "^2.8.5"
    "debug" "^4.3.3"
    "etag" "^1.8.1"
    "fs-extra" "^10.0.0"
    "pathe" "^0.2.0"
    "svg-baker" "1.7.0"
    "svgo" "^2.8.0"

"vite-plugin-vue-setup-extend@^0.4.0":
  "integrity" "sha512-WMbjPCui75fboFoUTHhdbXzu4Y/bJMv5N9QT9a7do3wNMNHHqrk+Tn2jrSJU0LS5fGl/EG+FEDBYVUeWIkDqXQ=="
  "resolved" "https://registry.npmmirror.com/vite-plugin-vue-setup-extend/-/vite-plugin-vue-setup-extend-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "@vue/compiler-sfc" "^3.2.29"
    "magic-string" "^0.25.7"

"vite@^2.3.0 || ^3.0.0-0", "vite@^3.0.0", "vite@>=2.0.0":
  "integrity" "sha512-29pdXjk49xAP0QBr0xXqu2s5jiQIXNvE/xwd0vUizYT2Hzqe4BksNNoWllFVXJf4eLZ+UlVQmXfB4lWrc+t18g=="
  "resolved" "https://registry.npmmirror.com/vite/-/vite-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "esbuild" "^0.15.9"
    "postcss" "^8.4.18"
    "resolve" "^1.22.1"
    "rollup" "^2.79.1"
  optionalDependencies:
    "fsevents" "~2.3.2"

"vue-cropper@^1.0.3":
  "integrity" "sha512-WsqKMpaBf9Osi1LQlE/5AKdD0nHWOy1asLXocaG8NomOWO07jiZi968+/PbMmnD0QbPJOumDQaGuGa13qys85A=="
  "resolved" "https://registry.npmmirror.com/vue-cropper/-/vue-cropper-1.1.1.tgz"
  "version" "1.1.1"

"vue-cropperjs@^5.0.0":
  "integrity" "sha512-RhnC8O33uRZNkn74aiHZwNHnBJOXWlS4P6gsRI0lw4cZlWjKSCywZI9oSI9POlIPI6OYv30jvnHMXGch85tw7w=="
  "resolved" "https://registry.npmmirror.com/vue-cropperjs/-/vue-cropperjs-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "cropperjs" "^1.5.6"

"vue-demi@*", "vue-demi@^0.11.4":
  "integrity" "sha512-/3xFwzSykLW2HiiLie43a+FFgNOcokbBJ+fzvFXd0r2T8MYohqvphUyDQ8lbAwzQ3Dlcrb1c9ykifGkhSIAk6A=="
  "resolved" "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.11.4.tgz"
  "version" "0.11.4"

"vue-demi@>=0.14.5":
  "integrity" "sha512-8QA7wrYSHKaYgUxDA5ZC24w+eHm3sYCbp0EzcDwKqN3p6HqtTCGR/GVsPyZW92unff4UlcSh++lmqDWN3ZIq4w=="
  "resolved" "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.6.tgz"
  "version" "0.14.6"

"vue-eslint-parser@^9.3.1":
  "integrity" "sha512-q7tWyCVaV9f8iQyIA5Mkj/S6AoJ9KBN8IeUSf3XEmBrOtxOZnfTg5s4KClbZBCK3GtnT/+RyCLZyDHuZwTuBjg=="
  "resolved" "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.3.2.tgz"
  "version" "9.3.2"
  dependencies:
    "debug" "^4.3.4"
    "eslint-scope" "^7.1.1"
    "eslint-visitor-keys" "^3.3.0"
    "espree" "^9.3.1"
    "esquery" "^1.4.0"
    "lodash" "^4.17.21"
    "semver" "^7.3.6"

"vue-fuse@^4.1.1":
  "integrity" "sha512-bhuTiniVK3HmTcxvksrzPgDWPFTEDYiWbJa01E7yT2lRPtE4BVb5cXUBUmqtb5rCkDOsLgMh1n9mRlHQImYNkA=="
  "resolved" "https://registry.npmmirror.com/vue-fuse/-/vue-fuse-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "vue-demi" "^0.11.4"

"vue-mugen-scroll@^0.2.6":
  "integrity" "sha512-6FOBzotc8p/GDQZYkEdenxPydDvABUuxqMese6Zv4fpEZf7CjNSabRtAeBUtg+339Z9iKMdpzussbqFHCRXVqA=="
  "resolved" "https://registry.npmmirror.com/vue-mugen-scroll/-/vue-mugen-scroll-0.2.6.tgz"
  "version" "0.2.6"
  dependencies:
    "element-in-view" "^0.1.0"
    "throttleit" "^1.0.0"

"vue-qr@^4.0.6":
  "integrity" "sha512-pAISV94T0MNEYA3NGjykUpsXRE2QfaNxlu9ZhEL6CERgqNc21hJYuP3hRVzAWfBQlgO18DPmZTbrFerJC3+Ikw=="
  "resolved" "https://registry.npmmirror.com/vue-qr/-/vue-qr-4.0.9.tgz"
  "version" "4.0.9"
  dependencies:
    "glob" "^8.0.1"
    "js-binary-schema-parser" "^2.0.2"
    "simple-get" "^4.0.1"
    "string-split-by" "^1.0.0"

"vue-quill-editor@^3.0.6":
  "integrity" "sha512-g20oSZNWg8Hbu41Kinjd55e235qVWPLfg4NvsLW6d+DhgBTFbEuMpcWlUdrD6qT3+Noim6DRu18VLM9lVShXOQ=="
  "resolved" "https://registry.npmmirror.com/vue-quill-editor/-/vue-quill-editor-3.0.6.tgz"
  "version" "3.0.6"
  dependencies:
    "object-assign" "^4.1.1"
    "quill" "^1.3.4"

"vue-router@^4.1.6":
  "integrity" "sha512-DIUpKcyg4+PTQKfFPX88UWhlagBEBEfJ5A8XDXRJLUnZOvcpMF8o/dnL90vpVkGaPbjvXazV/rC1qBKrZlFugw=="
  "resolved" "https://registry.npmmirror.com/vue-router/-/vue-router-4.2.5.tgz"
  "version" "4.2.5"
  dependencies:
    "@vue/devtools-api" "^6.5.0"

"vue-signature-pad@^3.0.2":
  "integrity" "sha512-o25o+lROfCmzASS2+fU8ZV801kV+D4/02zkZ+ez3NKeiUmbxW7kwlUf5oKQkvA+l7Ou9xGsGLsirBLch3jyX8A=="
  "resolved" "https://registry.npmmirror.com/vue-signature-pad/-/vue-signature-pad-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "merge-images" "^1.1.0"
    "signature_pad" "^3.0.0-beta.3"

"vue-splitpane@^1.0.6":
  "integrity" "sha512-6sneVSEeF1VsCD025HP93nRxwzYhv3rotCeYi2Ah1FmGxwtfqbCZEIzaHGberdGNCpMlEzGD/1OIE1kK5QukLA=="
  "resolved" "https://registry.npmmirror.com/vue-splitpane/-/vue-splitpane-1.0.6.tgz"
  "version" "1.0.6"

"vue-tsc@^0.38.4":
  "integrity" "sha512-Yoy5phgvGqyF98Fb4mYqboR4Q149jrdcGv5kSmufXJUq++RZJ2iMVG0g6zl+v3t4ORVWkQmRpsV4x2szufZ0LQ=="
  "resolved" "https://registry.npmmirror.com/vue-tsc/-/vue-tsc-0.38.9.tgz"
  "version" "0.38.9"
  dependencies:
    "@volar/vue-typescript" "0.38.9"

"vue@^2.6.14 || ^3.3.0", "vue@^3.0.0-0 || ^2.6.0", "vue@^3.0.1", "vue@^3.0.11", "vue@^3.0.5", "vue@^3.2.0", "vue@^3.2.20", "vue@^3.2.25", "vue@^3.2.37", "vue@^3.2.39", "vue@>=3.0.0", "vue@>=3.0.3", "vue@2 || 3", "vue@3.4.4":
  "integrity" "sha512-suZXgDVT8lRNhKmxdkwOsR0oyUi8is7mtqI18qW97JLoyorEbE9B2Sb4Ws/mR/+0AgA/JUtsv1ytlRSH3/pDIA=="
  "resolved" "https://registry.npmmirror.com/vue/-/vue-3.4.4.tgz"
  "version" "3.4.4"
  dependencies:
    "@vue/compiler-dom" "3.4.4"
    "@vue/compiler-sfc" "3.4.4"
    "@vue/runtime-dom" "3.4.4"
    "@vue/server-renderer" "3.4.4"
    "@vue/shared" "3.4.4"

"vue3-text-clamp@^0.1.1":
  "integrity" "sha512-896tGhkwaDObKL4gUv9KhR6GQQYzIzut77P2jmfUoTaJ5lJP6kLMfCUEKwGQWbDgXXkqDcoE/QV/UtP4Jn7r3Q=="
  "resolved" "https://registry.npmmirror.com/vue3-text-clamp/-/vue3-text-clamp-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "resize-detector" "^0.3.0"
    "vue" "^3.2.37"

"vuedraggable@^4.1.0":
  "integrity" "sha512-FU5HCWBmsf20GpP3eudURW3WdWTKIbEIQxh9/8GE806hydR9qZqRRxRE3RjqX7PkuLuMQG/A7n3cfj9rCEchww=="
  "resolved" "https://registry.npmmirror.com/vuedraggable/-/vuedraggable-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "sortablejs" "1.14.0"

"vuex@^4.0.0-0":
  "integrity" "sha512-hmV6UerDrPcgbSy9ORAtNXDr9M4wlNP4pEFKye4ujJF8oqgFFuxDCdOLS3eNoRTtq5O3hoBDh9Doj1bQMYHRbQ=="
  "resolved" "https://registry.npmmirror.com/vuex/-/vuex-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.11"

"wcwidth@^1.0.1":
  "integrity" "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg=="
  "resolved" "https://registry.npmmirror.com/wcwidth/-/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webpack-sources@^3.2.3":
  "integrity" "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w=="
  "resolved" "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz"
  "version" "3.2.3"

"webpack-virtual-modules@^0.4.4", "webpack-virtual-modules@^0.4.5":
  "integrity" "sha512-5tyDlKLqPfMqjT3Q9TAqf2YqjwmnUleZwzJi1A5qXnlBCdj2AtOJ6wAWdglTIDOPgOiOrXeBeFcsQ8+aGQ6QbA=="
  "resolved" "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.6.tgz"
  "version" "0.4.6"

"which@^1.2.14":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmmirror.com/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wildcard@^1.1.0":
  "integrity" "sha512-DXukZJxpHA8LuotRwL0pP1+rS6CS7FF2qStDDE1C7DDg2rLud2PXRMuEDYIPhgEezwnlHNL4c+N6MfMTjCGTng=="
  "resolved" "https://registry.npmmirror.com/wildcard/-/wildcard-1.1.2.tgz"
  "version" "1.1.2"

"wmf@~1.0.1":
  "integrity" "sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw=="
  "resolved" "https://registry.npmmirror.com/wmf/-/wmf-1.0.2.tgz"
  "version" "1.0.2"

"word-wrap@^1.0.3":
  "integrity" "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA=="
  "resolved" "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.5.tgz"
  "version" "1.2.5"

"word@~0.3.0":
  "integrity" "sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA=="
  "resolved" "https://registry.npmmirror.com/word/-/word-0.3.0.tgz"
  "version" "0.3.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"xlsx@^0.18.5":
  "integrity" "sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ=="
  "resolved" "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.5.tgz"
  "version" "0.18.5"
  dependencies:
    "adler-32" "~1.3.0"
    "cfb" "~1.2.1"
    "codepage" "~1.15.0"
    "crc-32" "~1.2.1"
    "ssf" "~0.11.2"
    "wmf" "~1.0.1"
    "word" "~0.3.0"

"xml-name-validator@^4.0.0":
  "integrity" "sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw=="
  "resolved" "https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz"
  "version" "4.0.0"

"xmlchars@^2.2.0":
  "integrity" "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw=="
  "resolved" "https://registry.npmmirror.com/xmlchars/-/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"xss@^1.0.6":
  "integrity" "sha512-og7TEJhXvn1a7kzZGQ7ETjdQVS2UfZyTlsEdDOqvQF7GoxNfY+0YLCzBy1kPdsDDx4QuNAonQPddpsn6Xl/7sw=="
  "resolved" "https://registry.npmmirror.com/xss/-/xss-1.0.14.tgz"
  "version" "1.0.14"
  dependencies:
    "commander" "^2.20.3"
    "cssfilter" "0.0.10"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yargs-parser@^20.2.3":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs-parser@^21.1.1":
  "integrity" "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw=="
  "resolved" "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz"
  "version" "21.1.1"

"yargs@^17.0.0":
  "integrity" "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w=="
  "resolved" "https://registry.npmmirror.com/yargs/-/yargs-17.7.2.tgz"
  "version" "17.7.2"
  dependencies:
    "cliui" "^8.0.1"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.3"
    "y18n" "^5.0.5"
    "yargs-parser" "^21.1.1"

"yn@3.1.1":
  "integrity" "sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q=="
  "resolved" "https://registry.npmmirror.com/yn/-/yn-3.1.1.tgz"
  "version" "3.1.1"

"yocto-queue@^0.1.0":
  "integrity" "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
  "resolved" "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"

"zip-stream@^4.1.0":
  "integrity" "sha512-9qv4rlDiopXg4E69k+vMHjNN63YFMe9sZMrdlvKnCjlCRWeCBswPPMPUfx+ipsAWq1LXHe70RcbaHdJJpS6hyQ=="
  "resolved" "https://registry.npmmirror.com/zip-stream/-/zip-stream-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "archiver-utils" "^3.0.4"
    "compress-commons" "^4.1.2"
    "readable-stream" "^3.6.0"

"zrender@5.4.4":
  "integrity" "sha512-0VxCNJ7AGOMCWeHVyTrGzUgrK4asT4ml9PEkeGirAkKNYXYzoPJCLvmyfdoOXcjTHPs10OZVMfD1Rwg16AZyYw=="
  "resolved" "https://registry.npmmirror.com/zrender/-/zrender-5.4.4.tgz"
  "version" "5.4.4"
  dependencies:
    "tslib" "2.3.0"
