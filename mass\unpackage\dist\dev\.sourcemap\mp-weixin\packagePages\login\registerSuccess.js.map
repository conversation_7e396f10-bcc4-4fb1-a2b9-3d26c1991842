{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/registerSuccess.vue?7fad", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/registerSuccess.vue?44df", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/registerSuccess.vue?46fa", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/registerSuccess.vue?c706", "uni-app:///packagePages/login/registerSuccess.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/registerSuccess.vue?fae7", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/login/registerSuccess.vue?4f93"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "modalDialog", "data", "name", "idCardNumber", "type", "familyItem", "disabled", "IDdisabled", "modalVisible", "modalData", "title", "info", "btnTxt", "phoneList", "phone", "<PERSON><PERSON><PERSON><PERSON>", "loginForm", "onLoad", "methods", "setInputValue", "resetInputVal", "faceAuthenticationChild", "uni", "icon", "api", "then", "delta", "faceAuthentication", "idCardDecrypt", "success", "console", "verifyResult", "code", "_that", "res", "fail", "checkWxLogin", "getWxPhone", "url", "getArchiveId", "sfzjhm", "handleCancel", "handleConfirm", "setPhone"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACc;;;AAG5E;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8wB,CAAgB,8xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgDlyB;AAEA;AAIA;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAL;QACAM;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;QACAd;QACAC;MACA;IACA;EACA;EACAc;IACA;IACA;IACA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACAC;UACAZ;UACAa;QACA;QACA;MACA;MACAC;QACAtB;QACAC;MACA,GACAsB;QACA;UACAH;YACAZ;YACAa;UACA;UACAD;YACAI;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;;MAEA;QACA;UACAL;YACAZ;YACAa;UACA;UACA;QACA;MACA;;MAEA;MACA,mGACAlB,8BACAH;MACA,+FACAC,+BACAyB;MACA;MACA;MACAlC;QACAQ;QACAC;QACA0B;UACAC;UACA;YACA;YACA;UACA;UACApC;YACAmC;cACAC;cACA;gBACAN;kBACAtB;kBACAC;kBACA4B;kBACAC;gBACA,GACAP;kBACAK;kBACA;oBACA;oBACA;oBACAG,8DACAC;sBACAJ;oBACA;oBACAR;oBACA;oBACA;sBACAW;sBACAA;sBACAA;oBAEA;kBACA;gBACA;cACA;gBACAT;kBACAtB;kBACAC;kBACA4B;kBACAC;gBACA,GACAP;kBACA;oBACAH;sBACAI;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;QACAS;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;QAAA;QACA;QACA;UACAd;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAe;MAAA;MACAP;MACAN;QACA;UACA;UACA;QACA;UACAF;YACAgB;UACA;QACA;MAEA;IACA;IACAC;MACA;QACAC;MACA;MACAhB;QACAF;MACA;IACA;IACAmB;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAnB;QACAV;MACA;QACA;QACA;QACAQ;UACAgB;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtSA;AAAA;AAAA;AAAA;AAA67C,CAAgB,i5CAAG,EAAC,C;;;;;;;;;;;ACAj9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/login/registerSuccess.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/login/registerSuccess.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./registerSuccess.vue?vue&type=template&id=24d4c2d2&\"\nvar renderjs\nimport script from \"./registerSuccess.vue?vue&type=script&lang=js&\"\nexport * from \"./registerSuccess.vue?vue&type=script&lang=js&\"\nimport style0 from \"./registerSuccess.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/login/registerSuccess.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./registerSuccess.vue?vue&type=template&id=24d4c2d2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./registerSuccess.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./registerSuccess.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-wrapper\">\r\n\t\t<zlnavbar :isBack=\"true\">\r\n\t\t\t<block slot=\"content\">人脸识别</block>\r\n\t\t</zlnavbar>\r\n\t\t<view class=\"login-form\">\r\n\t\t\t<image class=\"form-icon\" mode='widthFix' src=\"../../static/images/login/register-success.png\"></image>\r\n\t\t\t<view class=\"center-box\">\r\n\t\t\t\t<view class=\"register-info\">\r\n\t\t\t\t\t<!-- <view class='info-title'>注册成功！</view> -->\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t为保障您的个人隐私及数据安全，确保所有操作为本人操作，请先进行人脸核身，验证通过后方可查看健康档案信息。\r\n\t\t\t\t\t\t<span v-if=\"type=='2'\">若姓名或证件号码有误，请联系档案管理机构进行修改。</span>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-wrap\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text>姓名: </text>\r\n\t\t\t\t\t\t<input type=\"text\" :disabled='disabled' v-model=\"name\" placeholder=\"请输入姓名\"\r\n\t\t\t\t\t\t\tplaceholder-style='color:#ccc' maxlength=\"50\" @input=\"setInputValue($event,'name')\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text>身份证号: </text>\r\n\t\t\t\t\t\t<input type=\"text\" :disabled='IDdisabled' v-model=\"idCardNumber\" placeholder=\"请输入身份证号\"\r\n\t\t\t\t\t\t\tplaceholder-style='color:#ccc' maxlength=\"18\"\r\n\t\t\t\t\t\t\t@input=\"setInputValue($event,'idCardNumber')\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tips\" v-if=\"familyItem.resumeRecognize == 1\" @click=\"resetInputVal\">\r\n\t\t\t\t\t\t如信息有误，点此重置。\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"bottom-btn-box\">\r\n\t\t\t\t<view class=\"main-btn\" @click=\"faceAuthentication\" v-if=\"ageRange\">人脸识别认证</view>\r\n\t\t\t\t<view class=\"main-btn\" @click=\"faceAuthenticationChild\" v-else>提交</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 选择手机号 -->\r\n\t\t<modalDialog :closeVisible='true' :modalData=\"modalData\" :list='phoneList' @confirm=\"handleConfirm\"\r\n\t\t\t@cancel='handleCancel' v-show=\"modalVisible\">\r\n\t\t</modalDialog>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from '@/api/api.js'\r\n\timport modalDialog from \"@/components/dialog/dialog.vue\";\r\n\timport {\r\n\t\tloginFn,\r\n\t\tgetPhoneNumberFn\r\n\t} from '@/api/login.js'\r\n\timport {\r\n\t\ttoast,\r\n\t\tcheckIdCard,\r\n\t\tcalculateAge\r\n\t} from '@/utils/util'\r\n\timport {\r\n\t\tsetAuthorization,\r\n\t\tsetInfoLogin\r\n\t} from \"@/utils/auth.js\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tmodalDialog\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tname: '',\r\n\t\t\t\tidCardNumber: '',\r\n\t\t\t\ttype: '', // 场景值：1- 当前账号人身核验; 2-亲属绑定验证; 3....其他拓展\r\n\t\t\t\tfamilyItem: null,\r\n\t\t\t\tdisabled: false,\r\n\t\t\t\tIDdisabled: false,\r\n\t\t\t\tmodalVisible: false,\r\n\t\t\t\tmodalData: {\r\n\t\t\t\t\ttype: 'list',\r\n\t\t\t\t\ttitle: '选择常用手机号',\r\n\t\t\t\t\tinfo: '',\r\n\t\t\t\t\tbtnTxt: '提交',\r\n\t\t\t\t},\r\n\t\t\t\tphoneList: [],\r\n\t\t\t\tphone: '',\r\n\t\t\t\tageRange: true,\r\n\t\t\t\tloginForm: {\r\n\t\t\t\t\tname: '',\r\n\t\t\t\t\tidCardNumber: '',\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// console.log('人脸识别type', options)\r\n\t\t\tthis.type = options.type\r\n\t\t\tthis.familyItem = options.familyItem ? JSON.parse(options.familyItem) : ''\r\n\t\t\tif (this.type == '2' || this.type == '1' && this.familyItem.resumeRecognize == 1) {\r\n\t\t\t\tthis.name = this.familyItem.resumeRecognize == 1 ? this.familyItem.realName : this.familyItem.name\r\n\t\t\t\tthis.idCardNumber = this.familyItem.idCard  // 汉中这个是注释掉的\r\n\t\t\t\tthis.disabled = true\r\n\t\t\t\tthis.IDdisabled = true  // 汉中这个是注释掉的\r\n\t\t\t\tthis.ageRange = calculateAge(this.familyItem.idCard) <= 16 ? false : true\r\n\t\t\t\t// console.log(calculateAge(this.idCardNumber))\r\n\t\t\t\t// if (!this.ageRange) {\r\n\t\t\t\t// \tthis.IDdisabled = false\r\n\t\t\t\t// \tthis.idCardNumber = ''\r\n\t\t\t\t// }\r\n\r\n\t\t\t\tif (this.familyItem.resumeRecognize == 1) {\r\n\t\t\t\t\tthis.disabled = false\r\n\t\t\t\t\tthis.idCardNumber = this.familyItem.idCard\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetInputValue(e, key) {\r\n\t\t\t\tthis.loginForm[key] = e.detail.value\r\n\t\t\t},\r\n\t\t\tresetInputVal() {\r\n\t\t\t\tthis.name = ''\r\n\t\t\t\tthis.loginForm.name = ''\r\n\t\t\t\tthis.idCardNumber = ''\r\n\t\t\t\tthis.loginForm.idCardNumber = ''\r\n\t\t\t},\r\n\t\t\tfaceAuthenticationChild() {\r\n\t\t\t\tif (this.idCardNumber != this.familyItem.idCard) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '信息错误，请核实',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tapi.documentFamilyChildBind({\r\n\t\t\t\t\t\tname: this.name,\r\n\t\t\t\t\t\tidCardNumber: this.idCardNumber,\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then((resCheck) => {\r\n\t\t\t\t\t\tif (resCheck.code == 200) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: resCheck.msg,\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tfaceAuthentication() {\r\n\t\t\t\tlet _that = this\r\n\t\t\t\tif (this.name == '' || this.idCardNumber == '') {\r\n\t\t\t\t\ttoast('请输入姓名和身份证号')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// if (!checkIdCard('01', this.idCardNumber)) {\r\n\t\t\t\t// \ttoast('身份证号格式不正确')\r\n\t\t\t\t// \treturn\r\n\t\t\t\t// }\r\n\r\n\t\t\t\tif (this.type == '2') {\r\n\t\t\t\t\tif (this.idCardNumber != this.familyItem.idCard) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '信息错误，请核实',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 唤醒腾讯人身核验\r\n\t\t\t\tvar name = this.type == '1' && this.familyItem.resumeRecognize == 1 && !this.loginForm.name ? this\r\n\t\t\t\t\t.familyItem.nameDecrypt : this\r\n\t\t\t\t\t.name\r\n\t\t\t\tvar idCardNumber = this.type == '1' && this.familyItem.resumeRecognize == 1 && !this.loginForm\r\n\t\t\t\t\t.idCardNumber ? this.familyItem\r\n\t\t\t\t\t.idCardDecrypt : this.idCardNumber\r\n\t\t\t\t// console.log(name, idCardNumber, 'verify===')\r\n\t\t\t\t// return\r\n\t\t\t\twx.startFacialRecognitionVerify({\r\n\t\t\t\t\tname: name,\r\n\t\t\t\t\tidCardNumber: idCardNumber,\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tconsole.log(\"face\", res)\r\n\t\t\t\t\t\tif (res.errCode != 0) {\r\n\t\t\t\t\t\t\ttoast(res.errMsg)\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\twx.login({\r\n\t\t\t\t\t\t\tsuccess(resCode) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"login\", resCode)\r\n\t\t\t\t\t\t\t\tif (_that.type == '1') {\r\n\t\t\t\t\t\t\t\t\tapi.faceIdentify({\r\n\t\t\t\t\t\t\t\t\t\t\tname: name,\r\n\t\t\t\t\t\t\t\t\t\t\tidCardNumber: idCardNumber,\r\n\t\t\t\t\t\t\t\t\t\t\tverifyResult: res.verifyResult,\r\n\t\t\t\t\t\t\t\t\t\t\tcode: resCode.code\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t.then((resCheck) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(\"check\", resCheck)\r\n\t\t\t\t\t\t\t\t\t\t\tif (resCheck.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// toast(resCheck.msg)\r\n\t\t\t\t\t\t\t\t\t\t\t\t// 更新状态\r\n\t\t\t\t\t\t\t\t\t\t\t\t_that.$store.dispatch('user/storeSetUserInfo').then((\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tres) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('isVerify', true)\r\n\t\t\t\t\t\t\t\t\t\t\t\t// 处理返回\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (_that.type == '1') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t_that.getArchiveId()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t_that.checkWxLogin()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t_that.getWxPhone()\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t} else if (_that.type == '2') {\r\n\t\t\t\t\t\t\t\t\tapi.documentFamilyFaceBind({\r\n\t\t\t\t\t\t\t\t\t\t\tname: _that.name,\r\n\t\t\t\t\t\t\t\t\t\t\tidCardNumber: _that.idCardNumber,\r\n\t\t\t\t\t\t\t\t\t\t\tverifyResult: res.verifyResult,\r\n\t\t\t\t\t\t\t\t\t\t\tcode: resCode.code\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t.then((resCheck) => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (resCheck.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\ttoast(err.errMsg)\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 首次状态判断\r\n\t\t\tcheckWxLogin() {\r\n\t\t\t\tloginFn(1).then(res => { // 微信登录&服务端获取openid\r\n\t\t\t\t\t// console.log(res.data,res.data.userExist, '接口换取的openid')\r\n\t\t\t\t\tif (res.data.userExist) {\r\n\t\t\t\t\t\tuni.setStorageSync('resident_id', res.data.loginUser.id)\r\n\t\t\t\t\t\tsetAuthorization(res.data.access_token)\r\n\t\t\t\t\t\tsetInfoLogin(true)\r\n\t\t\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t\t\t// \turl: `/pages/index/index`\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetWxPhone() {\r\n\t\t\t\tconsole.log('设置手机号')\r\n\t\t\t\tapi.fetchCurPhone({}).then(res => {\r\n\t\t\t\t\tif (res.data.length > 1) {\r\n\t\t\t\t\t\tthis.phoneList = res.data\r\n\t\t\t\t\t\tthis.modalVisible = true\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetArchiveId() {\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tsfzjhm: this.idCardNumber\r\n\t\t\t\t}\r\n\t\t\t\tapi.fetchArchiveId(params).then((res) => {\r\n\t\t\t\t\tuni.setStorageSync('archiveId', res.data)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandleCancel() {\r\n\t\t\t\tthis.modalVisible = false\r\n\t\t\t},\r\n\t\t\thandleConfirm(val) {\r\n\t\t\t\tthis.phone = val\r\n\t\t\t\tthis.modalVisible = false\r\n\t\t\t\tthis.setPhone()\r\n\t\t\t},\r\n\t\t\tsetPhone() {\r\n\t\t\t\tapi.chooePhone({\r\n\t\t\t\t\tphone: this.phone\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.modalVisible = false\r\n\t\t\t\t\tthis.checkWxLogin() // 由于账号合并产品需求，和后端沟通这里必须加，后续这里慎重改动\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.login-wrapper {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tflex-direction: column;\r\n\t\toverflow: scroll;\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\r\n\t\t.login-form {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 0 26px;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\t.register-info {\r\n\t\t\t\t// font-size: 12px;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.9);\r\n\t\t\t\tline-height: 20px;\r\n\t\t\t\t// padding: 0 20px;\r\n\t\t\t\tmargin-bottom: 25px;\r\n\t\t\t}\r\n\r\n\t\t\t.info-title {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-bottom: 19px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.center-box {\r\n\t\t\tpadding: 0 20px;\r\n\t\t\tmargin-bottom: 20px;\r\n\t\t}\r\n\r\n\t\t.form-wrap {\r\n\t\t\twidth: 100%;\r\n\t\t\t// padding: 0 20px;\r\n\r\n\t\t\t.form-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-stfvafvrt;\r\n\t\t\t\tpadding-bottom: 15px;\r\n\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\t// display: inline-block;\r\n\t\t\t\t\t// min-width: 80px;\r\n\t\t\t\t\t// background-color: red;\r\n\t\t\t\t\t// text-align: right;\r\n\t\t\t\t\t// width: fit-content;\r\n\t\t\t\t\t// padding-right: 10px;\r\n\t\t\t\t\tmargin-right: 10px;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tinput[disabled] {\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\r\n\t\t.bottom-btn-box {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.tips {\r\n\t\t\tcolor: #1c6ced;\r\n\t\t\ttext-align: left;\r\n\t\t\tmargin-bottom: 30px;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\ttext-decoration: underline;\r\n\r\n\t\t\t.reset-btn {\r\n\t\t\t\twidth: 40px;\r\n\t\t\t\tpadding: 0 !important;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tcolor: #1c6ced;\r\n\t\t\t\tborder: 1px solid #1c6ced;\r\n\t\t\t\tfont-size: 12px !important;\r\n\t\t\t\tbackground-color: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./registerSuccess.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./registerSuccess.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308042\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}