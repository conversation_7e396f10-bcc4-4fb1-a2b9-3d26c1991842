{"version": 3, "sources": ["webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/tabbar/tabbar.vue?82cb", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/tabbar/tabbar.vue?ef5c", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/tabbar/tabbar.vue?39b3", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/tabbar/tabbar.vue?0ef3", "uni-app:///components/tabbar/tabbar.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/tabbar/tabbar.vue?1f89", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/components/tabbar/tabbar.vue?e187"], "names": ["data", "iPhoneX", "JawHeight", "props", "selectedIndex", "type", "default", "noticeNum", "methods", "handleDidSelectTabBar"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqwB,CAAgB,qxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACuCzxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAA;IACA;MACAC;MACAC;MACA;IACA;EACA;;EAEAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EAEAE;IACAC;MACA;QACAL;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAo7C,CAAgB,w4CAAG,EAAC,C;;;;;;;;;;;ACAx8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tabbar/tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tabbar.vue?vue&type=template&id=8ad7aaf8&\"\nvar renderjs\nimport script from \"./tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tabbar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tabbar/tabbar.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=template&id=8ad7aaf8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"tabbar\" :style=\"{'height': (iPhoneX ? 60+JawHeight : 60) + 'px'}\">\r\n\t\t<!-- 首页 -->\r\n\t\t<button class=\"tabbar-item\" :style=\"{'color' : selectedIndex === 0 ? '#0052D9' : ''}\"\r\n\t\t\t@click=\"handleDidSelectTabBar(0)\">\r\n\t\t\t<view class=\"tabbar-item-icon\">\r\n\t\t\t\t<!-- <i class='iconfont icon-home'></i> -->\r\n\t\t\t\t<image src=\"../../static/images/home.png\" v-if=\" selectedIndex === 0 \"></image>\r\n\t\t\t\t<image src=\"../../static/images/grey-home.png\" v-else></image>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"tabbar-item-text\">健康档案</view>\r\n\t\t</button>\r\n\t\t\r\n\t\t<!-- 我的 -->\r\n\t\t<button class=\"tabbar-item\" :style=\"{'color' : selectedIndex === 2 ? '#0052D9' : ''}\"\r\n\t\t\t@click=\"handleDidSelectTabBar(2)\">\r\n\t\t\t<view class=\"tabbar-item-icon\">\r\n\t\t\t\t<image src=\"../../static/images/AI.png\" v-if=\" selectedIndex === 2 \"></image>\r\n\t\t\t\t<image src=\"../../static/images/gray-AI.png\" v-else></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tabbar-item-text\">AI报告分析</view>\r\n\t\t</button>\r\n\r\n\t\t<!-- 我的 -->\r\n\t\t<button class=\"tabbar-item\" :style=\"{'color' : selectedIndex ===1 ? '#0052D9' : ''}\"\r\n\t\t\t@click=\"handleDidSelectTabBar(1)\">\r\n\t\t\t<view class=\"tabbar-item-icon\">\r\n\t\t\t\t<!-- <i class='iconfont icon-customization'></i> -->\r\n\t\t\t\t<image src=\"../../static/images/my.png\" v-if=\" selectedIndex === 1 \"></image>\r\n\t\t\t\t<image src=\"../../static/images/grey-my.png\" v-else></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"point\" v-if=\"noticeNum!=0\"></view>\r\n\t\t\t<view class=\"tabbar-item-text\">我的</view>\r\n\t\t</button>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tiPhoneX: this.iPhoneX,\r\n\t\t\t\tJawHeight: this.JawHeight,\r\n\t\t\t\t// noticeNum: 0,\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tprops: {\r\n\t\t\tselectedIndex: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 5\r\n\t\t\t},\r\n\t\t\tnoticeNum: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\thandleDidSelectTabBar(selectedIndex) {\r\n\t\t\t\tthis.$emit(\"didSelectTabBar\", {\r\n\t\t\t\t\tselectedIndex: selectedIndex\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.tabbar {\r\n\t\tbackground: #FFFFFF;\r\n\t\t// z-index: 1024;\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tbottom: 0px;\r\n\t\tdisplay: flex;\r\n\t\t// align-items: center;\r\n\t\tjustify-content: space-around;\r\n\t\theight: 60px;\r\n\t\tborder-top: 1px solid #F4F4F4;\r\n\t\tz-index: 100;\r\n\t\t-webkit-transform: translateZ(1px);\r\n\t\t-moz-transform: translateZ(1px);\r\n\t\t-o-transform: translateZ(1px);\r\n\t\ttransform: translateZ(1px);\r\n\r\n\t\t.tabbar-item {\r\n\r\n\t\t\tpadding: 0;\r\n\t\t\tmargin: 0;\r\n\t\t\tborder: 0;\r\n\t\t\tborder-radius: 0;\r\n\t\t\toverflow: visible;\r\n\t\t\tposition: relative;\r\n\t\t\tcolor: #6E6D7A;\r\n\r\n\t\t\t.point {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 8px;\r\n\t\t\t\tright: 42%;\r\n\t\t\t\twidth: 8px;\r\n\t\t\t\theight: 8px;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground: #D54941;\r\n\t\t\t}\r\n\r\n\r\n\t\t\t&:after {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\r\n\t\t\twidth: 100%;\r\n\t\t\tflex-grow: 1;\r\n\t\t\theight: 60px;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #666666;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tline-height: 14px;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: space-between;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 21px;\r\n\t\t\t\theight: 20px;\r\n\t\t\t}\r\n\r\n\t\t\t.tabbar-item-icon {\r\n\t\t\t\tmargin-top: 10px;\r\n\t\t\t\theight: 30px;\r\n\r\n\t\t\t\ti {\r\n\t\t\t\t\tmargin-top: 7px;\r\n\t\t\t\t\tfont-size: 28px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.renwu {\r\n\t\t\t\t\tfont-size: 25px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.gongneng {\r\n\t\t\t\t\tdisplay: block;\r\n\r\n\t\t\t\t\tpadding-left: 5px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 21px;\r\n\t\t\t\t\theight: 20px;\r\n\t\t\t\t\topacity: 1;\r\n\t\t\t\t\tborder-radius: 3px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.tabbar-item-text {\r\n\t\t\t\tmargin-bottom: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t.tabbar-item-center {\r\n\r\n\t\t\t\theight: 30px;\r\n\r\n\t\t\t\t.tabbar-item-center-icon {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tborder: 0px;\r\n\t\t\t\t\tdisplay: inline-flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\theight: 30px;\r\n\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\ttext-decoration: none;\r\n\t\t\t\t\toverflow: visible;\r\n\t\t\t\t\ttransform: translate(0upx, 0upx);\r\n\t\t\t\t\tbackground: 0%;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 68px;\r\n\t\t\t\t\t\theight: 46px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542313793\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}