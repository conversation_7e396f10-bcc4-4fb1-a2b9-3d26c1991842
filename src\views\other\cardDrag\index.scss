.m-card-drag{
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .header{
    flex-shrink: 0;
  }
}
.footer{
  flex: 1;
  padding: 10px;
  box-sizing: border-box;
  overflow: hidden;
}
.card {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  height: 100%;
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-fill-color-blank);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}
.item-group-item {
  position: relative;
  width: calc(100% / 5.3);
  margin-bottom: 10px;
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  cursor: move;
  font-weight: 500;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.chosenClass {
  opacity: 0.6;
  border: 1px solid rgba(64,158,255,0.2);
}
.item-icon{
  width: 100%;
  display: flex;
  justify-content: center
}
