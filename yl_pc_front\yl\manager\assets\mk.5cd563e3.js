import{_ as s}from"./mk.701cea42.js";import{u as n,_}from"./index.dc283410.js";const u=Vue.defineComponent({__name:"mk",setup(c){const o=n(),t=()=>{o.push("/oa")};return(r,e)=>(Vue.openBlock(),Vue.createElementBlock("div",{class:"login-container",onClick:t},e[0]||(e[0]=[Vue.createElementVNode("img",{src:s,alt:"\u767B\u5F55\u9875",class:"login-image"},null,-1)])))}});const m=_(u,[["__scopeId","data-v-b52205fc"]]);export{m as default};
//# sourceMappingURL=mk.5cd563e3.js.map
