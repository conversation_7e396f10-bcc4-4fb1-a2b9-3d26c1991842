@charset "UTF-8";
.login-wrapper {
  height: 100%;
}
.main-content {
  padding: 14px;
}
.main-content .dialog-list .dialog-info {
  display: flex;
  flex-wrap: wrap;
  flex-shrink: 0;
  -webkit-column-gap: 10px;
          column-gap: 10px;
  margin-bottom: 16px;
}
.main-content .dialog-list .dialog-info view.image {
  width: 32px;
  height: 32px;
  background-color: #F2F2F2;
  border-radius: 50%;
  padding: 8px 4px;
}
.main-content .dialog-list .dialog-info view.image2 {
  padding: 0;
}
.main-content .dialog-list .dialog-info .dialog-content {
  max-width: calc(100% - 60px);
  background-color: #F7F7F7;
  padding: 10px;
  font-size: 14px;
}
.main-content .dialog-list .dialog-info .dialog-content.quest {
  border-radius: 10px 0 10px 10px;
}
.main-content .dialog-list .dialog-info .dialog-content.answer {
  border-radius: 0 10px 10px 10px;
}
.main-content .dialog-list .flex-end-content {
  justify-content: flex-end;
}
.main-content .main-button {
  margin-top: 30px;
  padding: 0 10px 20px 10px;
  text-align: center;
}
.main-content .main-button .button-list {
  margin-top: 10px;
  padding: 10px;
  display: inline-block;
  border-radius: 20px;
  background-color: #F7F7F7;
  font-size: 14px;
}
.main-content .main-button .button-list text {
  color: #666;
}
.main-content .main-button .button-list text.question {
  margin-right: 10px;
}
.main-content .main-button .button-list image {
  width: 20px;
  vertical-align: bottom;
}
.main-content .main-button .tips {
  margin-top: 20px;
  color: red;
  font-size: 14px;
}
.dotting {
  display: inline-block;
  min-width: 2px;
  min-height: 2px;
  margin-right: 22px;
  margin-left: 10px;
  box-shadow: 2px 0 #1c6ced;
  /* 初始化显示一个点 */
  -webkit-animation: dot 1.5s infinite step-start both;
          animation: dot 1.5s infinite step-start both;
}
@-webkit-keyframes dot {
0% {
    box-shadow: 2px 0 #1c6ced;
}
  /* 1个点 */
25% {
    box-shadow: none;
}
  /* 0个点 */
50% {
    box-shadow: 2px 0 #1c6ced;
}
  /* 1个点 */
75% {
    box-shadow: 2px 0 #1c6ced, 6px 0 #1c6ced;
}
  /* 2个点 */
100% {
    box-shadow: 2px 0 #1c6ced, 6px 0 #1c6ced, 10px 0 #1c6ced;
}
  /* 3个点 */
}
@keyframes dot {
0% {
    box-shadow: 2px 0 #1c6ced;
}
  /* 1个点 */
25% {
    box-shadow: none;
}
  /* 0个点 */
50% {
    box-shadow: 2px 0 #1c6ced;
}
  /* 1个点 */
75% {
    box-shadow: 2px 0 #1c6ced, 6px 0 #1c6ced;
}
  /* 2个点 */
100% {
    box-shadow: 2px 0 #1c6ced, 6px 0 #1c6ced, 10px 0 #1c6ced;
}
  /* 3个点 */
}

