<template>
  <div class="login-container" @click="goToModules">
    <img src="@/assets/image/9jsc1.jpg" alt="登录页" class="login-image">
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToModules = () => {
  router.push('/jsc2')
}
</script>

<style scoped>
html, body, #app {
  height: 100%;
  margin: 0;
  padding: 0;
}

.login-container {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.login-image {
  width: 100%;
  height: 100%;
  object-position: center; /* 图片居中显示 */

}
</style>
<!-- <style scoped>
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 113vh;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  min-height: 100vh;
  min-width: 100vw;
}

.enter-tip {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 16px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}
</style> -->