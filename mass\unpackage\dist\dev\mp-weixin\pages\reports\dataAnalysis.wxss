.main-report.data-v-24d3e3ef {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.main-report .report-list.data-v-24d3e3ef {
  flex: 1;
  overflow: auto;
  position: relative;
}
.main-report .report-list .list-info.data-v-24d3e3ef {
  text-align: left;
  background-color: #ececec;
  border-radius: 4px;
  padding: 6px;
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  position: relative;
}
.main-report .report-list .list-info checkbox.data-v-24d3e3ef {
  position: absolute;
  left: 4px;
  top: 50%;
  -webkit-transform: translateY(-50%) scale(0.6);
          transform: translateY(-50%) scale(0.6);
}
.main-report .report-list .list-info .report-content.data-v-24d3e3ef {
  padding-left: 30px;
}
.main-report .report-list .list-info .report-content view.data-v-24d3e3ef {
  line-height: 26px;
  color: #666;
}
.main-report .report-list .list-info .report-content .report-title.data-v-24d3e3ef {
  color: #333;
}
.main-report .report-list .list-info .report-content .report-title text.data-v-24d3e3ef {
  padding: 2px 4px;
  color: white;
  margin-right: 6px;
}
.main-report .report-list .list-info .report-content .green-text.data-v-24d3e3ef {
  background-color: green;
}
.main-report .report-list .list-info .report-content .orange-text.data-v-24d3e3ef {
  background-color: orange;
}
.main-report .list-loading.data-v-24d3e3ef {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding-top: 100px;
}
.main-report .report-button.data-v-24d3e3ef {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 10px;
}
.main-report .report-button text.data-v-24d3e3ef {
  display: inline-block;
  flex: 1;
  text-align: center;
  background-color: #1c6ced;
  color: white;
  padding: 10px 0;
  border-radius: 4px;
}
.main-report .searchPop.data-v-24d3e3ef {
  position: fixed;
  bottom: 60px;
  left: 0;
  width: 100%;
  background-color: white;
  border-top: 1px solid #ececec;
  border-radius: 10px 10px 0 0;
  box-shadow: 0 0 0rpx 2000rpx rgba(0, 0, 0, 0.5);
}
.main-report .searchPop .title.data-v-24d3e3ef {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  padding: 16px;
  color: black;
}
.main-report .searchPop .title .button.data-v-24d3e3ef {
  color: #666;
}
.main-report .searchPop .title .submit-button.data-v-24d3e3ef {
  color: #1c6ced;
}
.main-report .searchPop checkbox.data-v-24d3e3ef {
  -webkit-transform: scale(0.6);
          transform: scale(0.6);
  margin-right: 6px;
}
.main-report .searchPop .search-form.data-v-24d3e3ef {
  text-align: left;
  padding: 0 20px;
}
.main-report .searchPop .search-form > view.data-v-24d3e3ef {
  margin-bottom: 10px;
}
.main-report .searchPop .search-form .uni-list-cell.data-v-24d3e3ef {
  margin-right: 20px;
}
.main-report .searchPop .search-form .form-label.data-v-24d3e3ef {
  font-weight: 500;
}
.main-report .searchPop .search-form .form-search.data-v-24d3e3ef {
  display: flex;
  gap: 10px;
}
.main-report .searchPop .search-form .form-search text.data-v-24d3e3ef {
  height: 30px;
  line-height: 30px;
}
.main-report .searchPop .search-form .form-search picker.data-v-24d3e3ef {
  flex: 1;
  text-align: center;
  border: 1px solid #ececec;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  color: #666;
}

