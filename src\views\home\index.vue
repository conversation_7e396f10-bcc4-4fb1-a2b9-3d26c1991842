<template>
  <div class="home-container">
    <!-- 顶部欢迎区域 -->
    <el-card class="welcome-card">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>欢迎使用企业管理系统</h1>
          <p>这是一个基于Vue3和Element Plus的企业级管理系统示例</p>
          <el-button type="primary" size="large" @click="goToModules">
            进入系统
            <el-icon class="el-icon--right"><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="welcome-image">
          <!-- <img src="@/assets/images/welcome.svg" alt="欢迎图片" /> -->
        </div>
      </div>
    </el-card>

    <!-- 功能模块区域 -->
    <h2 class="section-title">系统功能模块</h2>
    <el-row :gutter="20">
      <el-col :span="8" v-for="module in modules" :key="module.path">
        <el-card class="module-card" @click="navigateToModule(module.path)">
          <div class="module-icon">
            <el-icon :size="40"><component :is="module.icon" /></el-icon>
          </div>
          <h3 class="module-title">{{ module.title }}</h3>
          <p class="module-desc">{{ module.description }}</p>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息区域 -->
    <h2 class="section-title">系统信息</h2>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>技术栈</span>
            </div>
          </template>
          <div class="tech-list">
            <div class="tech-item" v-for="tech in techStack" :key="tech.name">
              <el-icon><component :is="tech.icon" /></el-icon>
              <span>{{ tech.name }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
            </div>
          </template>
          <div class="status-list">
            <div class="status-item" v-for="status in systemStatus" :key="status.name">
              <span class="status-label">{{ status.name }}:</span>
              <span class="status-value">{{ status.value }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  OfficeBuilding,
  DataLine,
  Monitor,
  ArrowRight,
  ElementPlus,
  Edit,
  Connection,
  Cpu
} from '@element-plus/icons-vue'

const router = useRouter()

// 功能模块数据
const modules = ref([
  {
    title: 'OA系统',
    description: '高效的办公自动化系统，提供全面的办公管理解决方案',
    icon: 'OfficeBuilding',
    path: '/module/oa'
  },
  {
    title: '数据驾驶舱',
    description: '直观的数据可视化平台，助力企业决策分析',
    icon: 'DataLine',
    path: '/module/dashboard'
  },
  {
    title: '数据中台',
    description: '统一的数据管理平台，实现数据资产的高效管理',
    icon: 'Monitor',
    path: '/module/datacenter'
  }
])

// 技术栈信息
const techStack = ref([
  { name: 'Vue 3', icon: 'Edit' },
  { name: 'Element Plus', icon: 'ElementPlus' },
  { name: 'TypeScript', icon: 'Connection' },
  { name: 'Vite', icon: 'Cpu' }
])

// 系统状态信息
const systemStatus = ref([
  { name: '版本', value: 'v3.0.0' },
  { name: '更新日期', value: '2024-03-20' },
  { name: '服务状态', value: '正常' },
  { name: '在线用户', value: '128' }
])

// 导航方法
const goToModules = () => {
  router.push('/module/oa')
}

const navigateToModule = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.home-container {
  padding: 20px;
}

.welcome-card {
  margin-bottom: 30px;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.welcome-text {
  flex: 1;
}

.welcome-text h1 {
  margin: 0 0 15px;
  font-size: 28px;
  color: var(--el-color-primary);
}

.welcome-text p {
  margin: 0 0 25px;
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

.welcome-image {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.welcome-image img {
  max-width: 100%;
  height: 200px;
}

.section-title {
  margin: 30px 0 20px;
  font-size: 20px;
  color: var(--el-text-color-primary);
}

.module-card {
  height: 200px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.module-card:hover {
  transform: translateY(-5px);
}

.module-icon {
  margin-bottom: 15px;
  color: var(--el-color-primary);
}

.module-title {
  margin: 0 0 10px;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

.module-desc {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

.info-card {
  margin-bottom: 20px;
  height: 100%;
}

.card-header {
  font-weight: bold;
}

.tech-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 15px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: bold;
  color: var(--el-text-color-regular);
}

.status-value {
  color: var(--el-text-color-primary);
}
</style>