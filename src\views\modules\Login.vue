<template>
  <div class="dashboard-container">
    <!-- 顶部数据卡片 -->
    <el-row :gutter="20">
      <el-col :span="6" v-for="card in dataCards" :key="card.title">
        <el-card class="data-card" :body-style="{ padding: '20px' }">
          <el-row align="middle">
            <el-col :span="16">
              <h3 class="card-title">{{ card.title }}</h3>
              <div class="card-value">
                {{ card.value }}
                <span class="trend" :class="card.trend">
                  {{ card.rate }}
                  <el-icon>
                    <component :is="card.trend === 'up' ? 'CaretTop' : 'CaretBottom'" />
                  </el-icon>
                </span>
              </div>
            </el-col>
            <el-col :span="8" class="card-icon">
              <el-icon :size="40"><component :is="card.icon" /></el-icon>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>销售趋势</span>
              <el-radio-group v-model="timeRange" size="small">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">全年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <v-chart class="chart" :option="lineChartOption" autoresize />
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>销售分布</span>
            </div>
          </template>
          <div class="chart-container">
            <v-chart class="chart" :option="pieChartOption" autoresize />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  Money,
  ShoppingCart,
  User,
  Goods
} from '@element-plus/icons-vue'

// 注册 ECharts 必需的组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 顶部数据卡片数据
const dataCards = ref([
  {
    title: '总销售额',
    value: '¥126,560',
    rate: '12.5%',
    trend: 'up',
    icon: 'Money'
  },
  {
    title: '订单数量',
    value: '1,234',
    rate: '8.2%',
    trend: 'up',
    icon: 'ShoppingCart'
  },
  {
    title: '活跃用户',
    value: '15,689',
    rate: '3.1%',
    trend: 'down',
    icon: 'User'
  },
  {
    title: '商品总数',
    value: '8,426',
    rate: '6.7%',
    trend: 'up',
    icon: 'Goods'
  }
])

// 时间范围选择
const timeRange = ref('month')

// 折线图配置
const lineChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['销售额', '订单量']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '销售额',
      type: 'line',
      smooth: true,
      data: [12000, 13200, 10100, 13400, 15000, 14200, 16100]
    },
    {
      name: '订单量',
      type: 'line',
      smooth: true,
      data: [120, 132, 101, 134, 150, 142, 161]
    }
  ]
}))

// 饼图配置
const pieChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '销售分布',
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1048, name: '电子产品' },
        { value: 735, name: '服装' },
        { value: 580, name: '食品' },
        { value: 484, name: '家居' },
        { value: 300, name: '其他' }
      ]
    }
  ]
}))
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.data-card {
  margin-bottom: 20px;
}

.card-title {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.card-value {
  margin-top: 8px;
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--el-color-primary);
}

.trend {
  font-size: 14px;
  margin-left: 8px;
}

.trend.up {
  color: #67c23a;
}

.trend.down {
  color: #f56c6c;
}

.chart-row {
  margin-top: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 350px;
}

.chart {
  height: 100%;
}
</style>