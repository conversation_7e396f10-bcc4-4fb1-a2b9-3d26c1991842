<view class="login-wrapper data-v-3b38b912"><zlnavbar vue-id="6a114c6f-1" isBack="{{true}}" class="data-v-3b38b912" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" class="data-v-3b38b912">密码修改</view></zlnavbar><view class="login-form data-v-3b38b912"><image class="pwd-edit-icon data-v-3b38b912" src="../../static/images/login/pwd-edit.png"></image><view class="login-info data-v-3b38b912">请为您的账号<label class="_span data-v-3b38b912">{{userInfo.phone}}</label><view class="data-v-3b38b912">设置一个新密码</view></view><block wx:if="{{hasPass}}"><view class="form-item data-v-3b38b912"><image class="form-icon data-v-3b38b912" src="../../static/images/login/lock-icon.svg"></image><block wx:if="{{oldPwdType}}"><input type="password" placeholder="原密码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','oldPassword']]]]]}}" value="{{loginForm.oldPassword}}" bindinput="__e" class="data-v-3b38b912"/></block><block wx:else><input type="text" placeholder="8-20字符须包含数字、字母及特殊字符" adjust-position="{{false}}" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','oldPassword']]]]]}}" value="{{loginForm.oldPassword}}" bindinput="__e" class="data-v-3b38b912"/></block><block wx:if="{{oldPwdType}}"><view data-event-opts="{{[['tap',[['handleInputTypeChange',['oldPwd']]]]]}}" class="iconfont icon-yanjing_xianshi _i data-v-3b38b912" bindtap="__e"></view></block><block wx:else><view data-event-opts="{{[['tap',[['handleInputTypeChange',['oldPwd']]]]]}}" class="iconfont icon-yanjing_yincang _i data-v-3b38b912" bindtap="__e"></view></block></view></block><block wx:if="{{!hasPass}}"><view class="form-item data-v-3b38b912"><image class="form-icon data-v-3b38b912" src="../../static/images/login/msg-icon.png"></image><input type="text" placeholder="请输入短信验证码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','messageCode']]]]]}}" value="{{loginForm.messageCode}}" bindinput="__e" class="data-v-3b38b912"/><button class="verify-btn data-v-3b38b912" disabled="{{disabled}}" data-event-opts="{{[['tap',[['getCode',['$event']]]]]}}" bindtap="__e">{{''+btnText}}</button></view></block><view class="form-item data-v-3b38b912"><image class="form-icon data-v-3b38b912" src="../../static/images/login/lock-icon.png"></image><block wx:if="{{pwdType}}"><input type="password" placeholder="8-20字符 须包含数字、字母及特殊字符" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','password']]]]]}}" value="{{loginForm.password}}" bindinput="__e" class="data-v-3b38b912"/></block><block wx:else><input type="text" placeholder="8-20字符须包含数字、字母及特殊字符" adjust-position="{{false}}" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','password']]]]]}}" value="{{loginForm.password}}" bindinput="__e" class="data-v-3b38b912"/></block><block wx:if="{{pwdType}}"><view data-event-opts="{{[['tap',[['handleInputTypeChange',['pwd']]]]]}}" class="iconfont icon-yanjing_xianshi _i data-v-3b38b912" bindtap="__e"></view></block><block wx:else><view data-event-opts="{{[['tap',[['handleInputTypeChange',['pwd']]]]]}}" class="iconfont icon-yanjing_yincang _i data-v-3b38b912" bindtap="__e"></view></block></view><view class="form-item data-v-3b38b912"><image class="form-icon data-v-3b38b912" src="../../static/images/login/lock-icon.png"></image><block wx:if="{{newPwdType}}"><input type="password" placeholder="确认密码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','confirmPassword']]]]]}}" value="{{loginForm.confirmPassword}}" bindinput="__e" class="data-v-3b38b912"/></block><block wx:else><input type="text" placeholder="确认密码" placeholder-style="color:#ccc" adjust-position="{{false}}" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','confirmPassword']]]]]}}" value="{{loginForm.confirmPassword}}" bindinput="__e" class="data-v-3b38b912"/></block><block wx:if="{{newPwdType}}"><view data-event-opts="{{[['tap',[['handleInputTypeChange',['newPwd']]]]]}}" class="iconfont icon-yanjing_xianshi _i data-v-3b38b912" bindtap="__e"></view></block><block wx:else><view data-event-opts="{{[['tap',[['handleInputTypeChange',['newPwd']]]]]}}" class="iconfont icon-yanjing_yincang _i data-v-3b38b912" bindtap="__e"></view></block></view><view class="form-item data-v-3b38b912"><image class="form-icon data-v-3b38b912" src="../../static/images/login/key-icon.png"></image><input type="text" placeholder="请输入图形验证码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','imgCode']]]]]}}" value="{{loginForm.imgCode}}" bindinput="__e" class="data-v-3b38b912"/><image class="verifiy-code data-v-3b38b912" src="{{verifyCodeImg}}" data-event-opts="{{[['tap',[['getVerifyImgCode',['$event']]]]]}}" bindtap="__e"></image></view><view class="bottom-btn-box data-v-3b38b912"><view data-event-opts="{{[['tap',[['updatePwdSubmit',['$event']]]]]}}" class="main-btn data-v-3b38b912" bindtap="__e">保存新密码</view></view></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="6a114c6f-2" modalData="{{modalData}}" btnVisible="{{true}}" data-event-opts="{{[['^cancel',[['handleCancel']]],['^confirm',[['handleConfirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-3b38b912" bind:__l="__l"></modal-dialog></view>