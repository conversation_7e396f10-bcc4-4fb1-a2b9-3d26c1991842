<view class="login-wrapper"><zlnavbar vue-id="9e743536-1" isBack="{{true}}" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content">密码登录</view></zlnavbar><view class="login-form"><image class="logo" src="../../static/images/logo.png"></image><view class="form-item"><image class="form-icon" src="../../static/images/login/phone-icon.png"></image><input type="text" placeholder="请输入手机号码" placeholder-style="color:#ccc" data-event-opts="{{[['input',[['setInputValue',['$event','phone']]]]]}}" value="{{loginForm.phone}}" bindinput="__e"/></view><view class="form-item"><image class="form-icon" src="../../static/images/login/lock-icon.svg"></image><block wx:if="{{showPassword}}"><input class="uni-input" type="password" placeholder="请输入登录密码" placeholder-style="color:#ccc" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['loginForm']]]]]}}" value="{{loginForm.password}}" bindinput="__e"/></block><block wx:else><input class="uni-input" type="text" placeholder="请输入登录密码" placeholder-style="color:#ccc" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['loginForm']]]]]}}" value="{{loginForm.password}}" bindinput="__e"/></block><block wx:if="{{showPassword}}"><view data-event-opts="{{[['tap',[['handleInputTypeChange',['$event']]]]]}}" class="iconfont icon-yanjing_xianshi _i" bindtap="__e"></view></block><block wx:else><view data-event-opts="{{[['tap',[['handleInputTypeChange',['$event']]]]]}}" class="iconfont icon-yanjing_yincang _i" bindtap="__e"></view></block></view><view class="form-item"><image class="form-icon" src="../../static/images/login/key-icon.png"></image><input class="vue-ref" type="text" placeholder="请输入图形验证码" placeholder-style="color:#ccc" maxlength="20" data-ref="codeRef" data-event-opts="{{[['input',[['__set_model',['$0','imgCode','$event',[]],['loginForm']],['setInputValue',['$event','imgCode']]]]]}}" value="{{loginForm.imgCode}}" bindinput="__e"/><image class="verifiy-code" src="{{verifyCodeImg}}" data-event-opts="{{[['tap',[['getVerifyImgCode',['$event']]]]]}}" bindtap="__e"></image></view><view class="bottom-btn-box"><view data-event-opts="{{[['tap',[['handleLogin',['$event']]]]]}}" class="main-btn" bindtap="__e">登录</view></view><view class="form-info"><view>忘记密码？<label data-event-opts="{{[['tap',[['goPath',['resetPwd']]]]]}}" bindtap="__e" class="_span">密码重置</label></view><view><label data-event-opts="{{[['tap',[['goPath',['index']]]]]}}" bindtap="__e" class="_span">验证码登录</label></view></view></view><view class="bottom-info"><checkbox class="checked-btn" color="#1C6CED" checked="{{checkState}}" data-event-opts="{{[['tap',[['handleCheckboxChange',['$0'],['checkState']]]]]}}" bindtap="__e"></checkbox><view>登录即代表同意<label data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="_span">用户服务协议</label>和<label data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e" class="_span">个人信息保护政策</label>，首次登录用户请使用短信验证码方式登录，登录成功后将自动为您创建账号。</view></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="9e743536-2" modalData="{{modalData}}" btnVisible="{{true}}" data-event-opts="{{[['^cancel',[['handleCancel']]],['^confirm',[['handleConfirm']]]]}}" bind:cancel="__e" bind:confirm="__e" bind:__l="__l"></modal-dialog><footer style="width:100%;" vue-id="9e743536-3" bind:__l="__l"></footer></view>