{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/fileManage.vue?d774", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/fileManage.vue?dbd1", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/fileManage.vue?9a6a", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/fileManage.vue?5401", "uni-app:///packagePages/my/fileManage.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/fileManage.vue?e21e", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/fileManage.vue?9820", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/fileManage.vue?b1c8", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/fileManage.vue?9db8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "authStatus", "codeVisible", "authCode", "second", "timeStr", "timer", "codeTimer", "time", "timeStatus", "refreshState", "disabled", "computed", "userInfo", "watch", "deep", "handler", "mounted", "onHide", "clearTimeout", "destroyed", "methods", "getFamilyStatus", "api", "idCard", "uni", "countDown", "refreshCode", "title", "getAuthCode", "id", "wxbarcode", "getAuthCode1", "setTimeout", "setAuthState", "showAuthToast", "handleSwitchChange", "handleAuthBtnCountdown", "handleCodeShowChange", "handleCodeCountdown", "clearTimer", "handleShowAuthToast"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACa;AACyB;;;AAG/F;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAO,CAAC,4CAAmC;AACtD,WAAW,mBAAO,CAAC,4CAAmC;AACtD;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAywB,CAAgB,yxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqD7xB;AACA;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAb;MACAc;MACAC,6BAEA;IACA;EAEA;EACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;EACA;EACAC,iCAEA;EACAC;IACA;IACAC;MAAA;MACAC;QACAC;MACA;QACA;QACA;QACA;UACAC;UACA;QACA;MACA;IACA;IACAC;MACA;QACAP;QACA;QACA;MACA;MACA;QACAA;QACA;QACA;QACA;MACA;IACA;IACAQ;MACAF;QACAG;MACA;MACA;IACA;IACAC;MAAA;MACAJ;QACAG;MACA;MACAL;QACAO;MACA;QACA;QACA;UACA;UACAC;UACA;QACA;;QACAN;MACA;IACA;IACAO;MAAA;MACA;MACAP;MACAQ;QACAV;UACAO;QACA;UACAL;UACA;UACA;YACAM;YACA;UACA;;UACA;QACA;MACA;IACA;IACAG;MAAA;MACA;MACA;QACAX;UACAO;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;QACAP;UACAO;QACA;UACA;QACA;UACA;UACA;QACA;MACA;;MACAG;QACA;QACA;MACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACAX;QAEA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACAN;QACAA;QACAM;MACA;IACA;IACAY;MAAA;MACA;QACAlB;QACAM;QACA;MACA;MACA;QACA;QACAA;QACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACApB;QACAM;QACAA;QACA;MACA;MACA;QACA;QACAA;QACA;QACA;MACA;IACA;IACAe;MACA;MACA;MACA;MACArB;IACA;IACAsB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3QA;AAAA;AAAA;AAAA;AAA6lC,CAAgB,glCAAG,EAAC,C;;;;;;;;;;;ACAjnC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAg9C,CAAgB,o6CAAG,EAAC,C;;;;;;;;;;;ACAp+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/my/fileManage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/my/fileManage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fileManage.vue?vue&type=template&id=deb8c8be&scoped=true&\"\nvar renderjs\nimport script from \"./fileManage.vue?vue&type=script&lang=js&\"\nexport * from \"./fileManage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fileManage.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./fileManage.vue?vue&type=style&index=1&id=deb8c8be&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"deb8c8be\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/my/fileManage.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fileManage.vue?vue&type=template&id=deb8c8be&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = require(\"@/static/images/my/menu-icon1.svg\")\n  var m1 = require(\"@/static/images/my/menu-icon6.svg\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fileManage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fileManage.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<zlnavbar bgColor=\"bg-white\" :isBack=\"true\">\r\n\t\t\t<block slot=\"content\">本人档案授权管理</block>\r\n\t\t</zlnavbar>\r\n\t\t<view class=\"main-box\">\r\n\t\t\t<view class=\"list-box\">\r\n\t\t\t\t<view class=\"list-item\">\r\n\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon1.svg`)\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t<view class=\"flex \">档案授权状态</view>\r\n\t\t\t\t\t\t<view class=\"flex text-right switch-box\">\r\n\t\t\t\t\t\t\t<switch color='#1C6CED' :disabled=\"disabled\" :checked=\"authStatus\"\r\n\t\t\t\t\t\t\t\t@change=\"handleSwitchChange\"></switch>\r\n\t\t\t\t\t\t\t<span class=\"mask-btn\" v-if=\"disabled\" @click=\"showAuthToast\"></span>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item\">\r\n\t\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon6.svg`)\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"flex \">本人档案授权</view>\r\n\t\t\t\t\t\t\t<view class=\"iconfont icon-shuaxin\" v-if=\"authStatus\" @click=\"refreshCode\">\r\n\t\t\t\t\t\t\t\t刷新</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"flex text-right\" >\r\n\t\t\t\t\t\t\t\t<button  @click=\"handleCodeShowChange\"></button>\r\n\t\t\t\t\t\t\t\t<view v-else>剩余有效时间：<span>{{timeStr}}</span></view>\r\n\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t<!-- <view class=\"text-right\" v-else>授权状态关闭，无法获取授权码</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"list-info-box\" v-if=\"authCode\">\r\n\t\t\t\t\t\t<view>请出示或告知医生此授权码</view>\r\n\t\t\t\t\t\t<view class=\"auth-code\">{{authCode}}</view>\r\n\t\t\t\t\t\t<canvas class=\"bar-code\" canvas-id=\"barcode\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"bottom-tips\">\r\n\t\t\t<view class=\"\">注意事项：</view>\r\n\t\t\t<view class=\"\">1.健康档案调阅授权仅限单次授权，再次调阅再次授权。</view>\r\n\t\t\t<view class=\"\">2.授权码的有效期为5分钟，超时则失效，若仍要使用授权码则需要重新获取。</view>\r\n\t\t\t<view class=\"\">3.若档案授权状态关闭，则健康档案无法被医疗机构调用，即无法获取授权码。</view>\r\n\t\t\t<view class=\"\">4.档案授权状态在5分钟之内仅可修改一次。</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\timport {\r\n\t\tformatCountDownTime,\r\n\t\ttoast,\r\n\t\tthrottle,\r\n\t\tdebounce\r\n\t} from \"@/utils/util.js\"\r\n\timport wxbarcode from 'wxbarcode'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tauthStatus: false,\r\n\t\t\t\tcodeVisible: false,\r\n\t\t\t\tauthCode: '',\r\n\t\t\t\tsecond: '',\r\n\t\t\t\ttimeStr: '',\r\n\t\t\t\ttimer: '',\r\n\t\t\t\tcodeTimer: '',\r\n\t\t\t\ttime: '',\r\n\t\t\t\ttimeStatus: 0,\r\n\t\t\t\trefreshState: false,\r\n\t\t\t\tdisabled: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tuserInfo() {\r\n\t\t\t\treturn this.$store.getters.userInfo\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tauthStatus: {\r\n\t\t\t\tdeep: true,\r\n\t\t\t\thandler() {\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.getFamilyStatus()\r\n\t\t},\r\n\t\tonHide() {\r\n\t\t\tclearTimeout(this.timer)\r\n\t\t\t// clearTimeout(this.codeTimer)\r\n\t\t\t// this.countDown()\r\n\t\t},\r\n\t\tdestroyed() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 查询当前成员档案状态\r\n\t\t\tgetFamilyStatus() {\r\n\t\t\t\tapi.documentFamilyStatus({\r\n\t\t\t\t\tidCard: this.userInfo.idCardDecrypt\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.userInfo.documentEmpowerStatus = res.data\r\n\t\t\t\t\tthis.authStatus = this.userInfo.documentEmpowerStatus == 1 ? true : false\r\n\t\t\t\t\tif (this.authStatus) {\r\n\t\t\t\t\t\tuni.showLoading()\r\n\t\t\t\t\t\tthis.getAuthCode()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcountDown() {\r\n\t\t\t\tif (uni.getStorageSync(\"timeStatus\") != 0) {\r\n\t\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\t\tthis.timeStatus = uni.getStorageSync(\"timeStatus\")\r\n\t\t\t\t\tthis.handleAuthBtnCountdown(uni.getStorageSync(\"timeStatus\"))\r\n\t\t\t\t}\r\n\t\t\t\tif (uni.getStorageSync(\"codeTimeStatus\") != 0) {\r\n\t\t\t\t\tclearTimeout(this.codeTimer)\r\n\t\t\t\t\tthis.codeVisible = true\r\n\t\t\t\t\tthis.authCode = uni.getStorageSync('authCode', )\r\n\t\t\t\t\tthis.handleCodeCountdown(uni.getStorageSync(\"codeTimeStatus\"))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\trefreshCode() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t})\r\n\t\t\t\tthis.getAuthCode()\r\n\t\t\t},\r\n\t\t\tgetAuthCode: debounce(function(e) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t})\r\n\t\t\t\tapi.documentGetCodeAcess({\r\n\t\t\t\t\tid: uni.getStorageSync('resident_id')\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.authCode = res.data\r\n\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\tvar symbol = res.data.length % 2 !== 0 ? \" \" : ''\r\n\t\t\t\t\t\twxbarcode.barcode('barcode', res.data + symbol, 520, 160)\r\n\t\t\t\t\t\t// uni.setStorageSync('authCode', res.data)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t})\r\n\t\t\t}),\r\n\t\t\tgetAuthCode1() {\r\n\t\t\t\tthis.refreshState = true\r\n\t\t\t\tuni.showLoading({})\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tapi.documentGetCodeAcess({\r\n\t\t\t\t\t\tid: uni.getStorageSync('resident_id')\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tthis.authCode = res.data\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\twxbarcode.barcode('barcode', res.data + ' ', 700, 200)\r\n\t\t\t\t\t\t\t// uni.setStorageSync('authCode', res.data)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.refreshState = false\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetAuthState(state) {\r\n\t\t\t\tthis.disabled = true\r\n\t\t\t\tif (state) {\r\n\t\t\t\t\tapi.documentOpen({\r\n\t\t\t\t\t\tid: uni.getStorageSync('resident_id')\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tthis.userInfo.documentEmpowerStatus = 1\r\n\t\t\t\t\t\tthis.getAuthCode()\r\n\t\t\t\t\t}).catch(e => {\r\n\t\t\t\t\t\tthis.authStatus = false\r\n\t\t\t\t\t\t// this.userInfo.documentEmpowerStatus = 0\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapi.documentCancle({\r\n\t\t\t\t\t\tid: uni.getStorageSync('resident_id')\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tthis.userInfo.documentEmpowerStatus = 0\r\n\t\t\t\t\t}).catch(e => {\r\n\t\t\t\t\t\tthis.authStatus = true\r\n\t\t\t\t\t\t// this.userInfo.documentEmpowerStatus = 1\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.disabled = false\r\n\t\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\t}, 50000);\r\n\t\t\t},\r\n\t\t\tshowAuthToast() {\r\n\t\t\t\ttoast('档案授权状态在5分钟之内仅可修改一次', 'none', 1000)\r\n\t\t\t},\r\n\t\t\t// 授权按钮状态切换\r\n\t\t\thandleSwitchChange(e) {\r\n\t\t\t\tthis.authStatus = e.detail.value\r\n\t\t\t\tthis.clearTimer()\r\n\t\t\t\tif (this.authStatus) {\r\n\t\t\t\t\tthis.setAuthState(true)\r\n\t\t\t\t\tuni.showLoading({})\r\n\r\n\t\t\t\t\tthis.timeStatus = 1\r\n\t\t\t\t\tvar time = 5 * 60;\r\n\t\t\t\t\tthis.handleAuthBtnCountdown(time)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.codeVisible = false\r\n\t\t\t\t\tthis.setAuthState(false)\r\n\t\t\t\t\tthis.clearTimer()\r\n\t\t\t\t\tthis.authCode = ''\r\n\t\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\t\tclearTimeout(this.codeTimer)\r\n\t\t\t\t\tuni.setStorageSync(\"timeStatus\", 0)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thandleAuthBtnCountdown(time) {\r\n\t\t\t\tif (time === 0) {\r\n\t\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\t\tuni.setStorageSync(\"timeStatus\", time)\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.timeStatus = time - 1;\r\n\t\t\t\t\tuni.setStorageSync(\"timeStatus\", time)\r\n\t\t\t\t\t// formatCountDownTime(this.timeStatus)\r\n\t\t\t\t\tthis.handleAuthBtnCountdown(this.timeStatus)\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t\thandleCodeShowChange() {\r\n\t\t\t\tthis.clearTimer()\r\n\t\t\t\tthis.getAuthCode()\r\n\t\t\t\tthis.second = 5 * 60;\r\n\t\t\t\tthis.timeStr = formatCountDownTime(this.second)\r\n\t\t\t\tthis.disabled = true\r\n\t\t\t\tthis.handleCodeCountdown(this.second)\r\n\t\t\t\tthis.codeVisible = true\r\n\t\t\t},\r\n\t\t\t// 验证码倒计时\r\n\t\t\thandleCodeCountdown(time) {\r\n\t\t\t\tif (time === 0) {\r\n\t\t\t\t\tthis.codeVisible = false\r\n\t\t\t\t\tclearTimeout(this.codeTimer)\r\n\t\t\t\t\tuni.setStorageSync(\"codeTimeStatus\", time)\r\n\t\t\t\t\tuni.setStorageSync('authCode', '')\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.codeTimer = setTimeout(() => {\r\n\t\t\t\t\tvar second = time - 1;\r\n\t\t\t\t\tuni.setStorageSync(\"codeTimeStatus\", time)\r\n\t\t\t\t\tthis.timeStr = formatCountDownTime(second)\r\n\t\t\t\t\tthis.handleCodeCountdown(second)\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t\tclearTimer() {\r\n\t\t\t\tthis.timer = ''\r\n\t\t\t\tthis.second = ''\r\n\t\t\t\tthis.timeStr = ''\r\n\t\t\t\tclearTimeout(this.codeTimer)\r\n\t\t\t},\r\n\t\t\thandleShowAuthToast() {\r\n\t\t\t\ttoast('档案授权状态在5分钟之内仅可修改一次')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #F5F6F7 !important;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t// /deep/ switch .wx-switch-input-disabled {\r\n\t// \t// background: #ccc !important;\r\n\t// }\r\n\r\n\t// /deep/ .wx-switch-input-checked {\r\n\t// \tbackground-color: #B5C7FF !important;\r\n\t// }\r\n\r\n\t.main-box {\r\n\t\tpadding: 16px;\r\n\t}\r\n\r\n\t.switch-box {\r\n\t\tposition: relative;\r\n\r\n\t\t.mask-btn {\r\n\t\t\tposition: absolute;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\twidth: 45px;\r\n\t\t\theight: 25px;\r\n\t\t\tz-index: 999;\r\n\t\t}\r\n\t}\r\n\r\n\t.list-box {\r\n\t\t.list-item-box {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12px;\r\n\r\n\t\t\t.list-info-box {\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t\t\t\t// padding-bottom: 30px;\r\n\t\t\t\t// padding-top: 15px;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\t// padding-bottom: 5px;\r\n\r\n\t\t\t\t.bar-code {\r\n\t\t\t\t\theight: 130px;\r\n\t\t\t\t\tmargin-top: 15px;\r\n\t\t\t\t\tmargin-left: 50px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.auth-code {\r\n\t\t\t\t\tfont-size: 60px;\r\n\t\t\t\t\tfont-weight: 860;\r\n\t\t\t\t\tcolor: #1c6ced;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 17px;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12px;\r\n\t\t\tpadding: 16px;\r\n\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder: 0;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 24px;\r\n\t\t\t\theight: 24px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list-cell {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #0D0B22;\r\n\t\t\tmargin-left: 12px;\r\n\r\n\t\t\t.list-cell-label {\r\n\t\t\t\tpadding: 1px 5px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tbackground: #D54941;\r\n\t\t\t\tborder-radius: 16px;\r\n\t\t\t\tmargin-left: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t.icon-shuaxin {\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: #1C6CED;\r\n\t\t\t}\r\n\r\n\t\t\t.text-right {\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\topacity: 0.9;\r\n\r\n\t\t\t\tspan {\r\n\t\t\t\t\tcolor: #1C6CED;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tbutton {\r\n\t\t\t\t\tfont-size: 14px !important;\r\n\t\t\t\t\theight: 28px;\r\n\t\t\t\t\tline-height: 28px;\r\n\t\t\t\t\tborder-radius: 6px;\r\n\t\t\t\t\tbackground-color: #F2F3FF;\r\n\t\t\t\t\tcolor: #1C6CED;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.bottom-tips {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t\tline-height: 20px;\r\n\t\tline-height: 24px;\r\n\t\tpadding: 30px;\r\n\t\tpadding-top: 0;\r\n\r\n\t\tview {\r\n\t\t\t&:first-child {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fileManage.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fileManage.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542307967\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fileManage.vue?vue&type=style&index=1&id=deb8c8be&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fileManage.vue?vue&type=style&index=1&id=deb8c8be&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542307994\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}