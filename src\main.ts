import { createApp } from 'vue'
import App from './App.vue'
import router from './routers'
import pinia from "./store";
import { registerElIcons } from "@/plugins/ElIcons"
// 引入全局组件布局
import PageWrapLayout from '@/components/PageWrapLayout/index.vue'
// 权限路由
import './permission'
// svg-icons注册导入
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon/index.vue'// svg component
// UI框架 element-plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// 引入暗黑模式 element-plus 2.2 内置暗黑模式
import 'element-plus/theme-chalk/dark/css-vars.css'
// 自定义暗黑模式
import "@/styles/element-dark.scss";
// 引入阿里图标库
import "@/assets/iconfont/iconfont.css";
import "@/assets/iconfont/iconfont.js";

const app = createApp(App)
registerElIcons(app)

app.component('svg-icon', SvgIcon)
app.component('PageWrapLayout', PageWrapLayout)


// 处理字体适配
import '@/utils/rem'

import WangEdior from '@/components/WangEdior/index.vue'

import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import '@vueup/vue-quill/dist/vue-quill.bubble.css'
app.component('QuillEditor', QuillEditor)

// 前端自定义字典以及解析
import { getWebDict, getWebDictName, getDict, getDictName } from '@/utils/dictionary'
app.config.globalProperties.$getWebDict = getWebDict;  // 前端字典 array
app.config.globalProperties.$getWebDictName = getWebDictName; // 前端字典 code -> name
app.config.globalProperties.$getDict = getDict; // 后端字典 array
app.config.globalProperties.$getDictName = getDictName;  // 字典获取 code -> name

// 验证类型方法
import { validateNull, validPassWord, checkphone, checkTelphone, validAttributeKey, checkIdCard, checkName, IdCard, validEmail, creditCode } from '@/utils/validate'

app.config.globalProperties.$validateNull = validateNull  // 判空、null、undefined
app.config.globalProperties.$validPassWord = validPassWord // 密码格式效验
app.config.globalProperties.$checkphone = checkphone // 手机号座机号正则验证
app.config.globalProperties.$checkTelphone = checkTelphone // 手机号正则验证
app.config.globalProperties.$validAttributeKey = validAttributeKey // 配置项属性名验证
app.config.globalProperties.$checkIdCard = checkIdCard  // 检验身份证合法性
app.config.globalProperties.$checkName = checkName  // 检验姓名合法性
app.config.globalProperties.$validEmail = validEmail  // 检验邮箱合法性
app.config.globalProperties.$creditCode = creditCode  // 效验社会统一信用代码
app.config.globalProperties.$IdCard = IdCard  // 根据身份证号获取生日、性别、年龄
  
import { getDatePicker, getUserInfo, birthDateToYear, uuid, deepClone, isWeixin, toChinesNum, getSysConfigvalue, getDateValue, numberFormat, fileDownload, tranListToTreeData } from '@/utils/index'
// 全局公共方法
app.config.globalProperties.$getDatePicker = getDatePicker // 时间戳转日期[date,chinese,其他(年月日时分秒)]
app.config.globalProperties.$getUserInfo = getUserInfo // 获取用户信息(带参数)
app.config.globalProperties.$birthDateToYear = birthDateToYear // 出生日期转年龄
app.config.globalProperties.$uuid = uuid // 随机字符串
app.config.globalProperties.$deepClone = deepClone // 深拷贝
app.config.globalProperties.$isWeixin = isWeixin // 是否微信内置浏览器
app.config.globalProperties.$toChinesNum = toChinesNum // 数字转换中文金额大写
app.config.globalProperties.$getSysConfigvalue = getSysConfigvalue //获取配置项
app.config.globalProperties.$getDateValue = getDateValue // 获取一个距离当前时间的一个时间
app.config.globalProperties.$numberFormat = numberFormat // 数字格式，每三位加逗号
app.config.globalProperties.$fileDownload = fileDownload // 文件下载，多种文件格式
app.config.globalProperties.$tranListToTreeData = tranListToTreeData // 将对象数组转化为树机构

// 全局常量
app.config.globalProperties.$sysCode = 1 // 1-pc、2-staff、3-mass
app.config.globalProperties.$technicalSupport = '西安网是科技发展有限公司'
app.config.globalProperties.$sysTel = '029-68255870'

// 各地市配置
if (import.meta.env.VITE_APP_CITY_CODE == 6107) {
    app.config.globalProperties.$areaCode1 = '61'
    app.config.globalProperties.$areaCode2 = '6107'
    app.config.globalProperties.$splitAreaCode = '61,6107'
    app.config.globalProperties.$areaName1 = '陕西省'
    app.config.globalProperties.$areaName2 = '汉中市'
    app.config.globalProperties.$lng = 107.0138
    app.config.globalProperties.$lat = 33.0436
    app.config.globalProperties.$address = "汉中市汉台区"
    app.config.globalProperties.$copyright = 'Copyright ©2023 汉中市卫生健康委员会'
}

// html生成img方法（场所码中this.util.Canvas2Image.saveAsPNG生成高清图片）
import util from '@/utils/canvas/index.js'
app.config.globalProperties.util = util

// html转pdf（注册后，直接调用getPdf这个原型链方法生成pdf）
import htmlToPdf from '@/utils/htmlToPdf';
app.use(htmlToPdf);

// 网页水印
import watermark from '@/utils/watermark';
watermark.set('高新控股')

// 电子签名
import VueSignaturePad from "vue-signature-pad";
app.use(VueSignaturePad);
 
app.use(pinia)
app.use(router)
app.use(ElementPlus).mount('#app')

