.main-box.data-v-50f93fd9 {
  position: relative;
  text-align: center;
  padding: 20px 26px;
  height: calc(100% - 200px);
  overflow: auto;
}
.main-box .main-content.data-v-50f93fd9 {
  margin-top: 100px;
}
.main-box .main-content image.data-v-50f93fd9 {
  width: 60px;
  margin-bottom: 20px;
}
.main-box .main-content .main-title.data-v-50f93fd9 {
  color: black;
  font-size: 14px;
  text-align: center;
}
.main-box .main-content .main-tips.data-v-50f93fd9 {
  width: 80%;
  margin: 0 auto;
  margin-top: 12px;
  margin-bottom: 76px;
  font-size: 12px;
  color: #000000a6;
  text-align: center;
}
.main-box .main-content .main-button.data-v-50f93fd9 {
  position: relative;
}
.main-box .main-content .main-button view.data-v-50f93fd9 {
  color: white;
  display: flex;
  width: 137px;
  height: 40px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-radius: 20px;
  border: 1px solid #FFF;
  background: linear-gradient(90deg, #338AEB 0%, #2DE2FB 100%);
  position: absolute;
  z-index: 1000;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.main-box .main-content .main-button view.bg1.data-v-50f93fd9 {
  width: 173px;
  height: 173px;
  position: absolute;
  fill: #3296ed1a;
  -webkit-filter: blur(22.1px);
          filter: blur(22.1px);
  top: -60px;
  left: 40%;
  opacity: 0.1;
  z-index: 100;
}
.main-box .main-content .main-button view.bg2.data-v-50f93fd9 {
  width: 134px;
  height: 135px;
  flex-shrink: 0;
  fill: #2de0fb1a;
  -webkit-filter: blur(22.1px);
          filter: blur(22.1px);
  position: absolute;
  left: 70%;
  bottom: -140px;
  opacity: 0.1;
  z-index: 100;
}
.familyList.data-v-50f93fd9 {
  position: absolute;
  top: 10px;
  left: 10px;
}
.familyList text.data-v-50f93fd9 {
  display: inline-block;
  background-color: #F7F7F7;
  color: #5F5F5F;
  padding: 4px 24px;
  border-radius: 20px;
}
.familyList .changedMenu.data-v-50f93fd9 {
  background-color: #E0EAFF;
  color: #376BE6;
}

