<view class="data-v-deb8c8be"><zlnavbar vue-id="f85f4cf6-1" bgColor="bg-white" isBack="{{true}}" class="data-v-deb8c8be" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" class="data-v-deb8c8be">本人档案授权管理</view></zlnavbar><view class="main-box data-v-deb8c8be"><view class="list-box data-v-deb8c8be"><view class="list-item data-v-deb8c8be"><image mode="widthFix" src="{{$root.m0}}" class="data-v-deb8c8be"></image><view class="list-cell data-v-deb8c8be"><view class="flex data-v-deb8c8be">档案授权状态</view><view class="flex text-right switch-box data-v-deb8c8be"><switch color="#1C6CED" disabled="{{disabled}}" checked="{{authStatus}}" data-event-opts="{{[['change',[['handleSwitchChange',['$event']]]]]}}" bindchange="__e" class="data-v-deb8c8be"></switch><block wx:if="{{disabled}}"><label data-event-opts="{{[['tap',[['showAuthToast',['$event']]]]]}}" class="mask-btn _span data-v-deb8c8be" bindtap="__e"></label></block></view></view></view><view class="list-item-box data-v-deb8c8be"><view class="list-item data-v-deb8c8be"><image mode="widthFix" src="{{$root.m1}}" class="data-v-deb8c8be"></image><view class="list-cell data-v-deb8c8be"><view class="flex data-v-deb8c8be">本人档案授权</view><block wx:if="{{authStatus}}"><view data-event-opts="{{[['tap',[['refreshCode',['$event']]]]]}}" class="iconfont icon-shuaxin data-v-deb8c8be" bindtap="__e">刷新</view></block></view></view><block wx:if="{{authCode}}"><view class="list-info-box data-v-deb8c8be"><view class="data-v-deb8c8be">请出示或告知医生此授权码</view><view class="auth-code data-v-deb8c8be">{{authCode}}</view><canvas class="bar-code data-v-deb8c8be" canvas-id="barcode"></canvas></view></block></view></view></view><view class="bottom-tips data-v-deb8c8be"><view class="data-v-deb8c8be">注意事项：</view><view class="data-v-deb8c8be">1.健康档案调阅授权仅限单次授权，再次调阅再次授权。</view><view class="data-v-deb8c8be">2.授权码的有效期为5分钟，超时则失效，若仍要使用授权码则需要重新获取。</view><view class="data-v-deb8c8be">3.若档案授权状态关闭，则健康档案无法被医疗机构调用，即无法获取授权码。</view><view class="data-v-deb8c8be">4.档案授权状态在5分钟之内仅可修改一次。</view></view></view>