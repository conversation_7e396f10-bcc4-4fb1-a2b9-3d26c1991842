<view class="uni-datetime-picker"><view data-event-opts="{{[['tap',[['initTimePicker',['$event']]]]]}}" bindtap="__e"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><view class="{{['uni-datetime-picker-timebox-pointer',(disabled)?'uni-datetime-picker-disabled':'',(border)?'uni-datetime-picker-timebox':'']}}"><text class="uni-datetime-picker-text">{{time}}</text><block wx:if="{{!time}}"><view class="uni-datetime-picker-time"><text class="uni-datetime-picker-text">{{selectTimeText}}</text></view></block></view></block></view><block wx:if="{{visible}}"><view class="uni-datetime-picker-mask" id="mask" data-event-opts="{{[['tap',[['tiggerTimePicker',['$event']]]]]}}" bindtap="__e"></view></block><block wx:if="{{visible}}"><view class="{{['uni-datetime-picker-popup',dateShow&&timeShow?'':'fix-nvue-height']}}" style="{{(fixNvueBug)}}"><view class="uni-title"><text class="uni-datetime-picker-text">{{selectTimeText}}</text></view><block wx:if="{{dateShow}}"><view class="uni-datetime-picker__container-box"><picker-view class="uni-datetime-picker-view" indicator-style="{{indicatorStyle}}" value="{{ymd}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><picker-view-column><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.m0}}</text></view></block></picker-view-column><picker-view-column><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.m1}}</text></view></block></picker-view-column><picker-view-column><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.m2}}</text></view></block></picker-view-column></picker-view><text class="uni-datetime-picker-sign sign-left">-</text><text class="uni-datetime-picker-sign sign-right">-</text></view></block><block wx:if="{{timeShow}}"><view class="uni-datetime-picker__container-box"><picker-view class="{{['uni-datetime-picker-view',hideSecond?'time-hide-second':'']}}" indicator-style="{{indicatorStyle}}" value="{{hms}}" data-event-opts="{{[['change',[['bindTimeChange',['$event']]]]]}}" bindchange="__e"><picker-view-column><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.m3}}</text></view></block></picker-view-column><picker-view-column><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.m4}}</text></view></block></picker-view-column><block wx:if="{{!hideSecond}}"><picker-view-column><block wx:for="{{$root.l5}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.m5}}</text></view></block></picker-view-column></block></picker-view><text class="{{['uni-datetime-picker-sign',hideSecond?'sign-center':'sign-left']}}">:</text><block wx:if="{{!hideSecond}}"><text class="uni-datetime-picker-sign sign-right">:</text></block></view></block><view class="uni-datetime-picker-btn"><view data-event-opts="{{[['tap',[['clearTime',['$event']]]]]}}" bindtap="__e"><text class="uni-datetime-picker-btn-text">{{clearText}}</text></view><view class="uni-datetime-picker-btn-group"><view data-event-opts="{{[['tap',[['tiggerTimePicker',['$event']]]]]}}" class="uni-datetime-picker-cancel" bindtap="__e"><text class="uni-datetime-picker-btn-text">{{cancelText}}</text></view><view data-event-opts="{{[['tap',[['setTime',['$event']]]]]}}" bindtap="__e"><text class="uni-datetime-picker-btn-text">{{okText}}</text></view></view></view></view></block></view>