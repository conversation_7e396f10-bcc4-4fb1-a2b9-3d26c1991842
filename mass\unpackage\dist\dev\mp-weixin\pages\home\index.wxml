<view class="wrapper data-v-71e217db"><zlnavbar vue-id="6b3d36ea-1" isBack="{{false}}" class="data-v-71e217db" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" class="data-v-71e217db">健康档案</view></zlnavbar><block wx:if="{{familyList[0].name}}"><scroll-menu vue-id="6b3d36ea-2" menuData="{{familyList}}" menuIndex="{{menuIndex}}" data-event-opts="{{[['^menuChange',[['handleTabMenuChange']]]]}}" bind:menuChange="__e" class="data-v-71e217db" bind:__l="__l"></scroll-menu></block><view class="main-box data-v-71e217db"><view class="menu-title data-v-71e217db">档案服务记录</view><view class="menu-box data-v-71e217db"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleMenuChange',['$0'],[[['tempMenuList','',index]]]]]]]}}" class="{{['data-v-71e217db','menu-item','menu-grey-item']}}" bindtap="__e"><image src="{{item.m0}}" class="data-v-71e217db"></image><view class="data-v-71e217db"><view class="data-v-71e217db">{{item.$orig.name}}</view><view class="menu-info data-v-71e217db">{{item.$orig.label}}</view></view></view></block></view></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="6b3d36ea-3" closeVisible="{{false}}" modalData="{{modalData}}" data-event-opts="{{[['^confirm',[['handleConfirm']]]]}}" bind:confirm="__e" class="data-v-71e217db" bind:__l="__l"></modal-dialog><modal-dialog data-custom-hidden="{{!(modalPhoneVisible)}}" vue-id="6b3d36ea-4" closeVisible="{{true}}" modalData="{{modalPhoneData}}" list="{{phoneList}}" data-event-opts="{{[['^confirm',[['handlePhoneConfirm']]],['^cancel',[['handlePhoneCancel']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-71e217db" bind:__l="__l"></modal-dialog></view>