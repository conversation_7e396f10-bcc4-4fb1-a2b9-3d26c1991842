
page {
	background-color: #F5F6F7 !important;
}

button.data-v-177e84bc {
  outline: none;
  padding: 0;
}
.main-box.data-v-177e84bc {
  padding: 16px;
}
.main-box .empty-box.data-v-177e84bc {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.list-box .list-item-box.data-v-177e84bc {
  margin-bottom: 17px;
  background-color: #fff;
  border-radius: 12px;
  padding: 0 16px;
  margin-bottom: 17px;
}
.list-box .list-content-item.data-v-177e84bc {
  flex: 1;
  font-size: 14px;
  line-height: 25px;
  color: #333333;
  border-top: 1px solid #e7e7e7;
  padding: 16px 0;
}
.list-box .list-content-item ._span.data-v-177e84bc {
  color: #1C6CED;
  text-decoration: underline;
}
.list-box .module-list-item .list-cell.data-v-177e84bc {
  border-top: 1px solid #e7e7e7;
}
.list-box .list-item.data-v-177e84bc {
  display: flex;
  align-items: center;
}
.list-box .list-item.data-v-177e84bc:last-child {
  border: 0;
}
.list-box .list-item image.data-v-177e84bc {
  width: 24px;
  height: 24px;
}
.list-cell-title.data-v-177e84bc {
  flex: 1;
}
.list-cell-title .time.data-v-177e84bc {
  color: #ccc !important;
}
.list-cell.data-v-177e84bc {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 400;
  color: #333;
  padding: 16px 0;
}
.list-cell .list-lable.data-v-177e84bc {
  font-weight: 600;
  display: flex;
  align-items: center;
}
.list-cell .list-lable image.data-v-177e84bc {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
.list-cell .icon-shanchu.data-v-177e84bc {
  color: #CCCCCC !important;
  margin-left: 15px;
  font-size: 18px;
}
.list-cell .list-cell-label.data-v-177e84bc {
  padding: 1px 5px;
  font-size: 14px;
  color: #FFFFFF;
  background: #D54941;
  border-radius: 16px;
  margin-left: 10px;
}
.list-cell .text-right.data-v-177e84bc {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
.arrow-icon.data-v-177e84bc {
  font-size: 20px;
  color: #999 !important;
  margin-left: 5px;
}

