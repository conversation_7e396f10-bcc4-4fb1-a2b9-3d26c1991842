<view class="tabbar" style="{{'height:'+((iPhoneX?60+JawHeight:60)+'px')+';'}}"><button data-event-opts="{{[['tap',[['handleDidSelectTabBar',[0]]]]]}}" class="tabbar-item" style="{{'color:'+(selectedIndex===0?'#0052D9':'')+';'}}" bindtap="__e"><view class="tabbar-item-icon"><block wx:if="{{selectedIndex===0}}"><image src="../../static/images/home.png"></image></block><block wx:else><image src="../../static/images/grey-home.png"></image></block></view><view class="tabbar-item-text">健康档案</view></button><button data-event-opts="{{[['tap',[['handleDidSelectTabBar',[2]]]]]}}" class="tabbar-item" style="{{'color:'+(selectedIndex===2?'#0052D9':'')+';'}}" bindtap="__e"><view class="tabbar-item-icon"><block wx:if="{{selectedIndex===2}}"><image src="../../static/images/AI.png"></image></block><block wx:else><image src="../../static/images/gray-AI.png"></image></block></view><view class="tabbar-item-text">AI报告分析</view></button><button data-event-opts="{{[['tap',[['handleDidSelectTabBar',[1]]]]]}}" class="tabbar-item" style="{{'color:'+(selectedIndex===1?'#0052D9':'')+';'}}" bindtap="__e"><view class="tabbar-item-icon"><block wx:if="{{selectedIndex===1}}"><image src="../../static/images/my.png"></image></block><block wx:else><image src="../../static/images/grey-my.png"></image></block></view><block wx:if="{{noticeNum!=0}}"><view class="point"></view></block><view class="tabbar-item-text">我的</view></button></view>