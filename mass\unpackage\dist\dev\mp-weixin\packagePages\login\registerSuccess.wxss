.login-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
}
.login-wrapper .login-form {
  width: 100%;
  padding: 0 26px;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.login-wrapper .login-form .register-info {
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
  line-height: 20px;
  margin-bottom: 25px;
}
.login-wrapper .login-form .info-title {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 19px;
}
.login-wrapper .center-box {
  padding: 0 20px;
  margin-bottom: 20px;
}
.login-wrapper .form-wrap {
  width: 100%;
}
.login-wrapper .form-wrap .form-item {
  display: flex;
  align-items: center;
  justify-content: flex-stfvafvrt;
  padding-bottom: 15px;
}
.login-wrapper .form-wrap .form-item text {
  margin-right: 10px;
  white-space: nowrap;
}
.login-wrapper input[disabled] {
  color: #999;
}
.login-wrapper .bottom-btn-box {
  width: 100%;
}
.login-wrapper .tips {
  color: #1c6ced;
  text-align: left;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  text-decoration: underline;
}
.login-wrapper .tips .reset-btn {
  width: 40px;
  padding: 0 !important;
  margin: 0;
  color: #1c6ced;
  border: 1px solid #1c6ced;
  font-size: 12px !important;
  background-color: none;
}

