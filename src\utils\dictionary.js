// 前端字典
export const getWebDict = {
  // 是否
  yesNo: [
    { code: "1", name: "是" },
    { code: "0", name: "否" }
  ],
  trueFalse: [
    { code: true, name: "是" },
    { code: false, name: "否" }
  ],
  statusOptions: [
    { code: "1", name: "启用" },
    { code: "0", name: "停用" }
  ],
  dealOpinions: [
    { code: "1", name: "同意" },
    { code: "0", name: "不同意" }
  ],
}
// 前端字典解析
export function getWebDictName(v, i) {
  var actions = [];
  getWebDict[v].forEach((j) => {
    if (j.code == i) {
      actions.push(j.name);
      return true;
    }
  })
  return actions.join('');
}

// 后端字典
/**
 * 字典名称获取字典对象
 * 例如: (cardType) => return [{code: "1", name: "身份证",...]
 * @param {string} code 字典名称
 * @returns {string} 显示名称
 */
export function getDict(type) {
  const list = JSON.parse(localStorage.getItem('YL_pc_dict')) || [];
  for (var i in list) {
    if (list[i].code == type) {
      let result = list[i].children
      result.forEach((item) => {
        if (!item.children || item.children.length == 0) {
          delete item.children
        }
      })
      return result
    }
  }
  return [];
}
/**
 * 字典名称和入参code获取name
 * 例如: (cardType, 1) => return 身份证
 * @param {string} dictName 字典码值
 * @param {string} code 待转换value
 * 仅用于显示，未匹配到原样输出
 * @returns {string} name 显示名称
 */
export function getDictName(dictName, code, split = '--') {
  if (code === null || code === undefined || code === '' || !dictName) {
    return split
  }
  let name = code
  // 字典的返回是个对象
  const list = JSON.parse(localStorage.getItem('YL_pc_dict')) || [];
  for (var i in list) {
    if (list[i].code == dictName) {
      let result = list[i].children
      var childrenMap = {};
      function cache(value) {
        for (var i = 0, item; (item = value[i++]);) {
          childrenMap[item.code] = item;
          if (item.children && item.children.length) {
            cache(item.children);
          }
        }
      }
      cache(result)
      name = childrenMap[code] ? childrenMap[code].name : name
    }
  }
  return name || '--'
}
