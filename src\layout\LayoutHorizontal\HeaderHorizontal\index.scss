.m-layout-header {
  width: 100%;
  transition: width 0.28s;
  flex-shrink: 0;
  box-sizing: border-box;
  box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
  .header-inner {
    height: 50px;
    width: 100%;
    border-bottom: 1px solid #eee;
    display: flex;
    background-color:$menuBg;
    align-items: center;
    padding: 0 10px 0 0;
    box-sizing: border-box;
    justify-content: space-between;
  }
}
.fixed-header{
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
}

.menu-horizontal{
  flex: 1;
  overflow: hidden;
  height: 100%;

  :deep(.el-menu-item){
    height: 100%;
  }
}



