{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/dialogue.vue?a119", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/dialogue.vue?dce9", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/dialogue.vue?24b0", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/dialogue.vue?b0b4", "uni-app:///packagePages/my/dialogue.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/dialogue.vue?f8a1", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/my/dialogue.vue?e43f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "dialogList", "btnList", "loading", "eventSource", "answerContent", "answerInfo", "requestTask", "searchInfo", "conversation_id", "user_info", "type", "onLoad", "content", "id", "title", "show", "onUnload", "methods", "getContentInfo", "info", "obj", "cxjmjkdabh", "destype", "yljgdm", "reportid", "jzlsh", "api", "ask", "uni", "delta", "item", "getInfo", "Authorization", "url", "method", "inputs", "query", "responseType", "response_mode", "user", "header", "enableChunked", "success", "console", "fail", "txt", "chunk", "processChunk", "processThinkContent", "thinkId", "thinkContent", "scrollToBottom", "scrollTop", "duration", "stopStream"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAuwB,CAAgB,uxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsC3xB;AACA;AAEA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cAAA;cAAA,OACA;YAAA;cACA;cAAA,MACA;gBAAA;gBAAA;cAAA;cACA;gBACA;kBACAD;kBACAE;gBACA;gBAEA;kBACAF;kBACAE;gBACA;cACA;cACA;gBACA;kBACAF;kBACAE;gBACA;gBACA;kBACAF;kBACAE;gBACA;cACA;cAAA;YAAA;cAGA;gBACA;kBACAF;kBACAE;gBACA;gBACA;kBACAC;kBACAC;kBACAC;gBACA,GACA;kBACAF;kBACAC;kBACAC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACAF;kBACAC;kBACAC;gBACA,EACA;cACA;gBACA;kBACAL;kBACAE;gBACA;kBACAF;kBACAE;gBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAI;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;QACA;QACAC;UACAC;QACA;QACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;QACApB;QACAE;MACA;QACAF;QACAE;MACA;MACA;MACA;MACA;IACA;IACAmB;MAAA;MACA;MACA;;MAEA;MACA;QACA;QACAC;MACA;QACA;QACAA;MACA;QACA;UACA;UACAA;QACA;UACA;UACAA;QACA;MACA;MAEA;MACA;QAEAC;QAAA;QACA;QACAC;QACAnC;UACA;UACAoC;UACAC;UACAC;UAAA;UACAC;UACA;UACAC;QACA;QACAC;UACA;UAAA;UACA;UACA;QACA;QACAC;QAAA;QACAC;UACAC;UACA;YACA;UACA;QACA;QACAC;UACAD;QACA;MACA;MACA;QACAA;QACA;QACA;QACA;QACA;QACA;QACA;UACAE;QACA;UACA;UACA;UACAA;QACA;QACAF;QACA;QACAG;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QAEA;QAEA;UACA;QACA;QACA;QACA,oDACA,oBACAhD,KACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;QAEA;;QAEA;QACA;UACA;UACA;YACA;cACA;gBACA+B;cACA;YACA;UACA;QACA;QACA;UACA;QACA;MACA;QACAa;MACA;IACA;IAEA;IACAK;MACA;MACA;MAEA;QACA;QACA;;QAEA;QACA,6HAEAC,2NAGAA,wGAEAA,0FACAC;MAIA;IACA;IACA;IACAC;MACAvB;QACAwB;QAAA;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpUA;AAAA;AAAA;AAAA;AAAs7C,CAAgB,04CAAG,EAAC,C;;;;;;;;;;;ACA18C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/my/dialogue.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/my/dialogue.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./dialogue.vue?vue&type=template&id=9770fc10&\"\nvar renderjs\nimport script from \"./dialogue.vue?vue&type=script&lang=js&\"\nexport * from \"./dialogue.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dialogue.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/my/dialogue.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialogue.vue?vue&type=template&id=9770fc10&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading\n    ? _vm.dialogList.length > 2 || (_vm.dialogList.length >= 2 && _vm.type == 2)\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialogue.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialogue.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-wrapper\">\r\n\t\t<zlnavbar :isBack=\"true\">\r\n\t\t\t<block slot=\"content\">AI健康助手</block>\r\n\t\t</zlnavbar>\r\n\t\t<view class=\"main-content\">\r\n\t\t\t<view class=\"dialog-list\" v-for=\"(item,index) in dialogList\" :key=\"index\">\r\n\t\t\t\t<view :class=\"item.type === 'question' ? 'dialog-info flex-end-content' : 'dialog-info'\">\r\n\t\t\t\t\t<view class=\"image\" v-if=\"item.type === 'answer'\">\r\n\t\t\t\t\t\t<image src=\"/static/images/index/AI_icon.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dialog-content\" :class=\"item.type === 'answer' ? 'answer' : 'quest'\">\r\n\t\t\t\t\t\t<text v-if=\"!item.content\" class=\"dotting\"></text>\r\n\t\t\t\t\t\t<rich-text :nodes=\"item.content\">\r\n\t\t\t\t\t\t</rich-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"image image2\" v-if=\"item.type === 'question'\">\r\n\t\t\t\t\t\t<image v-if=\"item.type === 'question'\" src=\"/static/images/index/userImg.png\" mode=\"widthFix\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main-button\" v-if=\"!loading\">\r\n\t\t\t\t<view v-for=\"(item,index) in btnList\">\r\n\t\t\t\t\t<view class=\"button-list\" v-if=\"item.show\" @click=\"ask(item)\">\r\n\t\t\t\t\t\t<text class=\"question\">{{ item.title }}</text>\r\n\t\t\t\t\t\t<image src=\"/static/images/index/arrow-right.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tips\" v-if=\"dialogList.length > 2 || (dialogList.length >= 2 && type == 2)\">\r\n\t\t\t\t\t本内容由AI生成，内容仅供参考，请仔细甄别\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\timport {\r\n\t\tmarked\r\n\t} from 'marked';\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdialogList: [],\r\n\t\t\t\tbtnList: [],\r\n\t\t\t\tloading: false,\r\n\t\t\t\teventSource: null,\r\n\t\t\t\tanswerContent: '',\r\n\t\t\t\tanswerInfo: '',\r\n\t\t\t\trequestTask: null,\r\n\t\t\t\tsearchInfo: null,\r\n\t\t\t\tconversation_id: '',\r\n\t\t\t\tuser_info: {},\r\n\t\t\t\ttype: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tasync onLoad(options) {\r\n\t\t\tthis.user_info = options\r\n\t\t\tawait this.getContentInfo(options)\r\n\t\t\tthis.type = options.type\r\n\t\t\tif(!this.searchInfo || !this.user_info.cxjmjkdabh){\r\n\t\t\t\tif(options.type == 1){\r\n\t\t\t\t\tthis.dialogList.push({\r\n\t\t\t\t\t\ttype: 'answer', \r\n\t\t\t\t\t\tcontent: '您好，欢迎使用AI健康助手服务。我是您的专属健康小管家，能够自动识别并分析您的健康档案，为您提供健康分析及个性化的健康建议。首先，让我为您展示一下我们系统中记录的您的健康档案信息，好吗？'\r\n\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.dialogList.push({\r\n\t\t\t\t\t\ttype: 'answer',\r\n\t\t\t\t\t\tcontent: '您的健康数据不完善，请稍后重试。'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif(options.type == 2){\r\n\t\t\t\t\tthis.dialogList.push({\r\n\t\t\t\t\t\ttype: 'question',\r\n\t\t\t\t\t\tcontent: '利用AI工具分析以上报告内容'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.dialogList.push({\r\n\t\t\t\t\t\ttype: 'answer',\r\n\t\t\t\t\t\tcontent: '您的健康数据不完善，请稍后重试。'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif (options.type == 1) {\r\n\t\t\t\tthis.dialogList.push({\r\n\t\t\t\t\ttype: 'answer',\r\n\t\t\t\t\tcontent: '您好，欢迎使用AI健康助手服务。我是您的专属健康小管家，能够自动识别并分析您的健康档案，为您提供健康分析及个性化的健康建议。首先，让我为您展示一下我们系统中记录的您的健康档案信息，好吗？'\r\n\t\t\t\t})\r\n\t\t\t\tthis.btnList = [{\r\n\t\t\t\t\t\tid: '1',\r\n\t\t\t\t\t\ttitle: '好的，请展示吧',\r\n\t\t\t\t\t\tshow: true,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: '2',\r\n\t\t\t\t\t\ttitle: '你能告诉我一些健康指导和就医建议吗？',\r\n\t\t\t\t\t\tshow: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \tid: '3',\r\n\t\t\t\t\t// \ttitle: '根据健康状态给出就医指导',\r\n\t\t\t\t\t// \tshow: false,\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: '4',\r\n\t\t\t\t\t\ttitle: '退出，结束此次对话',\r\n\t\t\t\t\t\tshow: true,\r\n\t\t\t\t\t},\r\n\t\t\t\t]\r\n\t\t\t} else if (options.type == 2) {\r\n\t\t\t\tthis.dialogList.push({\r\n\t\t\t\t\ttype: 'question',\r\n\t\t\t\t\tcontent: '利用AI工具分析以上报告内容'\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttype: 'answer',\r\n\t\t\t\t\tcontent: ''\r\n\t\t\t\t})\r\n\t\t\t\tthis.getInfo(4)\r\n\t\t\t}\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t    this.stopStream();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync getContentInfo(info){\r\n\t\t\t\tif(!info.cxjmjkdabh) return\r\n\t\t\t\tlet obj = {\r\n\t\t\t\t\tcxjmjkdabh: info.cxjmjkdabh,\r\n\t\t\t\t\tdestype: info.destype || 0,\r\n\t\t\t\t\tyljgdm: info.yljgdm || '',\r\n\t\t\t\t\treportid: info.reportid || '',\r\n\t\t\t\t\tjzlsh: info.jzlsh || '',\r\n\t\t\t\t} \r\n\t\t\t\tawait api.getAiInfo(obj).then(res => {\r\n\t\t\t\t\tthis.searchInfo = res.data\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\task(info) {\r\n\t\t\t\tif (info.id == 4) {\r\n\t\t\t\t\tthis.stopStream()\r\n\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.btnList.forEach((item) => {\r\n\t\t\t\t\tif (item.id === info.id) {\r\n\t\t\t\t\t\titem.show = false\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.dialogList.push({\r\n\t\t\t\t\ttype: 'question',\r\n\t\t\t\t\tcontent: info.title\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttype: 'answer',\r\n\t\t\t\t\tcontent: ''\r\n\t\t\t\t})\r\n\t\t\t\tthis.answerContent = ''\r\n\t\t\t\tthis.answerInfo = ''\r\n\t\t\t\tthis.getInfo(info.id)\r\n\t\t\t},\r\n\t\t\tgetInfo(typeId) {\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tlet Authorization = ''\r\n\t\t\t\t\r\n\t\t\t\t// 处理密钥\r\n\t\t\t\tif(typeId == 1) {\r\n\t\t\t\t\t// 健康状况报告\r\n\t\t\t\t\tAuthorization = 'Bearer app-ZbyyAyD3APFIWZUG5Of4UHN3'\r\n\t\t\t\t}else if(typeId == 2 || typeId == 3) {\r\n\t\t\t\t\t// 健康指导和就医建议\r\n\t\t\t\t\tAuthorization = 'Bearer app-tlFfEqaMybjLoG9joOez2kop'\r\n\t\t\t\t}else if(typeId == 4) {\r\n\t\t\t\t\tif (this.user_info.destype == 1 || this.user_info.destype == 2){\r\n\t\t\t\t\t\t// 检查检验报告分析\r\n\t\t\t\t\t\tAuthorization = 'Bearer app-86t6q6iEjbgGIXcSKFC3BiyQ'\r\n\t\t\t\t\t}else if (this.user_info.destype == 3){\r\n\t\t\t\t\t\t// 就诊情况分析\r\n\t\t\t\t\t\tAuthorization = 'Bearer app-7fpyQbXksFDvjDYtvmqoZx1K'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tlet query = typeId == 1 || typeId == 4 ? this.searchInfo : typeId == 2 ? this.searchInfo + '，你能告诉我一些健康指导和就医建议吗' : ''\r\n\t\t\t\tthis.requestTask = wx.request({\r\n\r\n\t\t\t\t\turl: 'https://jsws.bynrws.net/v1/chat-messages', // 线上\r\n\t\t\t\t\t// url: 'http://192.168.3.217/v1/chat-messages', // 测试\r\n\t\t\t\t\tmethod: 'POST',  \r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t// 请求参数\r\n\t\t\t\t\t\tinputs: [],\r\n\t\t\t\t\t\tquery,\r\n\t\t\t\t\t\tresponseType: \"arraybuffer\", // 关键：接收二进制流\r\n\t\t\t\t\t\tresponse_mode: 'streaming',\r\n\t\t\t\t\t\t// conversation_id: this.conversation_id,\r\n\t\t\t\t\t\tuser: this.user_info.cxjmjkdabh\r\n\t\t\t\t\t},\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Authorization': Authorization, // 线上\r\n\t\t\t\t\t\t// 'Authorization': 'Bearer app-ZhbDl0FSbBE8r98p6wrnnUwT', //测试\r\n\t\t\t\t\t\t'Content-Type': 'application/json',\r\n\t\t\t\t\t},\r\n\t\t\t\t\tenableChunked: true, // 开启分块传输\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('请求成功:。。。。。。。。。。。。。', res);\r\n\t\t\t\t\t\tif (res.statusCode !== 200) {\r\n\t\t\t\t\t\t\tthrow new Error('请求失败');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('微信小程序请求失败:', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tthis.requestTask.onChunkReceived((res) => {\r\n\t\t\t\t\tconsole.log('收到分块数据:', res);\r\n\t\t\t\t\tconst data = res.data\r\n\t\t\t\t\tlet txt;\r\n\t\t\t\t\t// 进行判断返回的对象是Uint8Array（开发者工具）或者ArrayBuffer（真机）\r\n\t\t\t\t\t// 1.获取对象的准确的类型\r\n\t\t\t\t\tconst type = Object.prototype.toString.call(data); // Uni8Array的原型对象被更改了所以使用字符串的信息进行判断。\r\n\t\t\t\t\tif(type ===\"[object Uint8Array]\"){\r\n\t\t\t\t\t\ttxt=decodeURIComponent(escape(String.fromCharCode(...data)))\r\n\t\t\t\t\t}else if(data instanceof ArrayBuffer){\r\n\t\t\t\t\t\t// 将ArrayBuffer转换为Uint8Array\r\n\t\t\t\t\t\tconst uint8Array = new Uint8Array(data);\r\n\t\t\t\t\t\ttxt=decodeURIComponent(escape(String.fromCharCode(...uint8Array)))\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(txt)\r\n\t\t\t\t\tconst chunk = txt\r\n\t\t\t\t\tchunk.split('\\n').forEach(msg => {\r\n\t\t\t\t\t\tif (msg.trim()) {\r\n\t\t\t\t\t\t\tthis.processChunk(msg.trim(), typeId);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tprocessChunk(chunkStr, typeId) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 去除可能的前缀 \"data:\"\r\n\t\t\t\t\tconst jsonStr = chunkStr && chunkStr.startsWith('data:') ? chunkStr.substring(5) : chunkStr;\r\n\r\n\t\t\t\t\tconst data = jsonStr ? JSON.parse(jsonStr) : '';\r\n\r\n\t\t\t\t\tif(data && data.conversation_id){\r\n\t\t\t\t\t\tthis.conversation_id = data.conversation_id\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 更新完整响应对象\r\n\t\t\t\t\tthis.fullResponse = {\r\n\t\t\t\t\t\t...this.fullResponse,\r\n\t\t\t\t\t\t...data\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t// 如果有answer字段，则更新显示内容\r\n\t\t\t\t\tif (data.answer !== undefined) {\r\n\t\t\t\t\t\tthis.answerContent = this.answerContent + this.fullResponse.answer;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理think部分内容，将其折叠展示\r\n\t\t\t\t\tthis.answerContent = this.processThinkContent(this.answerContent);\r\n\r\n\t\t\t\t\tthis.dialogList[this.dialogList.length - 1].content = marked(this.answerContent)\r\n\r\n\t\t\t\t\t// 可以根据其他字段做不同处理\r\n\t\t\t\t\tif (data.event === 'message_end') {\r\n\t\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\t\tif(typeId == '1'){\r\n\t\t\t\t\t\t\tthis.btnList.forEach((item) => {\r\n\t\t\t\t\t\t\t\tif (item.id == '2' || item.id == '3') {\r\n\t\t\t\t\t\t\t\t\titem.show = true\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\t\tthis.scrollToBottom();\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('解析JSON失败:', error, '原始数据:', chunkStr);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 处理think部分内容，实现折叠展示\r\n\t\t\tprocessThinkContent(content) {\r\n\t\t\t\t// 使用正则表达式匹配think标签及其内容\r\n\t\t\t\tconst thinkRegex = /<think>([\\s\\S]*?)<\\/think>/g;\r\n\r\n\t\t\t\treturn content.replace(thinkRegex, (match, thinkContent) => {\r\n\t\t\t\t\t// 生成唯一ID用于折叠控制\r\n\t\t\t\t\tconst thinkId = 'think_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\r\n\r\n\t\t\t\t\t// 返回折叠的HTML结构\r\n\t\t\t\t\treturn `\r\n\t\t\t\t\t\t<div class=\"think-container\">\r\n\t\t\t\t\t\t\t<div class=\"think-header\" onclick=\"toggleThink('${thinkId}')\">\r\n\t\t\t\t\t\t\t\t<span class=\"think-icon\">🤔</span>\r\n\t\t\t\t\t\t\t\t<span class=\"think-title\">思考过程</span>\r\n\t\t\t\t\t\t\t\t<span class=\"think-toggle\" id=\"toggle_${thinkId}\">▼</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"think-content\" id=\"${thinkId}\" style=\"display: none;\">\r\n\t\t\t\t\t\t\t\t<div class=\"think-text\">${thinkContent}</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t`;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 滚动到底部\r\n\t\t\tscrollToBottom() {\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tscrollTop: 99999999, // 一个足够大的值，确保滚动到底部，具体值取决于内容长度和设计需求。也可以使用动态计算的方式获取。例如：document.body.scrollHeight - window.innerHeight。但注意，在uni-app中，可能需要使用uni.createSelectorQuery来动态获取。\r\n\t\t\t\t\tduration: 300 // 滚动动画的时长，单位为ms。可以根据需要调整。\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 停止请求\r\n\t\t\tstopStream() {\r\n\t\t\t\tif (this.requestTask) {\r\n\t\t\t\t\tthis.requestTask.abort();\r\n\t\t\t\t\tthis.requestTask = null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.login-wrapper {\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.main-content {\r\n\t\t// height: 100%;\r\n\t\tpadding: 14px;\r\n\r\n\t\t.dialog-list {\r\n\t\t\t.dialog-info {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\tflex-shrink: 0;\r\n\t\t\t\tcolumn-gap: 10px;\r\n\t\t\t\tmargin-bottom: 16px;\r\n\r\n\t\t\t\tview.image {\r\n\t\t\t\t\twidth: 32px;\r\n\t\t\t\t\theight: 32px;\r\n\t\t\t\t\tbackground-color: #F2F2F2;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tpadding: 8px 4px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tview.image2 {\r\n\t\t\t\t\tpadding: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dialog-content {\r\n\t\t\t\t\tmax-width: calc(100% - 60px);\r\n\t\t\t\t\tbackground-color: #F7F7F7;\r\n\t\t\t\t\tpadding: 10px;\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dialog-content.quest {\r\n\t\t\t\t\tborder-radius: 10px 0 10px 10px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dialog-content.answer {\r\n\t\t\t\t\tborder-radius: 0 10px 10px 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.flex-end-content {\r\n\t\t\t\tjustify-content: flex-end;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.main-button {\r\n\t\t\tmargin-top: 30px;\r\n\t\t\tpadding: 0 10px 20px 10px;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t.button-list {\r\n\t\t\t\tmargin-top: 10px;\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tborder-radius: 20px;\r\n\t\t\t\tbackground-color: #F7F7F7;\r\n\t\t\t\tfont-size: 14px;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttext.question {\r\n\t\t\t\t\tmargin-right: 10px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 20px;\r\n\t\t\t\t\tvertical-align: bottom;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.tips{\r\n\t\t\t\tmargin-top: 20px;\r\n\t\t\t\tcolor: red;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.dotting {\r\n\t    display: inline-block;\r\n\t    min-width: 2px;\r\n\t    min-height: 2px;\r\n\t    margin-right: 22px;\r\n\t    margin-left: 10px;\r\n\t    box-shadow: 2px 0 rgb(28, 108, 237); /* 初始化显示一个点 */\r\n\t    animation: dot 1.5s infinite step-start both;\r\n\t}\r\n\t\r\n\t@keyframes dot {\r\n\t    0% { box-shadow: 2px 0 rgb(28, 108, 237); } /* 1个点 */\r\n\t    25% { box-shadow: none; } /* 0个点 */\r\n\t    50% { box-shadow: 2px 0 rgb(28, 108, 237); } /* 1个点 */\r\n\t    75% { box-shadow: 2px 0 rgb(28, 108, 237), 6px 0 rgb(28, 108, 237); } /* 2个点 */\r\n\t    100% { box-shadow: 2px 0 rgb(28, 108, 237), 6px 0 rgb(28, 108, 237), 10px 0 rgb(28, 108, 237); } /* 3个点 */\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialogue.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dialogue.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308007\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}