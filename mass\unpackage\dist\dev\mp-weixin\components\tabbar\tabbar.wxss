.tabbar {
  background: #FFFFFF;
  position: fixed;
  width: 100%;
  bottom: 0px;
  display: flex;
  justify-content: space-around;
  height: 60px;
  border-top: 1px solid #F4F4F4;
  z-index: 100;
  -webkit-transform: translateZ(1px);
  transform: translateZ(1px);
}
.tabbar .tabbar-item {
  padding: 0;
  margin: 0;
  border: 0;
  border-radius: 0;
  overflow: visible;
  position: relative;
  color: #6E6D7A;
  width: 100%;
  flex-grow: 1;
  height: 60px;
  background: #FFFFFF;
  text-align: center;
  color: #666666;
  font-size: 12px;
  font-weight: 500;
  line-height: 14px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.tabbar .tabbar-item .point {
  position: absolute;
  top: 8px;
  right: 42%;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #D54941;
}
.tabbar .tabbar-item:after {
  display: none;
}
.tabbar .tabbar-item image {
  width: 21px;
  height: 20px;
}
.tabbar .tabbar-item .tabbar-item-icon {
  margin-top: 10px;
  height: 30px;
}
.tabbar .tabbar-item .tabbar-item-icon ._i {
  margin-top: 7px;
  font-size: 28px;
}
.tabbar .tabbar-item .tabbar-item-icon .renwu {
  font-size: 25px;
}
.tabbar .tabbar-item .tabbar-item-icon .gongneng {
  display: block;
  padding-left: 5px;
}
.tabbar .tabbar-item .tabbar-item-icon image {
  width: 21px;
  height: 20px;
  opacity: 1;
  border-radius: 3px;
}
.tabbar .tabbar-item .tabbar-item-text {
  margin-bottom: 10px;
}
.tabbar .tabbar-item .tabbar-item-center {
  height: 30px;
}
.tabbar .tabbar-item .tabbar-item-center .tabbar-item-center-icon {
  position: relative;
  border: 0px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  -webkit-transform: translate(0rpx, 0rpx);
          transform: translate(0rpx, 0rpx);
  background: 0%;
}
.tabbar .tabbar-item .tabbar-item-center .tabbar-item-center-icon image {
  width: 68px;
  height: 46px;
}

