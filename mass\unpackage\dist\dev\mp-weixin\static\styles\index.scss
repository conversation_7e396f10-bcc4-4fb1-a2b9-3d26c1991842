@import './flex.css';
view{
	 // -webkit-overflow-scrolling: touch;
	 // overflow-scrolling: touch;
}


/*  */
switch.green[checked] .wx-switch-input,
switch[checked] .wx-switch-input,
checkbox.green[checked] .wx-checkbox-input,
checkbox[checked] .wx-checkbox-input,
radio.green[checked] .wx-radio-input,
radio[checked] .wx-radio-input,
switch.green.checked .uni-switch-input,
switch.checked .uni-switch-input,
checkbox.green.checked .uni-checkbox-input,
checkbox.checked .uni-checkbox-input,
radio.green.checked .uni-radio-input,
radio.checked .uni-radio-input {
	background-color: #1C6CED !important;
	border-color:#1C6CED  !important;
	color:#1C6CED  !important;
	border-color: #1C6CED !important;
}

switch::after {
	color: #4F5ADC !important;
}

switch::before {
	color: #8799a3 !important;
}

/*  */

button::after {
	border: none;
}


// image {	
// 	position: relative;
// 	&::after {
// 		font-family: "iconfont";
// 		content: "\e627";
// 		color: #dcdef8;
// 		font-size: 50px;
// 		position: absolute;
// 		top: 50%;
// 		left: 50%;
// 		transform: translate(-50%, -50%);
// 		z-index: -1;
// 		background: #f2f2fb;
// 		width: 100%;
// 		height: 100%;
// 		display: flex;
// 		align-items: center;
// 		justify-content: center;
// 		// border-radius: 30px 15px 15px;
// 	}
// }

image {
        will-change: transform;//解决加载时瞬间拉伸问题
        // width: auto;//解决加载时瞬间拉伸问题
        // height: auto;//解决加载时瞬间拉伸问题
        image-rendering:-moz-crisp-edges;
        image-rendering:-o-crisp-edges;
        image-rendering:-webkit-optimize-contrast;
        image-rendering: crisp-edges;
        -ms-interpolation-mode:nearest-neighbor;
    }
	
::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #ccc;
}
::-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #ccc;
  opacity: 1;
}
::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #ccc;
  opacity: 1;
}
::-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #ccc;
}
.main-btn {
	width: 100%;
	height: 46px;
	background: #1c6ced;
	border-radius: 6px;
	box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.10); 
	font-size: 15px;
	font-weight: 400;
	color: #fff; 
	display: flex; 
	align-items: center;
	justify-content: center;
	border: none;
	outline: none;
}

.main-btn2 {
	width: 100%;
	height: 45px;
	background: #D54941;
	border-radius: 6px;
	font-size: 16        px;
	font-weight: 400;
	color: #ffff;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 20px;
	border: none;
	outline: none;
}

/*
暂无数据
*/
.empty-box {
	padding: 170px 0;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;

	image {
		width: 200px;
		height: 108px;
		margin-bottom: 12px;
	}

}


	
/* 灰色背景的盒子 */
.wrapper {
	// position: fixed;
	width: 100%;
	height: 100%;
	background-color: #fff;
	overflow-y: scroll;
	font-size: 14px;
	font-weight: 400;
	color: #0D0B22; 
	// -webkit-overflow-scrolling: touch;
	// overflow-scrolling: touch;
}

/* scroll bar 选中状态颜色 */
.text-active {
	color: #4F5ADC !important
}

/* 统一标题样式 */
.menu-title {
	font-size: 14px;
	font-weight: 400;
	color: rgba(0,0,0,0.90);
	position: relative;
	padding-left: 8px;
	font-weight: 600;
		
	&::after{
		position: absolute;
		content: '';
		height: 80%;
		width: 3px;
		left:0;
		top: 50%;
		transform: translateY(-50%);
		background: #1c6ced;
		
	}
}


/* 统一灰色提示文字样式 */
.hint-info {
	font-size: 12px;
	font-weight: 400;
	color: #9E9EA7;
	margin-top: 12px;
	text-align: center;
}

.hint-info2 {
	color: #6E6D7A;
	margin-bottom: 5px;
}

/* 主题颜色文字 */
.main-font-color {
	font-size: 12px;
	color: #4F5ADC;
}