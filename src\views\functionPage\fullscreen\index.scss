.app-container-inner{
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .header{
    padding: 20px;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #0000001a;
    .title{
      margin-bottom: 20px;
    }
  }
  .fullscreen{
    display: flex;
    flex-direction: column;
    background: white;
    width: 300px;
    height: 300px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    .title{
      padding: 20px;
    }
    .inner{
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
