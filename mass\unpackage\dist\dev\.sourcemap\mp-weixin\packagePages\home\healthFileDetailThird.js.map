{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "uni-app:///packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "scrollMenu", "u<PERSON><PERSON><PERSON>", "data", "isOpen", "collapseHeight", "name", "tabCur", "serviceSData", "serviceDataDetail", "collapseData", "detailDate", "tabData", "serviceSDataTabList", "apiDataType", "id", "infoData", "tjlb", "computed", "userInfo", "onLoad", "tabList", "methods", "getResultInfo", "infoList", "info", "result", "item", "subItem", "goDetail", "uni", "url", "handleChangePanel", "scrollTop", "setTimeout", "duration", "selector", "handleMenuChange", "goHome"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,8BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8I;AAC9I;AACyE;AACL;AACa;AACyB;;;AAG1G;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,2FAAM;AACR,EAAE,4GAAM;AACR,EAAE,qHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAAoxB,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+MxyB;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAJ;QACAK;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IAEA;IACA;IACA;IACA;MACA;MACA;IACA;MACA;MACA;IACA;IACA;IACA;MACA;MACA;MACA,yGACAC;IACA;MACA;MACA,qGACAT;MACA,qGACAC;MACA;IACA;;IAEA;;IAEA;IACA;EACA;EACAS;IACAC;MAAA;MACA;QACA;UACA;YACA;YACA;YACAC;cACA;gBACA;gBACA;kBACAC;gBACA;kBACA;kBACA,sDACAf;oBACAe;kBACA;oBACAA;kBACA;gBACA;gBACA;cACA;cACAC;YACA;YACAC;UACA;YACA;cACAA;gBACAC;cACA;YACA;cAEAD;YACA;UACA;QACA;MAEA;IACA;IACAE;MAAA;MACA;MACA;MACA;MACA;QACAb;MACA;MACA;QACAc;UACAC;QACA;MACA;QACA;QACAD;UACAC;QACA;MACA;IACA;IAEAC;MACA;MACAL;MACA;QACA;UACAxB;QACA;MACA;MACA;IACA;IACA8B;MAAA;MACAC;QACA;UACAJ;YACAK;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA,gGACAzB;MACA;MACA;QACA;MACA;IACA;IACA0B;MACAR;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxXA;AAAA;AAAA;AAAA;AAAwmC,CAAgB,2lCAAG,EAAC,C;;;;;;;;;;;ACA5nC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA29C,CAAgB,+6CAAG,EAAC,C;;;;;;;;;;;ACA/+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./healthFileDetailThird.vue?vue&type=template&id=960aaf7e&scoped=true&\"\nvar renderjs\nimport script from \"./healthFileDetailThird.vue?vue&type=script&lang=js&\"\nexport * from \"./healthFileDetailThird.vue?vue&type=script&lang=js&\"\nimport style0 from \"./healthFileDetailThird.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./healthFileDetailThird.vue?vue&type=style&index=1&id=960aaf7e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"960aaf7e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/home/<USER>\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetailThird.vue?vue&type=template&id=960aaf7e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.collapseData ? _vm.serviceSDataTabList.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, sitem) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        sitem = _temp2.sitem\n      var _temp, _temp2\n      sitem.type == \"detail\" ? _vm.goDetail(sitem.value) : \"\"\n    }\n    _vm.e1 = function ($event, sitem) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        sitem = _temp4.sitem\n      var _temp3, _temp4\n      sitem.type == \"detail\" ? _vm.goDetail(sitem) : \"\"\n    }\n    _vm.e2 = function ($event, sitem) {\n      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        sitem = _temp6.sitem\n      var _temp5, _temp6\n      sitem.type == \"detail\" ? _vm.goDetail() : \"\"\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetailThird.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetailThird.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"\">\n\t\t<zlnavbar :isBack=\"true\">\n\t\t\t<block slot=\"content\">{{serviceSData.name}}</block>\n\t\t</zlnavbar>\n\t\t<!-- 滚动菜单 -->\n\t\t<scrollMenu :menuData='scrollMenu' @menuIndex='handleTabMenuChange'></scrollMenu>\n\n\t\t<!-- main -->\n\t\t<view class=\"main-box\">\n\t\t\t<view class=\"list-box\">\n\t\t\t\t<!-- 普通非导航 -->\n\t\t\t\t<view class=\"list-item-box\" v-if=\"serviceDataDetail.arrData\">\n\t\t\t\t\t<view class=\"list-item\" v-for=\"(item,index) in serviceDataDetail.arrData\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"list-cell\" v-if=\"item.value\">\n\t\t\t\t\t\t\t<view class=\"list-lable\">{{item.label}}</view>\n\t\t\t\t\t\t\t<view class=\"text-right \">{{item.value}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 非折叠多条数据 -->\n\t\t\t\t\t\t<view class=\"no-collapse-content-box\"\n\t\t\t\t\t\t\tv-for=\"(collapseItem,collapseIndex) in collapseData[index].data\" :key=\"collapseIndex\"\n\t\t\t\t\t\t\tv-else-if=\"item.label==''\">\n\n\t\t\t\t\t\t\t<block v-for=\"(sitem,index) in collapseItem\" :key=\"sitem.value\">\n\t\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type=='detail'&&'list-item2']\"\n\t\t\t\t\t\t\t\t\t@click=\"sitem.type=='detail'?goDetail(sitem.value):''\">\n\t\t\t\t\t\t\t\t\t<view :class=\"['list-cell', sitem.type!='detail'&&'sub-list-cell']\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">==={{sitem.label}}</view>\n\t\t\t\t\t\t\t\t\t\t<!-- AAA111 -->\n\t\t\t\t\t\t\t\t\t\t<view :class=\"['text-right',sitem.type=='detail'&&'text-right-btn']\">\n\t\t\t\t\t\t\t\t\t\t\t{{sitem.value}}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"list-item collapse-content-desc\" v-if=\"sitem.type=='desc'\">\n\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{sitem.label}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<!-- AAA222 -->\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{sitem.value}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 普通带折叠 -->\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<view class=\"collapse-item\">\n\t\t\t\t\t\t\t\t<view class='list-cell' @click.stop.prevent='handleChangePanel(item)'>\n\t\t\t\t\t\t\t\t\t<view class='list-lable'>{{item.label}}</view>\n\t\t\t\t\t\t\t\t\t<i class=\"iconfont icon-arrow-right arrow-icon\"\n\t\t\t\t\t\t\t\t\t\t:class=\"{ 'collapse-item-arrow-active': item.isOpen, 'collapse-item--animation': item.isOpen  }\"></i>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view v-show='item.isOpen'>\n\t\t\t\t\t\t\t\t\t<view class='collapse-content' :class=\"{'is--transition':item.isOpen}\"\n\t\t\t\t\t\t\t\t\t\tv-for=\"(collapseItem,collapseIndex) in collapseData[index].data\"\n\t\t\t\t\t\t\t\t\t\t:key=\"collapseIndex\">\n\n\t\t\t\t\t\t\t\t\t\t<view class=\"list-item\" v-for=\"(collapsesItem,collapsesIndex) in collapseItem\"\n\t\t\t\t\t\t\t\t\t\t\t:key=\"collapsesIndex\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell \" v-if=\"false\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{collapsesItem.label}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA333 -->\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-right \">{{collapsesItem .value}}</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\" v-if=\"collapsesItem.type!='desc'\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{collapsesItem.label}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA444 -->\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-right\">{{collapsesItem .value||''}}</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"collapse-content-desc\" v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{collapsesItem.label}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA555 -->\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{collapsesItem.value}}</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\n\t\t\t\t<!-- 根据导航菜单渲染列表与折叠内容 -->\n\t\t\t\t<view class=\"list-item-box\" v-if='collapseData'>\n\t\t\t\t\t<!-- 导航 -->\n\t\t\t\t\t<scroll-view class=\"tabs\" scroll-x=\"true\" scroll-with-animation :scroll-left=\"scrollLeft\">\n\t\t\t\t\t\t<view class=\"tabs-scroll\" v-if=\"serviceSDataTabList.length>0\">\n\t\t\t\t\t\t\t<view class=\"tabs-scroll_item\" :class=\"{'tab-active':tabCur==index}\"\n\t\t\t\t\t\t\t\tv-for=\" (item,index) in serviceSDataTabList\" :key=\"index\"\n\t\t\t\t\t\t\t\t@click=\"handleMenuChange(index,item)\">\n\t\t\t\t\t\t\t\t{{item.label}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\n\t\t\t\t\t<view class=\"list-content-item\">\n\t\t\t\t\t\t<view class=\"list-item\" v-for=\"(item,index) in collapseData\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"list-cell\" v-if=\"item.value&&item.type!='desc'\">\n\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{item.label}}</view>\r\n\t\t\t\t\t\t\t\t<!-- AAA777 -->\n\t\t\t\t\t\t\t\t<view class=\"text-right\">{{item.value}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!--collapse-content-desc -->\n\t\t\t\t\t\t\t<view class=\"\" v-else-if=\"item.type=='desc'\">\n\t\t\t\t\t\t\t\t<view class=\"list-cell\">\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{item.label}}</view>\r\n\t\t\t\t\t\t\t\t\t<!-- AAA666 -->\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"text-left\">{{item.value}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 非折叠多条数据 -->\n\t\t\t\t\t\t\t<view class=\"no-collapse-content-box\"\n\t\t\t\t\t\t\t\tv-for=\"(collapseItem,collapseIndex) in collapseData[index].data\" :key=\"collapseIndex\"\n\t\t\t\t\t\t\t\tv-else-if=\"item.label==''\">\n\t\t\t\t\t\t\t\t<block v-for=\"(sitem,index) in collapseItem\" :key=\"sitem.value\">\n\t\t\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type=='detail'&&'list-item2']\"\n\t\t\t\t\t\t\t\t\t\t@click=\"sitem.type=='detail'?goDetail(sitem):''\" v-if=\"sitem.type!='desc'\">\n\t\t\t\t\t\t\t\t\t\t<view :class=\"['list-cell', sitem.type!='detail'&&'sub-list-cell']\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{sitem.label}}</view>\n\t\t\t\t\t\t\t\t\t\t\t<!-- AAA888 -->\n\t\t\t\t\t\t\t\t\t\t\t<view :class=\"['text-right',sitem.type=='detail'&&'text-right-btn']\">\n\t\t\t\t\t\t\t\t\t\t\t\t{{sitem.value}}\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"list-item collapse-content-desc\" v-else>\n\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{sitem.label}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- AAA999 -->\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{sitem.value}}</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 普通带折叠 -->\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<view class=\"collapse-item\">\n\t\t\t\t\t\t\t\t\t<view class='list-cell' @click.stop.prevent='handleChangePanel(item)'>\n\t\t\t\t\t\t\t\t\t\t<view class='list-lable'>{{item.label}}</view>\n\t\t\t\t\t\t\t\t\t\t<i class=\"iconfont icon-arrow-right arrow-icon\"\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'collapse-item-arrow-active': item.isOpen, 'collapse-item--animation': item.isOpen  }\"></i>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view v-show='item.isOpen'>\n\t\t\t\t\t\t\t\t\t\t<view class='collapse-content' :class=\"{'is--transition':item.isOpen}\"\n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(collapseItem,collapseIndex) in collapseData[index].data\"\n\t\t\t\t\t\t\t\t\t\t\t:key=\"collapseIndex\">\n\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-item\"\n\t\t\t\t\t\t\t\t\t\t\t\tv-for=\"(collapsesItem,collapsesIndex) in collapseItem\"\n\t\t\t\t\t\t\t\t\t\t\t\t:key=\"collapsesIndex\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell \" v-if=\"false\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{collapsesItem.label}}</view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-right \">{{collapsesItem .value}}</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\" v-if=\"collapsesItem.type!='desc'\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{collapsesItem.label}}</view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-right\">{{collapsesItem .value||''}}</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"collapse-content-desc\" v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{collapsesItem.label}}</view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{collapsesItem.value}}</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 门诊信息：带详情跳转的内容-->\n\t\t\t\t<view class=\"list-item-box\" v-for=\"(item,index) in serviceDataDetail.data\" :key=\"index\" v-else>\n\t\t\t\t\t<view class=\"list-content-item\">\n\t\t\t\t\t\t<block v-for=\"(sitem,index) in item\" :key=\"sitem.value\">\n\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type=='detail'&&'list-item2']\" v-if=\"sitem.type!='desc'\">\n\t\t\t\t\t\t\t\t<view class=\"list-cell\" @click=\"sitem.type=='detail'?goDetail():''\">\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{sitem.label}}</view>\n\t\t\t\t\t\t\t\t\t<!-- 详情 -->\n\t\t\t\t\t\t\t\t\t<view :class=\"['text-right',sitem.type=='detail'&&'text-right-btn']\">\n\t\t\t\t\t\t\t\t\t\t{{sitem.value}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"list-item \" v-else>\n\t\t\t\t\t\t\t\t<view class=\"list-cell\">\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{sitem.label}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"text-left\">{{sitem.value}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<button class=\"main-btn bottom-btn\" v-if=\"false\">异常数据反馈</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport api from \"@/api/api.js\";\n\timport uCharts from '@/packagePages/components/echarts/echarts.vue';\n\timport scrollMenu from \"@/packagePages/components/scrollMenu/index.vue\";\n\timport {\n\t\tserviceJsonData\n\t} from './serviceJsonData.js'\n\texport default {\n\t\tcomponents: {\n\t\t\tscrollMenu,\n\t\t\tuCharts\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisOpen: false,\n\t\t\t\tcollapseHeight: 0,\n\t\t\t\tscrollMenu: [{\n\t\t\t\t\tname: '本人'\n\t\t\t\t}, ],\n\t\t\t\ttabCur: 0,\n\t\t\t\tserviceSData: '',\n\t\t\t\tserviceDataDetail: '',\n\t\t\t\tcollapseData: null,\n\t\t\t\tdetailDate: '',\n\t\t\t\ttabData: '',\n\t\t\t\tserviceSDataTabList: [],\n\t\t\t\tapiDataType: '',\n\t\t\t\tid: '',\n\t\t\t\tinfoData: null, // 详情数据\r\n\t\t\t\ttjlb: '',\n\t\t\t}\n\t\t},\n\n\t\tcomputed: {\n\t\t\tuserInfo() {\n\t\t\t\treturn this.$store.getters.userInfo\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// console.log(\"healthFileDetailThird2\", options)\n\t\t\tthis.infoData = options.infoData ? JSON.parse(options.infoData) : null\n\n\t\t\tthis.serviceSData = JSON.parse(options.serviceData)\n\t\t\tthis.detailDate = options.data\n\t\t\tthis.tabData = options.tabData && JSON.parse(options.tabData)\r\n\t\t\tif(options.tjlb && options.tjlb == '8'){\r\n\t\t\t\tthis.serviceDataDetail = serviceJsonData[`yytjDetailThird`]\r\n\t\t\t\tthis.tjlb = '8'\r\n\t\t\t} else {\r\n\t\t\t\tthis.serviceDataDetail = serviceJsonData[`${this.serviceSData.key}DetailThird`]\r\n\t\t\t\tthis.tjlb = ''\r\n\t\t\t}\n\t\t\t// this.serviceDataDetail = serviceJsonData[`${this.serviceSData.key}DetailThird`]\n\t\t\tif (!this.tabData) {\n\t\t\t\tthis.serviceSDataTabList = this.serviceDataDetail.tabList\n\t\t\t\tthis.apiDataType = this.serviceDataDetail.tabList && this.serviceDataDetail.tabList[0].type\n\t\t\t\tthis.collapseData = this.serviceDataDetail.tabList && this.serviceDataDetail.data[this.serviceDataDetail\n\t\t\t\t\t.tabList[this.tabCur].value]\n\t\t\t} else {\r\n\t\t\t\tthis.serviceSDataTabList = this.serviceDataDetail[this.tabData.value].tabList\n\t\t\t\tthis.apiDataType = this.serviceDataDetail[this.tabData.value].tabList && this.serviceDataDetail[this\n\t\t\t\t\t.tabData.value].tabList[0].type\n\t\t\t\tthis.collapseData = this.serviceSDataTabList && this.serviceDataDetail[this.tabData.value].data[this\n\t\t\t\t\t.serviceSDataTabList[this.tabCur].value]\n\t\t\t\t// console.log('aaaaaaaa',this.serviceSDataTabList,this.serviceDataDetail,this.tabData,this.collapseData)\n\t\t\t}\n\n\t\t\t// if (['outpatientInfo', ].indexOf(this.serviceSData.key) > -1) {\n\t\t\t\t\n\t\t\t// }\n\t\t\tthis.getResultInfo()\n\t\t},\n\t\tmethods: {\n\t\t\tgetResultInfo() {\n\t\t\t\tif ((['outpatientInfo', 'hospitalInfor'].indexOf(this.serviceSData.key) > -1) || (this.tjlb = '8')) {\n\t\t\t\t\tthis.collapseData.forEach((item, index) => {\n\t\t\t\t\t\tif (item.type == 'arr') {\n\t\t\t\t\t\t\tlet infoList = this.infoData && this.infoData[0][item.key] || this.infoData || []\n\t\t\t\t\t\t\tlet result = []\n\t\t\t\t\t\t\tinfoList.forEach((subItem) => {\n\t\t\t\t\t\t\t\tlet arr = item.data[0].map((sitem) => {\n\t\t\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(sitem))\n\t\t\t\t\t\t\t\t\tif (info.type != 'detail') {\n\t\t\t\t\t\t\t\t\t\tinfo.value = subItem[info.key] || '无'\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t// 非处方明细\n\t\t\t\t\t\t\t\t\t\tif (this.collapseData[0].key != 'hisMzCfmx' && this\n\t\t\t\t\t\t\t\t\t\t\t.collapseData[0].key != 'hisMzSysjcmxjl') {\n\t\t\t\t\t\t\t\t\t\t\tinfo.label = item[info.key] ||  '无'\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tinfo.list = subItem.cfDetailList\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn info\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\tresult.push(arr)\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\titem.data = result\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (item.data && item.data[0]) {\n\t\t\t\t\t\t\t\titem.data[0].forEach((subItem) => {\n\t\t\t\t\t\t\t\t\tsubItem.value = this.infoData[0][subItem.key] || ' '\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t\titem.value = this.infoData[0][item.key] || ' '\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoDetail(item, type = '', ) {\n\t\t\t\tvar data = item.value\n\t\t\t\tvar serviceData = JSON.stringify(this.serviceSData)\n\t\t\t\tlet infoData = null\n\t\t\t\tif (this.serviceSData.key == 'outpatientInfo') {\n\t\t\t\t\tinfoData = JSON.stringify(item.list)\n\t\t\t\t}\n\t\t\t\tif (type == '') {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/packagePages/home/<USER>\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tvar tabData = JSON.stringify(this.serviceDataDetail.tabList[this.tabCur])\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/packagePages/home/<USER>\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\thandleChangePanel(item) {\n\t\t\t\tthis.isOpen = !this.isOpen\n\t\t\t\titem.isOpen = !item.isOpen\n\t\t\t\tthis.collapseData.map(data => {\n\t\t\t\t\tif (data.label != item.label) {\n\t\t\t\t\t\tdata.isOpen = false\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\tthis.scrollTop()\n\t\t\t},\n\t\t\tscrollTop() {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.$nextTick(function() {\n\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\tduration: 0,\n\t\t\t\t\t\t\tselector: \".tabs\" //滚动到的元素的id\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t}, 50)\n\t\t\t},\n\t\t\thandleMenuChange(index, item) {\n\t\t\t\tthis.tabCur = index\n\t\t\t\tthis.apiDataType = item.type && item.type || ''\n\t\t\t\tif (!this.tabData) {\n\t\t\t\t\tthis.collapseData = this.serviceDataDetail.data[this.serviceDataDetail.tabList[index].value]\n\t\t\t\t} else {\n\t\t\t\t\tthis.collapseData = this.serviceDataDetail[this.tabData.value].data[this.serviceDataDetail[this\n\t\t\t\t\t\t.tabData.value].tabList[index].value]\n\t\t\t\t}\n\t\t\t\tif (['outpatientInfo'].indexOf(this.serviceSData.key) > -1) {\n\t\t\t\t\tthis.getResultInfo()\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoHome() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t})\n\t\t\t},\n\t\t}\n\t};\n</script>\n<style>\n\tpage {\n\t\tbackground-color: #F5F6F7 !important;\n\t}\n</style>\n<style lang=\"scss\" scoped>\n\t.main-box {\n\t\tpadding: 16px;\n\t\tpadding-bottom: 70px;\n\t}\n\n\t.tabs {\n\t\tborder-bottom: 1px solid #e7e7e7;\n\t}\n\n\t.tabs-scroll {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: nowrap;\n\t\tbox-sizing: border-box;\n\n\n\t\t.tabs-scroll_item {\n\t\t\theight: 48px;\n\t\t\tline-height: 48px;\n\t\t\t// margin-right: -10px;\n\t\t\tflex-shrink: 0;\n\t\t\tpadding-bottom: 10px;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tfont-size: 14px;\n\t\t\tpadding: 0 16px;\n\t\t\tcolor: rgba(0, 0, 0, 0.90);\n\t\t}\n\n\t\t.tab-active {\n\t\t\tposition: relative;\n\t\t\tcolor: #0052D9 !important;\n\n\t\t\t&::after {\n\t\t\t\tcontent: \"\";\n\t\t\t\tposition: absolute;\n\t\t\t\twidth: 16px;\n\t\t\t\theight: 3px;\n\t\t\t\tbackground: #0052d9;\n\t\t\t\tborder-radius: 999px;\n\t\t\t\tleft: 50%;\n\t\t\t\tbottom: 0px;\n\t\t\t\ttransform: translateX(-50%);\n\t\t\t}\n\t\t}\n\t}\n\n\n\t.list-box {\n\t\t.list-item-box2 {\n\t\t\tbackground-color: #EF7029 !important;\n\n\t\t\t.list-cell {\n\n\t\t\t\t.list-lable {\n\t\t\t\t\tfont-size: 14px !important;\n\t\t\t\t\tcolor: #fff !important;\n\t\t\t\t\tfont-weight: 4 00 !important;\n\t\t\t\t}\n\n\t\t\t\t.text-right {\n\t\t\t\t\tfont-size: 17px !important;\n\t\t\t\t\tfont-weight: 600 !important;\n\t\t\t\t\tcolor: #fff !important;\n\t\t\t\t\topacity: 1 !important;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.list-item-box {\n\t\t\tmargin-bottom: 17px;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-radius: 12px;\n\t\t\tmargin-bottom: 17px;\n\n\t\t}\n\n\t\t.list-content-item {\n\n\t\t\tpadding: 0 16px;\n\n\t\t}\n\n\t\t.list-item2 {\n\n\t\t\t.list-cell {\n\t\t\t\t.list-lable {\n\t\t\t\t\tcolor: #1C6CED !important;\n\t\t\t\t}\n\n\t\t\t\t.text-right {\n\t\t\t\t\tcolor: #333 !important;\n\t\t\t\t}\n\n\t\t\t\t.text-right-btn {\n\t\t\t\t\tcolor: #1C6CED !important;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.list-item {\n\t\t\t// display: flex;\n\t\t\t// align-items: center;\n\t\t\tpadding: 16px 0;\n\t\t\tborder-bottom: 1px solid #e7e7e7;\n\n\t\t\t&:last-child {\n\t\t\t\tborder: 0;\n\t\t\t}\n\n\t\t\timage {\n\t\t\t\twidth: 24px;\n\t\t\t\theight: 24px;\n\t\t\t}\n\t\t}\n\n\t\t.sub-list-cell {\n\t\t\tfont-size: 12px !important;\n\n\n\t\t\t.list-lable {\n\t\t\t\t// font-size: 12px !important;\n\t\t\t\tfont-weight: 400 !important;\n\t\t\t\tcolor: #666666 !important;\n\t\t\t}\n\n\n\n\t\t\t.text-right-btn {\n\t\t\t\tcolor: #1C6CED !important;\n\t\t\t}\n\n\t\t\t.text-left {\n\t\t\t\tcolor: #1C6CED !important;\n\t\t\t\tword-break: break-all;\n\t\t\t}\n\t\t}\n\n\t\t.collapse-content-desc {\n\t\t\t.text-right-btn {\n\t\t\t\tcolor: #1C6CED !important;\n\t\t\t}\n\n\t\t\t.text-right,\n\t\t\t.text-left {\n\t\t\t\tfont-size: 12px!important;\n\t\t\t\tcolor: #333 !important;\n\t\t\t\tword-break: break-all;\n\t\t\t}\n\t\t}\n\n\t\t.list-cell {\n\t\t\tflex: 1;\n\t\t\tdisplay: flex;\n\t\t\talign-items: flex-start;\n\t\t\tjustify-content: space-between;\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #333;\n\n\t\t\t.list-label-big {\n\t\t\t\tfont-size: 17px;\n\t\t\t}\n\n\t\t\t.text-right {\n\t\t\t\tcolor: #333;\n\t\t\t\topacity: 0.9;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: flex-end;\n\t\t\t\tmargin-left: 16px;\n\t\t\t\tflex: 1;\n\n\t\t\t\t.block-label {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tmargin-left: 46px;\n\t\t\t\t\tcolor: rgba(0, 0, 0, 0.60) !important;\n\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\twidth: 12px;\n\t\t\t\t\t\theight: 2px;\n\t\t\t\t\t\tbackground-color: #00919e;\n\t\t\t\t\t\tleft: -20px;\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\tborder-radius: 5px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:last-child::after {\n\t\t\t\t\t\tbackground-color: #FFB546 !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tbutton {\n\t\t\t\t\tfont-size: 14px !important;\n\t\t\t\t\theight: 28px;\n\t\t\t\t\tline-height: 28px;\n\t\t\t\t\tborder-radius: 6px;\n\t\t\t\t\tbackground-color: #F2F3FF;\n\t\t\t\t\tcolor: #1C6CED;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t.list-lable {\n\t\t\tfont-weight: 600;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\timage {\n\t\t\t\twidth: 24px;\n\t\t\t\theight: 24px;\n\t\t\t\tmargin-right: 8px;\n\t\t\t}\n\t\t}\n\n\t\t.text-left {\n\t\t\tfont-size: 14px;\n\t\t\ttext-align: left;\n\t\t\tcolor: #333;\n\t\t\tmargin-top: 5px;\n\t\t\tword-break: break-all;\n\t\t}\n\t}\n\n\t.arrow-icon {\n\t\tfont-size: 20px;\n\t\tcolor: #999 !important;\n\t\tmargin-left: 5px;\n\t}\n\n\t// 图表\n\t.charts-box {\n\t\twidth: 100%;\n\t\theight: 210px;\n\t}\n\n\t.charts-label-box {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 17px;\n\n\t\t.charts-label-item {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-left: 46px;\n\t\t\t}\n\n\t\t\t.charts-label {\n\t\t\t\tfont-size: 12px;\n\t\t\t\tcolor: #cccccc;\n\n\t\t\t\tspan {\n\t\t\t\t\tfont-size: 45px;\n\t\t\t\t\tfont-weight: 700;\n\t\t\t\t\tcolor: #00919e;\n\t\t\t\t\tmargin-right: 5px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\n\t// 折叠菜单\n\t.collapse-item {\n\t\twidth: 100%;\n\n\t\t.collapse-item-arrow-active {\n\t\t\ttransform: rotate(90deg);\n\t\t}\n\n\t\t.collapse-item--animation {\n\t\t\ttransition-property: transform;\n\t\t\ttransition-duration: 0.3s;\n\t\t\ttransition-timing-function: ease;\n\t\t}\n\n\t\t.collapse-content {\n\t\t\tmargin-top: 17px;\n\t\t\tfont-size: 17px;\n\t\t\tpadding: 0 10px;\n\t\t\tcolor: #666;\n\t\t\tbackground-color: #f5f5f5;\n\t\t\tborder-radius: 6px;\n\n\t\t\t&.is--transition {\n\t\t\t\ttransition-property: width, height, background-color, border-width;\n\t\t\t\ttransition-duration: 0.3s;\n\t\t\t\ttransition-timing-function: ease-in;\n\t\t\t\ttransition-delay: 500ms;\n\t\t\t}\n\t\t}\n\n\t\timage {\n\t\t\tmargin-left: 17px;\n\t\t\tmargin-top: 5px;\n\t\t}\n\t}\n\n\n\n\t.no-collapse-content-box {\n\t\tmargin-bottom: 16px;\n\n\t\t.list-item2 {\n\t\t\tbackground-color: #fff !important;\n\t\t\tborder-bottom: 0px !important;\n\t\t\tpadding: 16px 0 !important;\n\n\t\t\t&::after {\n\t\t\t\theight: 0px !important;\n\t\t\t}\n\t\t}\n\n\t\t&:first-child {\n\t\t\t.list-item2 {\n\t\t\t\tpadding-top: 0px !important;\n\t\t\t}\n\t\t}\n\n\t\t.list-item {\n\t\t\t// border-bottom: 0.5px solid #dddddd;\n\t\t\tbackground: #f5f5f5;\n\t\t\tpadding: 16px 10px;\n\t\t\tposition: relative;\n\t\t\tborder-bottom: 0;\n\n\t\t\t&::after {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 10px;\n\t\t\t\tbottom: 0;\n\t\t\t\twidth: 94%;\n\t\t\t\theight: 1px;\n\t\t\t\tbackground-color: #dddd;\n\t\t\t}\n\n\t\t\t&:nth-child(2) {\n\t\t\t\tborder-radius: 6px 6px 0 0;\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tborder-radius: 0 0 6px 6px;\n\n\t\t\t\t&::after {\n\t\t\t\t\theight: 0px !important;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\n\t}\n\n\t.bottom-btn {\n\t\twidth: 91%;\n\t\tposition: fixed;\n\t\tleft: 50%;\n\t\tbottom: 20px;\n\t\ttransform: translateX(-50%);\n\t}\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetailThird.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetailThird.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308000\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetailThird.vue?vue&type=style&index=1&id=960aaf7e&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetailThird.vue?vue&type=style&index=1&id=960aaf7e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308032\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}