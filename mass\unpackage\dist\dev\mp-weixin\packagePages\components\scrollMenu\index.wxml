<scroll-view class="tabs" scroll-x="true" scroll-with-animation="{{true}}" scroll-left="{{scrollLeft}}"><view class="tabs-scroll"><block wx:for="{{menuData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleMenuChange',['$0',index],[[['menuData','',index]]]]]]]}}" class="{{['tabs-scroll_item',(currentIndex==index)?'active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></view></scroll-view>