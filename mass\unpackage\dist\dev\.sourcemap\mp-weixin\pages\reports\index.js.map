{"version": 3, "sources": ["webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/reports/index.vue?2e6a", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/reports/index.vue?4709", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/reports/index.vue?2a9a", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/reports/index.vue?d479", "uni-app:///pages/reports/index.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/reports/index.vue?8a9b", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/reports/index.vue?bfff"], "names": ["components", "scrollMenu", "modalDialog", "dataAnalysis", "data", "menuIndex", "modalVisible", "modalData", "title", "info", "btnTxt", "type", "activeIndex", "userInfo", "familyList", "familyItem", "moduleCode", "modalPhoneVisible", "modalPhoneData", "phoneList", "phone", "cxjmjkdabh", "methods", "refreshInfo", "getUserInfo", "item", "handleCancel", "uni", "handleConfirm", "url", "checkVerify", "getWxPhone", "api", "handlePhoneCancel", "handlePhoneConfirm", "checkWxLogin", "setAuthorization", "setInfoLogin", "setPhone", "handleTabMenuChange", "mask", "sfzjhm", "idCard", "console", "handleMenuChange", "goPage"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClBA;AAAA;AAAA;AAAA;AAAowB,CAAgB,oxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwCxxB;AAIA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAA;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAX;MACAY;MACAC;MACAC;MACAC;MACAC;MACAC;QACAP;QACAH;QACAC;QACAC;MACA;MACAS;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAV;MACAA;QACA;UACAW;QACA;QACA;UACAA;UACAA,yEACA,8BACA;QACA;MACA;MAEA;MACA;MACA;IAEA;IACAC;MACAC;MACA;IACA;IACAC;MACA;MACAD;QACAE;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;QACA;UACA;QACA;UACA;UACAH;YACAE,qFACAd;UACA;QACA;MACA;IACA;IACAgB;MAAA;MACAC;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QAAA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QAAA;QACA;QACA;UACAR;UACAS;UACAC;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAN;QACAZ;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEAmB;MAAA;QAAA;MACAZ;QACAnB;QACAgC;MACA;MACA;QACAC,mMACAC;MACA;MACAV;QACAW;QACAhB;QACA;MACA;QACAA;MACA;IACA;IACAiB;MAAA;MACA;MACA;MACAjB;QACAE;MACA;IACA;IACAgB;MACA;QACAlB;UACAE;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1NA;AAAA;AAAA;AAAA;AAA28C,CAAgB,+5CAAG,EAAC,C;;;;;;;;;;;ACA/9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/reports/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=50f93fd9&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=50f93fd9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"50f93fd9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/reports/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=50f93fd9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      _vm.menuIndex = index\n      _vm.handleTabMenuChange()\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"wrapper\">\r\n\t\t<zlnavbar :isBack=\"false\">\r\n\t\t\t<block slot=\"content\">AI报告分析</block>\r\n\t\t</zlnavbar>\r\n\t\t<!-- 滚动菜单 -->\r\n\t\t<!-- <scrollMenu :menuData='familyList' :menuIndex=\"menuIndex\" @menuChange='handleTabMenuChange'\r\n\t\t\tv-if=\"familyList[0].name\">\r\n\t\t</scrollMenu> -->\r\n\t\t<!-- main -->\r\n\t\t<view class=\"main-box\">\r\n\t\t\t<view class=\"familyList\">\r\n\t\t\t\t<text v-for=\"(item,index) in familyList\" @click=\"menuIndex = index;handleTabMenuChange()\" :class=\"menuIndex === index ? 'changedMenu' : ''\">{{ item.name }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main-content\" v-if=\"activeIndex === 0\">\r\n\t\t\t\t<image src=\"/static/images/index/AI_icon.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"main-title\">\r\n\t\t\t\t\t您好，欢迎使用AI健康助手服务。\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"main-tips\">\r\n\t\t\t\t\t我是您的专属健康小管家，可以为您提供健康分析以及个性化的健康建议，请把任务交给我吧！\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"main-button\">\r\n\t\t\t\t\t<view @click=\"goPage(1)\">健康指标分析</view>\r\n\t\t\t\t\t<view class=\"bg1\"></view>\r\n\t\t\t\t\t<view class=\"bg2\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<data-analysis v-if=\"activeIndex === 2\" />\r\n\t\t</view>\r\n\t\t<modalDialog :closeVisible='false' :modalData=\"modalData\" @confirm=\"handleConfirm\" v-show=\"modalVisible\">\r\n\t\t</modalDialog>\r\n\t\t<!-- 选择手机号 -->\r\n\t\t<modalDialog :closeVisible='true' :modalData=\"modalPhoneData\" :list='phoneList' @confirm=\"handlePhoneConfirm\"\r\n\t\t\t@cancel='handlePhoneCancel' v-show=\"modalPhoneVisible\">\r\n\t\t</modalDialog>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\timport scrollMenu from \"@/components/scrollMenu/index.vue\";\r\n\timport modalDialog from \"@/components/dialog/dialog.vue\";\r\n\timport dataAnalysis from './dataAnalysis.vue'\r\n\timport {\r\n\t\tparseTime\r\n\t} from \"@/utils/util\";\r\n\timport {\r\n\t\tloginFn,\r\n\t\tgetPhoneNumberFn\r\n\t} from '@/api/login.js'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tscrollMenu,\r\n\t\t\tmodalDialog,\r\n\t\t\tdataAnalysis\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuIndex: 0,\r\n\t\t\t\tmodalVisible: false,\r\n\t\t\t\tmodalData: {\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tinfo: '为保障您的个人隐私及数据安全，确保所有操作为本人操作，请先进行人脸核身，验证通过后方可查看健康档案信息。',\r\n\t\t\t\t\tbtnTxt: '人脸识别认证'\r\n\t\t\t\t},\r\n\t\t\t\ttype: 'grey',\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\tscrollMenu: [],\r\n\t\t\t\tuserInfo: null,\r\n\t\t\t\tfamilyList: [],\r\n\t\t\t\tfamilyItem: null,\r\n\t\t\t\tmoduleCode: '',\r\n\t\t\t\tmodalPhoneVisible: false,\r\n\t\t\t\tmodalPhoneData: {\r\n\t\t\t\t\ttype: 'list',\r\n\t\t\t\t\ttitle: '选择常用手机号',\r\n\t\t\t\t\tinfo: '',\r\n\t\t\t\t\tbtnTxt: '提交',\r\n\t\t\t\t},\r\n\t\t\t\tphoneList: ['18710646315', '345'],\r\n\t\t\t\tphone: '',\r\n\t\t\t\tcxjmjkdabh: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\trefreshInfo(){\r\n\t\t\t\tthis.activeIndex = 0\r\n\t\t\t},\r\n\t\t\tgetUserInfo(callback) {\r\n\t\t\t\tlet data = this.$store.state.user.userInfo\r\n\t\t\t\tvar familyList = JSON.parse(JSON.stringify(data.familyList)) || []\r\n\t\t\t\tfamilyList.unshift(data)\r\n\t\t\t\tfamilyList.map(item => {\r\n\t\t\t\t\tif (item.nameDecrypt) {\r\n\t\t\t\t\t\titem.name = '本人'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!item.type && !item.nameDecrypt && data.isRealAttestation) {\r\n\t\t\t\t\t\titem.fullName = JSON.parse(JSON.stringify(item.name))\r\n\t\t\t\t\t\titem.name = item.name.replace(item.name.substring(item.name.length > 2 ?\r\n\t\t\t\t\t\t\t\t1 : 2, item.name.length - 1),\r\n\t\t\t\t\t\t\t\"*\")\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\tthis.familyList = familyList\r\n\t\t\t\tthis.familyItem = this.familyList[this.menuIndex]\r\n\t\t\t\tthis.handleTabMenuChange()\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\thandleCancel() {\r\n\t\t\t\tuni.setStorageSync('isVerify', false)\r\n\t\t\t\tthis.modalVisible = false\r\n\t\t\t},\r\n\t\t\thandleConfirm() {\r\n\t\t\t\tthis.modalVisible = false;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/packagePages/login/registerSuccess?type=1'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 判断是否实名\r\n\t\t\tcheckVerify() {\r\n\t\t\t\t// 未认证\r\n\t\t\t\tif (!uni.getStorageSync('isVerify')) {\r\n\t\t\t\t\tthis.modalVisible = true\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 已认证--不需要重新实名认证，需要调用选择手机号\r\n\t\t\t\t\tif (!this.userInfo.resumeRecognize) {\r\n\t\t\t\t\t\tthis.getWxPhone()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 已认证--需要重新实名认证，更换手机号的情况，自动填充\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/packagePages/login/registerSuccess?type=1&&familyItem=' + JSON.stringify(this\r\n\t\t\t\t\t\t\t\t.familyItem)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetWxPhone() {\r\n\t\t\t\tapi.fetchCurPhone({}).then(res => {\r\n\t\t\t\t\tif (res.data.length > 1) {\r\n\t\t\t\t\t\tthis.phoneList = res.data\r\n\t\t\t\t\t\tthis.modalPhoneVisible = true\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t\t\t// \turl: '/pages/index/index',\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandlePhoneCancel() {\r\n\t\t\t\tthis.modalPhoneVisible = false\r\n\t\t\t},\r\n\t\t\thandlePhoneConfirm(val) {\r\n\t\t\t\tthis.phone = val\r\n\t\t\t\tthis.modalPhoneVisible = false\r\n\t\t\t\tthis.setPhone()\r\n\t\t\t},\r\n\t\t\t// 首次状态判断\r\n\t\t\tcheckWxLogin() {\r\n\t\t\t\tloginFn(1).then(res => { // 微信登录&服务端获取openid\r\n\t\t\t\t\t// console.log(res.data,res.data.userExist, '接口换取的openid')\r\n\t\t\t\t\tif (res.data.userExist) {\r\n\t\t\t\t\t\tuni.setStorageSync('resident_id', res.data.loginUser.id)\r\n\t\t\t\t\t\tsetAuthorization(res.data.access_token)\r\n\t\t\t\t\t\tsetInfoLogin(true)\r\n\t\t\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t\t\t// \turl: `/pages/index/index`\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetPhone() {\r\n\t\t\t\tapi.chooePhone({\r\n\t\t\t\t\tphone: this.phone\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.modalPhoneVisible = false\r\n\t\t\t\t\tthis.checkWxLogin() // 由于账号合并产品需求，和后端沟通这里必须加，后续这里慎重改动\r\n\t\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t\t// \turl: '/pages/index/index',\r\n\t\t\t\t\t// })\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\thandleTabMenuChange() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '请稍后..',\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t})\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tsfzjhm: this.familyList[this.menuIndex]?.idCardDecrypt || this.familyList[this.menuIndex]\r\n\t\t\t\t\t\t.idCard,\r\n\t\t\t\t}\r\n\t\t\t\tapi.fetchArchiveId(params).then((res) => {\r\n\t\t\t\t\tconsole.log('111111',res.data)\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.cxjmjkdabh = res.data\r\n\t\t\t\t}).catch(()=>{\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandleMenuChange(item, moduleCode = '') {\r\n\t\t\t\tif (item.type == 'grey') return\r\n\t\t\t\tvar data = JSON.stringify(item)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/home/<USER>\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoPage(index){\r\n\t\t\t\tif(index === 1){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/packagePages/my/dialogue?cxjmjkdabh=${this.cxjmjkdabh}&type=1`\r\n\t\t\t\t\t})\r\n\t\t\t\t}else if(index === 2){\r\n\t\t\t\t\tthis.activeIndex = 2\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.main-box {\r\n\t\tposition: relative;\r\n\t\ttext-align: center;\r\n\t\tpadding: 20px 26px;\r\n\t\theight: calc(100% - 200px);\r\n\t\toverflow: auto;\r\n\t\t.main-content{\r\n\t\t\tmargin-top: 100px;\r\n\t\t\timage{\r\n\t\t\t\twidth: 60px;\r\n\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t}\r\n\t\t\t.main-title{\r\n\t\t\t\tcolor: black;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t\t.main-tips{\r\n\t\t\t\twidth: 80%;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tmargin-top: 12px;\r\n\t\t\t\tmargin-bottom: 76px;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: #000000a6;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t\t.main-button{\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tview{\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\twidth: 137px;\r\n\t\t\t\t\theight: 40px;\r\n\t\t\t\t\tpadding: 10px;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tgap: 10px;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\tborder-radius: 20px;\r\n\t\t\t\t\tborder: 1px solid #FFF;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, #338AEB 0%, #2DE2FB 100%);\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tz-index: 1000;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t}\r\n\t\t\t\tview.bg1{\r\n\t\t\t\t\twidth: 173px;\r\n\t\t\t\t\theight: 173px;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tfill: #3296ed1a;\r\n\t\t\t\t\tfilter: blur(22.1px);\r\n\t\t\t\t\ttop: -60px;\r\n\t\t\t\t\tleft: 40%;\r\n\t\t\t\t\topacity: 0.1;\r\n\t\t\t\t\tz-index: 100;\r\n\t\t\t\t}\r\n\t\t\t\tview.bg2{\r\n\t\t\t\t\twidth: 134px;\r\n\t\t\t\t\theight: 135px;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\tfill: #2de0fb1a;\r\n\t\t\t\t\tfilter: blur(22.1px);\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 70%;\r\n\t\t\t\t\tbottom: -140px;\r\n\t\t\t\t\topacity: 0.1;\r\n\t\t\t\t\tz-index: 100;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// & > view{\r\n\t\t\t\t// \tpadding: 16px;\r\n\t\t\t\t// \tbackground-color: #f0f0f0;\r\n\t\t\t\t// \tborder-radius: 4px;\r\n\t\t\t\t// \tcolor: #888;\r\n\t\t\t\t// \tview{\r\n\t\t\t\t// \t\tline-height: 22px;\r\n\t\t\t\t// \t}\r\n\t\t\t\t// \t.main-btn-title{\r\n\t\t\t\t// \t\tfont-size: 16px;\r\n\t\t\t\t// \t\tcolor: #333;\r\n\t\t\t\t// \t\tmargin-bottom: 10px;\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.familyList{\r\n\t\tposition: absolute;\r\n\t\ttop: 10px;\r\n\t\tleft: 10px;\r\n\t\ttext{\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tbackground-color: #F7F7F7;\r\n\t\t\tcolor: #5F5F5F;\r\n\t\t\tpadding: 4px 24px;\r\n\t\t\tborder-radius: 20px;\r\n\t\t}\r\n\t\t.changedMenu{\r\n\t\t\tbackground-color: #E0EAFF;\r\n\t\t\tcolor: #376BE6;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=50f93fd9&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=50f93fd9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542313719\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}