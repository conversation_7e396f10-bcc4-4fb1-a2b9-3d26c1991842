<template>
  <div class="datacenter-container">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6" v-for="stat in statistics" :key="stat.title">
        <el-card class="stat-card">
          <el-row align="middle">
            <el-col :span="16">
              <h3 class="stat-title">{{ stat.title }}</h3>
              <div class="stat-value">{{ stat.value }}</div>
            </el-col>
            <el-col :span="8" class="stat-icon">
              <el-icon :size="32"><component :is="stat.icon" /></el-icon>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格区域 -->
    <el-card class="data-table-card">
      <template #header>
        <div class="table-header">
          <div class="header-left">
            <span class="title">数据资产目录</span>
            <el-tag class="tag" type="success">已连接</el-tag>
          </div>
          <div class="header-right">
            <el-input
              v-model="searchQuery"
              placeholder="搜索数据表"
              class="search-input"
              :prefix-icon="Search"
            />
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="filteredTableData" style="width: 100%">
        <el-table-column prop="name" label="数据表名称" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="row.type === '主表' ? 'primary' : 'info'">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column prop="size" label="数据量" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === '正常' ? 'success' : 'danger'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button-group>
              <el-button type="primary" size="small" @click="viewData(row)">
                查看
              </el-button>
              <el-button type="success" size="small" @click="analyzeData(row)">
                分析
              </el-button>
              <el-button type="warning" size="small" @click="exportData(row)">
                导出
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Connection,
  DataLine,
  Document,
  Search,
  Refresh
} from '@element-plus/icons-vue'

// 顶部统计数据
const statistics = ref([
  {
    title: '数据源',
    value: '12个',
    icon: 'Connection'
  },
  {
    title: '数据表',
    value: '186个',
    icon: 'Document'
  },
  {
    title: '数据总量',
    value: '2.3TB',
    icon: 'DataLine'
  },
  {
    title: '今日同步',
    value: '1.2GB',
    icon: 'Refresh'
  }
])

// 表格数据
const tableData = ref([
  {
    name: 'user_info',
    type: '主表',
    updateTime: '2024-03-18 10:30:00',
    size: '1.2GB',
    status: '正常'
  },
  {
    name: 'order_details',
    type: '主表',
    updateTime: '2024-03-18 10:25:00',
    size: '2.5GB',
    status: '正常'
  },
  {
    name: 'product_catalog',
    type: '从表',
    updateTime: '2024-03-18 09:15:00',
    size: '500MB',
    status: '正常'
  },
  {
    name: 'transaction_log',
    type: '从表',
    updateTime: '2024-03-18 08:45:00',
    size: '800MB',
    status: '异常'
  }
])

// 搜索和分页
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(186)

// 过滤后的表格数据
const filteredTableData = computed(() => {
  return tableData.value.filter(item =>
    item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 操作方法
const refreshData = () => {
  ElMessage.success('数据已刷新')
}

const viewData = (row: any) => {
  ElMessage.info(`查看数据表：${row.name}`)
}

const analyzeData = (row: any) => {
  ElMessage.info(`分析数据表：${row.name}`)
}

const exportData = (row: any) => {
  ElMessage.success(`开始导出数据表：${row.name}`)
}
</script>

<style scoped>
.datacenter-container {
  padding: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-title {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.stat-value {
  margin-top: 8px;
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.stat-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--el-color-primary);
}

.data-table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left .title {
  font-size: 16px;
  font-weight: bold;
}

.header-right {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 200px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>