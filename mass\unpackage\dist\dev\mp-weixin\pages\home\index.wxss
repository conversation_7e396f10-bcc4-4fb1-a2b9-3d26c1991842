.main-box.data-v-71e217db {
  padding: 16px;
  padding-bottom: 100px;
  padding-top: 26px;
}
.main-box .menu-box.data-v-71e217db {
  display: grid;
  grid-template-columns: repeat(2, 50%);
  margin-top: 26px;
}
.main-box .menu-item.data-v-71e217db {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
  position: relative;
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
  margin-bottom: 16px;
  padding-left: 16px;
}
.main-box .menu-item.data-v-71e217db:nth-child(even):last-child {
  border: none;
}
.main-box .menu-item.data-v-71e217db:nth-last-child(2):nth-child(odd) {
  border: none;
}
.main-box .menu-item.data-v-71e217db:nth-child(odd):last-child {
  border-bottom: none;
}
.main-box .menu-item image.data-v-71e217db {
  width: 48px;
  height: 48px;
  margin-right: 12px;
}
.main-box .menu-item .menu-info.data-v-71e217db {
  font-size: 10px;
  color: rgba(0, 0, 0, 0.4);
  margin-top: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  line-break: anywhere;
}

