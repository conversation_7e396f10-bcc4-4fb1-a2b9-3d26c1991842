{"version": 3, "sources": ["webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/colorui/components/cu-custom.vue?d7bf", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/colorui/components/cu-custom.vue?3a00", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/colorui/components/cu-custom.vue?aea4", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/colorui/components/cu-custom.vue?b13a", "uni-app:///colorui/components/cu-custom.vue"], "names": ["data", "StatusBar", "CustomBar", "name", "computed", "style", "props", "bgColor", "type", "default", "isBack", "bgImage", "methods", "BackPage", "uni", "delta"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;;;AAGxD;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwwB,CAAgB,wxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkB5xB;EACAA;IACA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;QACAA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B", "file": "colorui/components/cu-custom.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cu-custom.vue?vue&type=template&id=213e6c59&\"\nvar renderjs\nimport script from \"./cu-custom.vue?vue&type=script&lang=js&\"\nexport * from \"./cu-custom.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"colorui/components/cu-custom.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cu-custom.vue?vue&type=template&id=213e6c59&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cu-custom.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cu-custom.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"cu-custom\" :style=\"[{height:CustomBar + 'px'}]\">\r\n\t\t\t<view class=\"cu-bar fixed\" :style=\"style\" :class=\"[bgImage!=''?'none-bg text-white bg-img':'',bgColor]\">\r\n\t\t\t\t<view class=\"action\" @tap=\"BackPage\" v-if=\"isBack\">\r\n\t\t\t\t\t<text class=\"cuIcon-back\"></text>\r\n\t\t\t\t\t<slot name=\"backText\"></slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\" :style=\"[{top:StatusBar + 'px'}]\">\r\n\t\t\t\t\t<slot name=\"content\"></slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<slot name=\"right\"></slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tStatusBar: this.StatusBar,\r\n\t\t\t\tCustomBar: this.CustomBar\r\n\t\t\t};\r\n\t\t},\r\n\t\tname: 'cu-custom',\r\n\t\tcomputed: {\r\n\t\t\tstyle() {\r\n\t\t\t\tvar StatusBar= this.StatusBar;\r\n\t\t\t\tvar CustomBar= this.CustomBar;\r\n\t\t\t\tvar bgImage = this.bgImage;\r\n\t\t\t\tvar style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;\r\n\t\t\t\tif (this.bgImage) {\r\n\t\t\t\t\tstyle = `${style}background-image:url(${bgImage});`;\r\n\t\t\t\t}\r\n\t\t\t\treturn style\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tisBack: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tbgImage: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tBackPage() {\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\tdelta: 1\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\r\n</style>\r\n"], "sourceRoot": ""}