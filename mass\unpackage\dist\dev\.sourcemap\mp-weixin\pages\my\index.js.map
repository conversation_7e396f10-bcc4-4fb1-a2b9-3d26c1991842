{"version": 3, "sources": ["webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/my/index.vue?f60b", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/my/index.vue?d775", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/my/index.vue?4c38", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/my/index.vue?b25a", "uni-app:///pages/my/index.vue", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/my/index.vue?087f", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/my/index.vue?a4e4", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/my/index.vue?3013", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/my/index.vue?8a04"], "names": ["components", "Footer", "props", "noticeNum", "type", "default", "data", "ageType", "genderType", "computed", "userInfo", "console", "methods", "getAgeGroup", "getUserInfo", "callback", "go<PERSON><PERSON>", "uni", "url", "cleanStorgage", "handleLogout", "title", "success", "handleUnbundleWx", "api"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA,WAAW,yBAAQ,IAAqB;AACxC;AACA;AACA;AACA,IAAI,MAAM,CAAC;AACX,WAAW,mBAAO,CAAC,4CAAmC;AACtD,WAAW,mBAAO,CAAC,4CAAmC;AACtD,WAAW,mBAAO,CAAC,4CAAmC;AACtD,WAAW,mBAAO,CAAC,4CAAmC;AACtD,mBAAmB,SAA4C;AAC/D,WAAW,mBAAO,CAAC,4CAAmC;AACtD;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAowB,CAAgB,oxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqGxxB;AAEA;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAA;IACAC;EACA;EACAC;IAEAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACA;IAEA;EACA;;EACAC;IACAC;MACAC;MACA;IACA;EACA;EACA;EACA;EACA;EACA;;EAEAC;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACAC;QACA;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACAF;MACAA;MACAA;MACAA;MACAA;MACA;IACA;IACAG;MACA;MACAH;QACAI;QACAC;UACA;YACA;YACA;YACAL;YACA;YACAA;cACA;cACAC;YACA;UACA;QACA;MACA;IACA;IACAK;MACA;MACAN;QACAI;QACAC;UACA;YACAE;YACA;YACA;YACAP;YACAA;cACAC;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1MA;AAAA;AAAA;AAAA;AAAwlC,CAAgB,2kCAAG,EAAC,C;;;;;;;;;;;ACA5mC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA28C,CAAgB,+5CAAG,EAAC,C;;;;;;;;;;;ACA/9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4dcceeb0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=4dcceeb0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4dcceeb0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4dcceeb0&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = require(\"@/static/images/my/\" +\n    _vm.ageType +\n    \"-\" +\n    (_vm.userInfo && _vm.userInfo.sex == \"1\" ? \"man\" : \"women\") +\n    \".png\")\n  var m1 = require(\"@/static/images/my/menu-icon1.svg\")\n  var m2 = require(\"@/static/images/my/menu-icon2.svg\")\n  var m3 = require(\"@/static/images/my/menu-icon3.svg\")\n  var m4 = require(\"@/static/images/my/menu-icon4.svg\")\n  var m5 = false ? require(\"@/static/images/my/menu-icon5.svg\") : null\n  var m6 = require(\"@/static/images/my/menu-icon5.svg\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<zlnavbar bgColor=\"bg-white\" :isBack=\"false\">\r\n\t\t\t<block slot=\"content\">我的</block>\r\n\t\t</zlnavbar>\r\n\t\t<view class=\"main-box\">\r\n\t\t\t<!-- 用户信息 -->\r\n\t\t\t<view class=\"user-info-box\">\r\n\t\t\t\t<view class=\"user-avatar\">\r\n\t\t\t\t\t<!-- <open-data type=\"userAvatarUrl\"></open-data> -->\r\n\t\t\t\t\t<image mode=\"widthFix\"\r\n\t\t\t\t\t\t:src=\"require(`@/static/images/my/${ageType}-${userInfo&&userInfo.sex=='1'?'man':'women'}.png`)\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"user-info\" v-if=\"userInfo\">\r\n\t\t\t\t\t<view class=\"user-name\">{{userInfo.realName || ' '}}</view>\r\n\t\t\t\t\t<view>{{userInfo.phone}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 功能项 -->\r\n\t\t\t<view class=\"list-box\">\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item\" @click=\"goPath('fileManage')\">\r\n\t\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon1.svg`)\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"flex \">本人档案授权管理</view>\r\n\t\t\t\t\t\t\t<view class=\"flex text-right\">\r\n\t\t\t\t\t\t\t\t<view class=\" iconfont icon-arrow_right1 arrow-icon\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"list-item\" @click=\"goPath('accessRecord')\">\r\n\t\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon2.svg`)\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"flex \">调阅记录</view>\r\n\t\t\t\t\t\t\t<view class=\"flex text-right\">\r\n\t\t\t\t\t\t\t\t<view class=\" iconfont icon-arrow_right1 arrow-icon\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item\" @click=\"goPath('familyFileManage')\">\r\n\t\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon3.svg`)\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"flex \">家庭档案授权管理</view>\r\n\t\t\t\t\t\t\t<view class=\"flex text-right\">\r\n\t\t\t\t\t\t\t\t<view class=\" iconfont icon-arrow_right1 arrow-icon\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item\" @click=\"goPath('messageNotification')\">\r\n\t\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon4.svg`)\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"flex \">消息提醒</view>\r\n\t\t\t\t\t\t\t<view class=\"flex text-right\">\r\n\t\t\t\t\t\t\t\t<span class=\"list-cell-label\" v-if=\"noticeNum!=0\">{{noticeNum}}</span>\r\n\t\t\t\t\t\t\t\t<view class=\" iconfont icon-arrow_right1 arrow-icon\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"list-item\" @click=\"goPath('pwdEdit')\" v-if=\"false\">\r\n\t\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon5.svg`)\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"flex \">密码修改</view>\r\n\t\t\t\t\t\t\t<view class=\"flex text-right\">\r\n\t\t\t\t\t\t\t\t<view class=\" iconfont icon-arrow_right1 arrow-icon\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item\" @click=\"goPath('question')\">\r\n\t\t\t\t\t\t<image mode=\"widthFix\" :src=\"require(`@/static/images/my/menu-icon5.svg`)\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"flex \">问卷调查</view>\r\n\t\t\t\t\t\t\t<view class=\"flex text-right\">\r\n\t\t\t\t\t\t\t\t<view class=\" iconfont icon-arrow_right1 arrow-icon\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t<!-- <button class=\"main-btn2\" @click=\"handleLogout\">退出</button> -->\r\n\t\t\t<button class=\"main-btn2\" @click=\"handleUnbundleWx\">微信解绑</button>\r\n\t\t</view>\r\n\t\t<Footer style=\"width: 100%\" />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\timport Footer from \"@/components/footer/index.vue\"\r\n\timport {\r\n\t\tparseTime,\r\n\t\tcheckAgeGroup\r\n\t} from \"@/utils/util\";\r\n\timport {\r\n\t\tsetAuthorization,\r\n\t\tsetInfoLogin\r\n\t} from \"@/utils/auth.js\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tFooter\r\n\t\t},\r\n\t\tprops: {\r\n\r\n\t\t\tnoticeNum: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tageType: 'adult',\r\n\t\t\t\tgenderType: 'man',\r\n\t\t\t\t// userInfo: ''\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tuserInfo() {\r\n\t\t\t\tconsole.log('noticeNum',this.noticeNum)\r\n\t\t\t\treturn this.$store.getters.userInfo\r\n\t\t\t}\r\n\t\t},\r\n\t\t// onLoad(options) {\r\n\t\t// \tconst num = decodeURIComponent(options.num);\r\n\t\t// \tconsole.log('param1:', num);\r\n\t\t// },\r\n\r\n\t\tmethods: {\r\n\t\t\tgetAgeGroup() {\r\n\t\t\t\tthis.ageType = checkAgeGroup(this.userInfo.idCardDecrypt)\r\n\t\t\t},\r\n\t\t\tgetUserInfo(callback) {\r\n\t\t\t\tthis.$store.dispatch('user/storeSetUserInfo').then((res) => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.userInfo = res.data\r\n\t\t\t\t\t\tcallback(res.data)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoPath(path) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/my/${path}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcleanStorgage() {\r\n\t\t\t\tuni.setStorageSync('resident_id', '')\r\n\t\t\t\tuni.setStorageSync(\"isLogin\", '')\r\n\t\t\t\tuni.setStorageSync('isVerify', '')\r\n\t\t\t\tuni.setStorageSync(\"ls_token\", '')\r\n\t\t\t\tuni.setStorageSync('archiveId', '')\r\n\t\t\t\t// uni.setStorageSync('authCode', '') // 不清除内容---授权码\r\n\t\t\t},\r\n\t\t\thandleLogout() {\r\n\t\t\t\tvar that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '确认退出登录？',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tsetAuthorization('')\r\n\t\t\t\t\t\t\tsetInfoLogin(false)\r\n\t\t\t\t\t\t\tuni.clearStorage()\r\n\t\t\t\t\t\t\t// that.cleanStorgage()\r\n\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t// url: '/packagePages/login/index'\r\n\t\t\t\t\t\t\t\turl: '/packagePages/login/wxLogin'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleUnbundleWx() {\r\n\t\t\t\tvar that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '确认要解绑？',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tapi.wxUnbundle({}).then((res) => {})\r\n\t\t\t\t\t\t\tsetAuthorization('')\r\n\t\t\t\t\t\t\tsetInfoLogin(false)\r\n\t\t\t\t\t\t\tuni.clearStorage()\r\n\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\turl: '/packagePages/login/wxLogin'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #F5F6F7 !important;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\tbutton {\r\n\t\t// background-color: #fff;\r\n\t\toutline: none;\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t.main-box {\r\n\t\tpadding: 16px;\r\n\t\tpadding-bottom: 80px;\r\n\t}\r\n\r\n\t.user-info-box {\r\n\t\tpadding: 16px;\r\n\t\tmargin-bottom: 23px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12px;\r\n\r\n\t\t.user-name {\r\n\t\t\tmargin-bottom: 5px;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\r\n\t\t.user-avatar {\r\n\t\t\twidth: 64px;\r\n\t\t\theight: 64px;\r\n\t\t\tbackground-color: #ccc;\r\n\t\t\tmargin-right: 15px;\r\n\t\t\tposition: relative;\r\n\t\t\tz-index: 10;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\toverflow: hidden;\r\n\t\t\tz-index: 10;\r\n\r\n\t\t\timgage {\r\n\t\t\t\twidth: 100% !important;\r\n\t\t\t}\r\n\r\n\t\t\ti {\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tcolor: #C9C9D5;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\tz-index: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t}\r\n\r\n\t.list-box {\r\n\t\t.list-item-box {\r\n\t\t\tmargin-bottom: 17px;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12px;\r\n\t\t\tpadding: 0 16px;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-top: 17px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tborder-bottom: 1px solid #e7e7e7;\r\n\t\t\tpadding: 16px 0;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder: 0;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 24px;\r\n\t\t\t\theight: 24px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list-cell {\r\n\t\t\t// height: 56px;\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #0D0B22;\r\n\t\t\tmargin-left: 18px;\r\n\r\n\t\t\t.list-cell-label {\r\n\t\t\t\tpadding: 1px 5px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tbackground: #D54941;\r\n\t\t\t\tborder-radius: 16px;\r\n\t\t\t\tmargin-left: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t.text-right {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #6E6D7A;\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.arrow-icon {\r\n\t\tfont-size: 20px;\r\n\t\tcolor: #999 !important;\r\n\t\tmargin-left: 5px;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542313678\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=4dcceeb0&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=4dcceeb0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542313778\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}