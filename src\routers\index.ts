import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
  createWebHashHistory,
  Router,
} from 'vue-router'
import Layout from '@/layout/index.vue'
// 扩展继承属性
interface extendRoute {
  hidden?: boolean
}
//
// import tableRouter from './modules/table'
// import dataScreenRouter from './modules/dataScreen'
// import excelRouter from './modules/excel'
// import nestedRouter from './modules/nested'
// import systemRouter from './modules/system'
// import echartsRouter from './modules/echarts'
// import chatRouter from './modules/chat'
// import othersRouter from './modules/other'
// import externalLink from './modules/externalLink'
// import formRouter from './modules/form'
// import functionPageRouter from './modules/functionPage'
import demoRouter from './modules/demo'

// 异步组件
export const asyncRoutes = [
  ...demoRouter,
  // ...dataScreenRouter,
  // ...echartsRouter,
  // ...tableRouter,
  // ...formRouter,
  // ...othersRouter,
  // ...functionPageRouter,
  // ...chatRouter,
  // ...nestedRouter,
  // ...excelRouter,
  // ...externalLink,
  // ...systemRouter,
]

/**
 * path ==> 路由路径
 * name ==> 路由名称
 * component ==> 路由组件
 * redirect ==> 路由重定向
 * alwaysShow ==> 如果设置为true，将始终显示根菜单，无论其子路由长度如何
 * hidden ==> 如果“hidden:true”不会显示在侧边栏中（默认值为false）
 * keepAlive ==> 设为true 缓存
 * meta ==> 路由元信息
 * meta.title ==> 路由标题
 * meta.icon ==> 菜单icon
 * meta.affix ==> 如果设置为true将会出现在 标签栏中
 * meta.breadcrumb ==> 如果设置为false，该项将隐藏在breadcrumb中（默认值为true）
 * meta.activeMenu ==> 详情页的时候可以设置菜单高亮 ,高亮菜单的path
 */

export const constantRoutes: Array<RouteRecordRaw & extendRoute> = [
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/errorPages/404.vue'),
    hidden: true,
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/errorPages/403.vue'),
    hidden: true,
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    hidden: true,
    meta: { title: '门户', noAuth: true },
  },
  {
    path: '/loginging',
    name: 'loging',
    component: () => import('@/views/login/loging.vue'),
    hidden: true,
    meta: { title: '登录', noAuth: true },
  },
  {
    path: '/mk',
    name: 'mk',
    component: () => import('@/views/login/mk.vue'),
    hidden: true,
    meta: { title: '模块', noAuth: true },
  },
  {
    path: '/oa',
    name: 'oa',
    component: () => import('@/views/login/oa.vue'),
    hidden: true,
    meta: { title: 'OA', noAuth: true },
  },
  {
    path: '/rs',
    name: 'rs',
    component: () => import('@/views/login/rs.vue'),
    hidden: true,
    meta: { title: '人事', noAuth: true },
  },
  {
    path: '/xj',
    name: 'xj',
    component: () => import('@/views/login/xj.vue'),
    hidden: true,
    meta: { title: '新建', noAuth: true },
  },
  {
    path: '/dacx',
    name: 'dacx',
    component: () => import('@/views/login/dacx.vue'),
    hidden: true,
    meta: { title: '档案查询', noAuth: true },
  },
  {
    path: '/mk2',
    name: 'mk2',
    component: () => import('@/views/login/mk2.vue'),
    hidden: true,
    meta: { title: '模块', noAuth: true },
  },
  {
    path: '/jsc1',
    name: 'jsc1',
    component: () => import('@/views/login/jsc1.vue'),
    hidden: true,
    meta: { title: '驾驶舱', noAuth: true },
  },
  {
    path: '/jsc2',
    name: 'jsc2',
    component: () => import('@/views/login/jsc2.vue'),
    hidden: true,
    meta: { title: '驾驶舱', noAuth: true },
  },
    {
    path: '/mk3',
    name: 'mk3',
    component: () => import('@/views/login/mk3.vue'),
    hidden: true,
    meta: { title: '模块', noAuth: true },
  },
  {
    path: '/zt1',
    name: 'zt1',
    component: () => import('@/views/login/zt1.vue'),
    hidden: true,
    meta: { title: '中台1', noAuth: true },
  },
  {
    path: '/zt2',
    name: 'zt2',
    component: () => import('@/views/login/zt2.vue'),
    hidden: true,
    meta: { title: '中台2', noAuth: true },
  },
  // {
  //   path: '/login',
  //   name: 'Login',
  //   component: () => import('@/views/login/index.vue'),
  //   hidden: true,
  //   meta: { title: '登录', noAuth: true },
  // },
  // {
  //   path: '/login',
  //   name: 'Login',
  //   component: () => import('@/views/login/index.vue'),
  //   hidden: true,
  //   meta: { title: '登录', noAuth: true },
  // },
  // {
  //   path: '/login',
  //   name: 'Login',
  //   component: () => import('@/views/login/index.vue'),
  //   hidden: true,
  //   meta: { title: '登录', noAuth: true },
  // },
  // {
  //   path: '/login',
  //   name: 'Login',
  //   component: () => import('@/views/login/index.vue'),
  //   hidden: true,
  //   meta: { title: '登录', noAuth: true },
  // },
  {
    path: '/',
    name: 'home',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '首页' },
  },
]

/**
 * notFoundRouter(找不到路由)
 */
export const notFoundRouter = {
  path: '/:pathMatch(.*)',
  name: 'notFound',
  redirect: '/404',
}

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL), // history
  // history: createWebHashHistory(), // hash
  routes: constantRoutes,
})

export default router