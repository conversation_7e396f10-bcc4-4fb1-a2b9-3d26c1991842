<template>
  <div class="login-container" @click="goToModules">
    <img src="@/assets/image/mk.png" alt="登录页" class="login-image">
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToModules = () => {
  router.push('/oa')
}
</script>

<style scoped>
html, body, #app {
  height: 100%;
  margin: 0;
  padding: 0;
}

.login-container {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.login-image {
  width: 100%;
  height: 100%;
  object-position: center; /* 图片居中显示 */

}
</style>