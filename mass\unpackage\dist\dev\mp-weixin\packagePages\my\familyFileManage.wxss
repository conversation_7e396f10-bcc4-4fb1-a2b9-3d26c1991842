
page {
	background-color: #F5F6F7 !important;
}

.main-box.data-v-12093e25 {
  padding: 16px;
}
.switch-box.data-v-12093e25 {
  position: relative;
}
.switch-box .mask-btn.data-v-12093e25 {
  position: absolute;
  display: inline-block;
  width: 45px;
  height: 25px;
  z-index: 999;
}
.list-box .list-item-box.data-v-12093e25 {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 17px;
}
.list-box .list-item-box .list-lable.data-v-12093e25 {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
}
.list-box .list-item-box .list-info-box.data-v-12093e25 {
  font-size: 20px;
  font-weight: 400;
  text-align: center;
  color: rgba(0, 0, 0, 0.9);
  padding-top: 30px;
}
.list-box .list-item-box .list-info-box .bar-code.data-v-12093e25 {
  height: 90px;
  margin-top: 15px;
  margin-left: 30px;
}
.list-box .list-item-box .list-info-box .auth-code.data-v-12093e25 {
  font-size: 60px;
  font-weight: 860;
  color: #1c6ced;
}
.list-box .list-item-box .list-info-box .text-right.data-v-12093e25 {
  text-align: center !important;
}
.list-box .list-item.data-v-12093e25 {
  display: flex;
  align-items: center;
  margin: 17px 0;
  background-color: #fff;
  border-bottom: 0.5px solid #e7e7e7;
  padding-bottom: 18px;
}
.list-box .list-item.data-v-12093e25:last-child {
  border: 0;
}
.list-box .list-item image.data-v-12093e25 {
  width: 24px;
  height: 24px;
  margin-right: 12px;
}
.list-box .list-cell.data-v-12093e25 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 400;
  color: #0D0B22;
}
.list-box .list-cell .list-cell-label.data-v-12093e25 {
  padding: 1px 5px;
  font-size: 14px;
  color: #FFFFFF;
  background: #D54941;
  border-radius: 16px;
  margin-left: 10px;
}
.list-box .text-right.data-v-12093e25 {
  font-size: 12px;
  color: #666;
  opacity: 0.9;
}
.list-box .text-right ._span.data-v-12093e25 {
  color: #1C6CED;
}
button.data-v-12093e25 {
  flex: 1;
  font-size: 14px !important;
  font-weight: 600;
  height: 40px;
  line-height: 40px;
  border-radius: 6px;
  background-color: #F2F3FF;
  color: #1C6CED;
}
button.data-v-12093e25:last-child {
  margin-left: 16px;
}
button[disabled].data-v-12093e25 {
  background-color: #F2F3FF !important;
  color: #B5C7FF !important;
}
.arrow-icon.data-v-12093e25 {
  font-size: 20px;
  color: #999 !important;
  margin-left: 5px;
}
.bottom-tips.data-v-12093e25 {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 20px;
  line-height: 24px;
  padding: 30px;
  padding-top: 0;
}
.bottom-tips view.data-v-12093e25:first-child {
  font-weight: bold;
}

