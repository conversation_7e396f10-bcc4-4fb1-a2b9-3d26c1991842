<template>
  <div class="login-container" @click="goToModules">
    <img src="@/assets/image/login-bg.jpg" alt="登录页" class="login-image">
    <!-- <div class="enter-tip">点击进入模块页面</div> -->
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToModules = () => {
  router.push('/loginging')
}
</script>

<style scoped>
.login-container {
  position: relative;
  width: 100%;
  height: 100vh; /* 固定视口高度 */
  overflow-y: auto; /* 允许垂直滚动 */
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 图片顶部对齐 */
  background-color: #000; 
}

.login-image {
  width: 100%;
  height: auto; /* 保持宽高比 */
  display: block;
  object-fit: contain; /* 等比缩放，不裁剪 */
}
</style>