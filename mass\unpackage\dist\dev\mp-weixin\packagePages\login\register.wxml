<view class="login-wrapper"><zlnavbar vue-id="64ab440a-1" isBack="{{true}}" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content">注册</view></zlnavbar><view class="login-form"><image class="logo" src="../../static/images/logo.png"></image><view class="form-item"><image class="form-icon" src="../../static/images/login/phone-icon.png"></image><input type="text" placeholder="请输入手机号码" placeholder-style="color:#ccc" data-event-opts="{{[['input',[['setInputValue',['$event','phone']]]]]}}" value="{{loginForm.phone}}" bindinput="__e"/></view><view class="form-item"><image class="form-icon" src="../../static/images/login/lock-icon.png"></image><input type="password" placeholder="密码8-20字符 须包含数字、字母及特殊字符" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','password']]]]]}}" value="{{loginForm.password}}" bindinput="__e"/></view><view class="form-item"><image class="form-icon" src="../../static/images/login/lock-icon.png"></image><input type="password" placeholder="确认密码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','confirmPassword']]]]]}}" value="{{loginForm.confirmPassword}}" bindinput="__e"/></view><view class="form-item"><image class="form-icon" src="../../static/images/login/key-icon.png"></image><input type="text" placeholder="请输入图形验证码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','imgCode']]]]]}}" value="{{loginForm.imgCode}}" bindinput="__e"/><image class="verifiy-code" src="{{verifyCodeImg}}" data-event-opts="{{[['tap',[['getVerifyImgCode',['$event']]]]]}}" bindtap="__e"></image></view><view class="form-item"><image class="form-icon" src="../../static/images/login/msg-icon.png"></image><input type="text" placeholder="请输入短信验证码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','messageCode']]]]]}}" value="{{loginForm.messageCode}}" bindinput="__e"/><button class="verify-btn" disabled="{{disabled}}" data-event-opts="{{[['tap',[['getCode',['$event']]]]]}}" bindtap="__e">{{''+btnText}}</button></view><view class="bottom-btn-box"><view data-event-opts="{{[['tap',[['registerSubmit',['$event']]]]]}}" class="main-btn" bindtap="__e">注册</view></view></view><footer style="width:100%;" vue-id="64ab440a-2" bind:__l="__l"></footer></view>