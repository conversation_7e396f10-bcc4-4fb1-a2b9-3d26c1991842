
page {
	background-color: #F5F6F7 !important;
}

button.data-v-597e2d06 {
  outline: none;
  padding: 0;
}
.main-box.data-v-597e2d06 {
  padding: 16px;
  padding-bottom: 80px;
}
.main-btn.data-v-597e2d06 {
  margin-top: 20px;
}
.list-box .feedback-input.data-v-597e2d06 {
  margin-top: 10px;
}
.list-box textarea.data-v-597e2d06 {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-sizing: border-box;
}
.list-box .list-item-box.data-v-597e2d06 {
  margin-bottom: 17px;
  background-color: #fff;
  border-radius: 12px;
  padding: 0 16px;
  margin-bottom: 17px;
}
.list-box .list-item.data-v-597e2d06 {
  display: flex;
  align-items: center;
  padding: 16px 0;
}
.list-box .list-item text.data-v-597e2d06 {
  margin-left: 8px;
}
.list-box .list-cell.data-v-597e2d06 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 400;
  color: #333;
}
.list-box .list-cell .list-lable.data-v-597e2d06 {
  font-weight: 600;
}
.list-box .list-cell .list-cell-label.data-v-597e2d06 {
  padding: 1px 5px;
  font-size: 14px;
  color: #FFFFFF;
  background: #D54941;
  border-radius: 16px;
  margin-left: 10px;
}
.list-box .list-cell .text-right.data-v-597e2d06 {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
.arrow-icon.data-v-597e2d06 {
  font-size: 20px;
  color: #999 !important;
  margin-left: 5px;
}
.empty-box.data-v-597e2d06 {
  padding-top: 100px;
}
checkbox.data-v-597e2d06::before {
  font-family: "cuIcon";
  position: absolute;
  color: #ffffff !important;
  top: 50%;
  left: 5px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
radio.data-v-597e2d06::before {
  font-family: "cuIcon";
  content: "\e645";
  position: absolute;
  color: #ffffff !important;
  top: 50%;
  left: 5px;
  font-size: 32rpx;
  line-height: 16px;
  pointer-events: none;
  transition: all 0.3s ease-in-out 0s;
}

