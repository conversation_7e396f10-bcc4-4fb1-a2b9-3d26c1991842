{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "uni-app:///packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/packagePages/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "scrollMenu", "u<PERSON><PERSON><PERSON>", "modalDialog", "emptyPlaceholder", "computed", "collapseItemData", "console", "userInfo", "data", "modalVisible", "modalData", "type", "title", "info", "btnTxt", "formData", "value", "key", "label", "placeholder", "isOpen", "collapseHeight", "tabCur", "serviceSData", "serviceDataDetail", "collapseData", "detailDate", "tabData", "serviceDataTabList", "apiDataType", "id", "infoData", "familyItem", "columnName", "businessId", "businessName", "tabCurParent", "isYyjl", "yy<PERSON><PERSON><PERSON><PERSON>", "tjlb", "cxjmjkdabhObj", "onLoad", "tabList", "methods", "getData", "fullName", "identityNo", "archiveId", "api", "res", "result", "getResultInfo", "item", "_that", "infoList", "subItem", "indexOf", "detailList", "mjzjzjl_jyjl_mx", "jybgdh", "yljgdm", "zybasy_jyjl_mx", "yytj_tjjl_mx", "tjj<PERSON>", "tjjgdm", "bgdh", "goDetail", "parentData", "collapseIndexData", "serviceData", "currentTabData", "uni", "icon", "url", "handleAIReportClick", "jcjybgdh", "jzlsh", "destype", "handleLongPress", "handleLongPressMul2", "handleCancel", "handleConfirm", "topicCode", "areaCode", "archiveid", "idCard", "bId", "sourceType", "reason", "amendData", "handleChangePanel", "scrollTop", "setTimeout", "duration", "selector", "handleMenuChange", "goHome"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACa;AACyB;;;AAGrG;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvPA;AAAA;AAAA;AAAA;AAA+wB,CAAgB,+xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmQnyB;AAKA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACA;QACAC;QACA;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAN;UACAO;UACAC;QACA,GACA;UACAH;UACAC;UACAN;UACAO;UACAC;QACA;MAEA;MACAC;MACAC;MACArB;MACAsB;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IAAA;IACA;IACA;IACA;IACA;IACAnC;IACA;IACA;IACA;IACA;IACA;IACA;MACA;MACA;MACA;IACA;MACA;MACA;MACA;IACA;IACA;MACA;MACA;MACA,yGACAoC;IACA;MACA;MACA,qGACAf;MACA,oGACAC;IACA;IACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA,sHACA,4BACA;MACA;IACA;IACA;MACA;QACA;UACA;QACA;QACA;QACA;QACA,oGACAA;MACA;MACA;MACA;QACA;QACA;QACA;QACA,oGACAA;QACA;QACA;MACA;;MACA;IACA;EACA;EACAe;IACA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;MACA;MACAC;QACA;UACAC;UACA;UACA;UACA;UACAA;YACA;cACA;cACApC;cACAA;cACA;YACA;YACAqC;cACA1C;cACAS;cACAC;cACAF;YACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAmC;MAAA;MACA;MACA,sGACA,6DACA;QACA;UACA;UACA;UACA,2GACAC;YACA;cACA;cACA;gBACAvC;cACA;gBACA;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;kBACAA;kBACAA;gBACA;cACA;cACA;YACA;YACAqC;UACA;UACA;QACA;UACA;UACA;YACA;YACA;YACA;YACA;cACA;gBACA;gBACArC;gBACA;cACA;cACAqC;YACA;YACA;YACA;UACA;UACA5C;UACAA;UACA;YACA;cACA+C;gBACA;cAAA,CACA;YACA;UACA;UAEA;YACA/C;YACA8C,iEACA;YACA;cACA;cACA;cAEA9C;cAEAgD;gBACA;kBACA;kBACAzC;kBACAA;kBACA0C,iEACA5B,QACAO,WACA;kBAEA;oBACA;oBACArB;oBAEA;sBACAA;oBACA;kBAEA;oBACA;oBACA,uBACA2C;sBACA3C;oBACA;sBACAA,4CACA4C;sBACA5C;oBACA;kBACA;kBACA;gBACA;gBACAP;gBACA4C;cACA;cACA5C;cACA8C;cACA;cACA;gBACA;kBACAA;oBACA;kBACA;gBACA;cACA;YACA;cACA;cACA;gBACAA;kBACAG;kBACA;oBACA;kBACA;kBACA;kBACAA,qFACArB,WACA;kBACA;gBACA;gBACA;gBACA;kBACAkB;oBACA;kBACA;gBACA;cACA;gBACA;gBACAA;gBACA;cACA;YACA;UACA;QACA;QACA;MACA;IACA;IACAM;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEAV;kBAAAW;kBAAAC;gBAAA;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEAb;kBAAAW;kBAAAC;gBAAA;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAxD;gBACA;gBAAA;gBAAA,OACA0C;kBAAAe;kBAAAC;kBAAAC;gBAAA;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAvD;gBAAAwD;gBAAAC;gBACA9D;gBACAA;gBACAqB;gBACAnB;gBACA6D;gBACAtC;gBACAA;;gBAEA;gBACAuC;gBACA;kBACA;kBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;gBACA;;gBAEA;gBAAA,MACAlB;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAH;gBACA;gBACA;kBACAlB;gBACA;cAAA;gBAAA,MAEAqB;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAH;gBACA;gBACA;kBACAlB;gBACA;cAAA;gBAAA,MAEAqB;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAH;gBACA;gBACA;kBACAlB;gBACA;cAAA;gBAAA,IAGAA;kBAAA;kBAAA;gBAAA;gBACAwC;kBACA3D;kBACA4D;gBACA;gBAAA;cAAA;gBAIA;gBACA;;gBAEA;kBACAD;oBACAE;kBACA;gBACA;kBACA9C;kBACA4C;oBACAE;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;MACApE;MACAA;MACA;QAAAqE;QAAAf;QAAAgB;QAAA7B;MACA;QACA8B;QACAF;QACAf;QACAgB;MACA;QACAC;QACAF;QACAf;QACAgB;MACA;QACAC;QACAF;QACAf;QACAgB;MACA;QACAC;QACAF;QACAf;QACAgB;MACA;MACA;MACAL;QACAE;MACA;IACA;IACA;IACAK;MACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACAzE;MACA;MACA;QACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;MAEA;;MAEA;MACA;QACA;QACA;QACA;UACA;UACA;QACA;QACA;QACA;UACA;UACA;QACA;QACA;QACA;UACA;UACA;QACA;MACA;MAEA;MACA;MACA;IACA;IACA0E;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;MACA;MACAV;MACA;MACA;MACA;QACApC;MACA;MAEA;QACAA;MACA;MAEA;MACA;QACA+C;MACA;MAEA;QACAA;QAAA;QACAC;QAAA;QACAhD;QAAA;QACAF;QAAA;QACAC;QAAA;QACAkD;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAzC;QACAuB;QACA;MACA;QACAA;QACA;UACAnB;QACA;MACA;MACA;QACAA;MACA;IAEA;IACAsC;MACA;MACAtC;MACA;QACA;UACA5C;QACA;MACA;MACA;IACA;IACAmF;MAAA;MACAC;QACA;UACArB;YACAsB;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;QACA,gGACApE;MACA;MACA;MACA,yGACA6B;QACA;MACA;IACA;IACAwC;MACAzB;QACAE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/1BA;AAAA;AAAA;AAAA;AAAmmC,CAAgB,slCAAG,EAAC,C;;;;;;;;;;;ACAvnC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAs9C,CAAgB,06CAAG,EAAC,C;;;;;;;;;;;ACA1+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packagePages/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packagePages/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./healthFileDetail.vue?vue&type=template&id=99f81d14&scoped=true&\"\nvar renderjs\nimport script from \"./healthFileDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./healthFileDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./healthFileDetail.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./healthFileDetail.vue?vue&type=style&index=1&id=99f81d14&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"99f81d14\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packagePages/home/<USER>\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetail.vue?vue&type=template&id=99f81d14&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l2 = _vm.serviceDataDetail.arrData\n    ? _vm.__map(_vm.serviceDataDetail.arrData, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var l0 =\n          !item.value && item.label == \"\" ? _vm.collapseItemData(index) : null\n        var l1 =\n          !item.value && !(item.label == \"\")\n            ? _vm.collapseItemData(index)\n            : null\n        return {\n          $orig: $orig,\n          l0: l0,\n          l1: l1,\n        }\n      })\n    : null\n  var g0 =\n    [\"fileSummary\", \"outpatientInfo\", \"hospitalInfor\"].indexOf(\n      this.serviceSData.key\n    ) == -1 && _vm.yyTjerror\n  var g1 = _vm.collapseData ? _vm.serviceDataTabList.length : null\n  var l3 =\n    _vm.collapseData && g1 > 0\n      ? _vm.serviceDataTabList.filter(function (item) {\n          return item.label != \"第一次入户随访详情\"\n        })\n      : null\n  var g2 = _vm.collapseData\n    ? (_vm.collapseData && _vm.collapseData.length > 0) ||\n      (_vm.collapseData &&\n        _vm.collapseData[0].data &&\n        _vm.collapseData[0].data.length > 0)\n    : null\n  var l6 =\n    _vm.collapseData && g2\n      ? _vm.__map(_vm.collapseData, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var l4 =\n            !(item.value && item.type != \"desc\") &&\n            !(item.type == \"desc\") &&\n            item.label == \"\"\n              ? _vm.collapseItemData(index)\n              : null\n          var l5 =\n            !(item.value && item.type != \"desc\") &&\n            !(item.type == \"desc\") &&\n            !(item.label == \"\")\n              ? _vm.collapseItemData(index)\n              : null\n          return {\n            $orig: $orig,\n            l4: l4,\n            l5: l5,\n          }\n        })\n      : null\n  var g3 = _vm.collapseData\n    ? (_vm.collapseData && _vm.collapseData.length == 0) ||\n      (_vm.collapseData &&\n        _vm.collapseData[0].data &&\n        _vm.collapseData[0].data.length == 0)\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, sitem, collapseItem) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        sitem = _temp2.sitem,\n        collapseItem = _temp2.collapseItem\n      var _temp, _temp2\n      sitem.type == \"detail\" ? _vm.goDetail(sitem, collapseItem) : \"\"\n    }\n    _vm.e1 = function ($event, sitem, collapseItem) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        sitem = _temp4.sitem,\n        collapseItem = _temp4.collapseItem\n      var _temp3, _temp4\n      return _vm.handleLongPress(sitem, collapseItem)\n    }\n    _vm.e2 = function ($event, sitem) {\n      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        sitem = _temp6.sitem\n      var _temp5, _temp6\n      return _vm.handleLongPress(sitem)\n    }\n    _vm.e3 = function ($event, collapseItem) {\n      var _temp7 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp8 = _temp7.eventParams || _temp7[\"event-params\"],\n        collapseItem = _temp8.collapseItem\n      var _temp7, _temp8\n      return _vm.handleLongPress(collapseItem)\n    }\n    _vm.e4 = function ($event, collapseItem) {\n      var _temp9 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp10 = _temp9.eventParams || _temp9[\"event-params\"],\n        collapseItem = _temp10.collapseItem\n      var _temp9, _temp10\n      return _vm.handleLongPress(collapseItem)\n    }\n    _vm.e5 = function ($event, index, item) {\n      var _temp11 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp12 = _temp11.eventParams || _temp11[\"event-params\"],\n        index = _temp12.index,\n        item = _temp12.item\n      var _temp11, _temp12\n      return _vm.handleMenuChange(index, item)\n    }\n    _vm.e6 = function (\n      $event,\n      sitem,\n      collapseItem,\n      item,\n      index,\n      collapseIndex\n    ) {\n      var _temp13 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp14 = _temp13.eventParams || _temp13[\"event-params\"],\n        sitem = _temp14.sitem,\n        collapseItem = _temp14.collapseItem,\n        item = _temp14.item,\n        index = _temp14.index,\n        collapseIndex = _temp14.collapseIndex\n      var _temp13, _temp14\n      sitem.type == \"detail\" && sitem.value\n        ? _vm.goDetail(\n            sitem,\n            collapseItem,\n            \"tab\",\n            _vm.infoData[0][item.key][index],\n            item.data[collapseIndex]\n          )\n        : \"\"\n    }\n    _vm.e7 = function (\n      $event,\n      sitem,\n      index,\n      collapseItem,\n      collapseIndex,\n      item\n    ) {\n      var _temp15 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp16 = _temp15.eventParams || _temp15[\"event-params\"],\n        sitem = _temp16.sitem,\n        index = _temp16.index,\n        collapseItem = _temp16.collapseItem,\n        collapseIndex = _temp16.collapseIndex,\n        item = _temp16.item\n      var _temp15, _temp16\n      return _vm.handleLongPressMul2(\n        sitem,\n        index,\n        collapseItem,\n        collapseIndex,\n        item,\n        index\n      )\n    }\n    _vm.e8 = function ($event, sitem, collapseItem, item, index) {\n      var _temp17 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp18 = _temp17.eventParams || _temp17[\"event-params\"],\n        sitem = _temp18.sitem,\n        collapseItem = _temp18.collapseItem,\n        item = _temp18.item,\n        index = _temp18.index\n      var _temp17, _temp18\n      $event.stopPropagation()\n      return _vm.handleAIReportClick(\n        sitem,\n        collapseItem,\n        (_vm.type = \"\"),\n        _vm.infoData[0][item.key][index]\n      )\n    }\n    _vm.e9 = function ($event, sitem) {\n      var _temp19 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp20 = _temp19.eventParams || _temp19[\"event-params\"],\n        sitem = _temp20.sitem\n      var _temp19, _temp20\n      return _vm.handleLongPress(sitem)\n    }\n    _vm.e10 = function (\n      $event,\n      collapsesItem,\n      collapsesIndex,\n      collapseItem,\n      collapseIndex,\n      item,\n      index\n    ) {\n      var _temp21 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp22 = _temp21.eventParams || _temp21[\"event-params\"],\n        collapsesItem = _temp22.collapsesItem,\n        collapsesIndex = _temp22.collapsesIndex,\n        collapseItem = _temp22.collapseItem,\n        collapseIndex = _temp22.collapseIndex,\n        item = _temp22.item,\n        index = _temp22.index\n      var _temp21, _temp22\n      return _vm.handleLongPressMul2(\n        collapsesItem,\n        collapsesIndex,\n        collapseItem,\n        collapseIndex,\n        item,\n        index\n      )\n    }\n    _vm.e11 = function ($event, collapsesItem) {\n      var _temp23 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp24 = _temp23.eventParams || _temp23[\"event-params\"],\n        collapsesItem = _temp24.collapsesItem\n      var _temp23, _temp24\n      return _vm.handleLongPress(collapsesItem)\n    }\n    _vm.e12 = function ($event, sitem, item) {\n      var _temp25 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp26 = _temp25.eventParams || _temp25[\"event-params\"],\n        sitem = _temp26.sitem,\n        item = _temp26.item\n      var _temp25, _temp26\n      sitem.type == \"detail\" ? _vm.goDetail(sitem, item) : \"\"\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l2: l2,\n        g0: g0,\n        g1: g1,\n        l3: l3,\n        g2: g2,\n        l6: l6,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<zlnavbar :isBack=\"true\">\r\n\t\t\t<block slot=\"content\">{{ serviceSData.name }}</block>\r\n\t\t</zlnavbar>\r\n\t\t<!-- 滚动菜单 -->\r\n\t\t<scrollMenu :menuData='scrollMenu' @menuIndex='handleTabMenuChange'></scrollMenu>\r\n\r\n\t\t<!-- main -->\r\n\t\t<view class=\"main-box\">\r\n\t\t\t<view class=\"list-box\">\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item\" style=\"padding: 16px;\">\r\n\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t<view class=\"list-lable list-label-big\">\r\n\t\t\t\t\t\t\t\t{{ serviceSData.key == 'homeDoctor' ? '签约日期：' : '' }}{{ detailDate }}<span></span>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex text-right\">\r\n\t\t\t\t\t\t\t\t<!-- <button @click=\"goHome\">返回档案首页</button> -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 普通非导航 -->\r\n\t\t\t\t<view class=\"list-item-box\">\r\n\t\t\t\t\t<view class=\"list-item-box\" v-if=\"serviceDataDetail.arrData\">\r\n\t\t\t\t\t\t<view class=\"list-item\" v-for=\"(item, index) in serviceDataDetail.arrData\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"list-cell\" @longpress=\"handleLongPress(item)\" v-if=\"item.value\">\r\n\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t<!-- AAA0 -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-right \">{{ item.value }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 非折叠多条数据 -->\r\n\t\t\t\t\t\t\t<view class=\"no-collapse-content-box\" v-for=\"(collapseItem, collapseIndex) in collapseItemData(index)\"\r\n\t\t\t\t\t\t\t\t:key=\"collapseIndex\" v-else-if=\"item.label == ''\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(sitem, index) in collapseItem\" :key=\"sitem.value\">\r\n\t\t\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type == 'detail' && 'list-item2']\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"sitem.type == 'detail' ? goDetail(sitem, collapseItem) : ''\"\r\n\t\t\t\t\t\t\t\t\t\t@longpress=\"handleLongPress(sitem, collapseItem)\">\r\n\t\t\t\t\t\t\t\t\t\t<view :class=\"['list-cell', sitem.type != 'detail' && 'sub-list-cell']\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA1 -->\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 详情 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view :class=\"['text-right', sitem.type == 'detail' && 'text-right-btn']\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ sitem.value }}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-item collapse-content-desc\" @longpress=\"handleLongPress(sitem)\"\r\n\t\t\t\t\t\t\t\t\t\tv-if=\"sitem.type == 'desc'\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA2 -->\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ sitem.value }}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 普通带折叠 -->\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"collapse-item\">\r\n\t\t\t\t\t\t\t\t\t<view class='list-cell' @click.stop.prevent='handleChangePanel(item)'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='list-lable'>{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- AAA3 -->\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"iconfont icon-arrow-right arrow-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'collapse-item-arrow-active': item.isOpen, 'collapse-item--animation': item.isOpen }\"></i>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-show='item.isOpen'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='collapse-content' :class=\"{ 'is--transition': item.isOpen }\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(collapseItem, collapseIndex) in collapseItemData(index)\" :key=\"collapseIndex\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-item\" v-for=\"(collapsesItem, collapsesIndex) in collapseItem\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:key=\"collapsesIndex\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\" @longpress=\"handleLongPress(collapseItem)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"collapsesItem.type != 'desc'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ collapsesItem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA4 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-right\">{{ collapsesItem.value || '' }}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"collapse-content-desc\" @longpress=\"handleLongPress(collapseItem)\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ collapsesItem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA5 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ collapsesItem.value }}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 错误数据反馈 -->\r\n\t\t\t\t<view class=\"hint-label\"\r\n\t\t\t\t\tv-if=\"['fileSummary', 'outpatientInfo', 'hospitalInfor'].indexOf(this.serviceSData.key) == -1 && yyTjerror\">\r\n\t\t\t\t\t<span class=\"iconfont icon-tishi\"></span>错误数据反馈：如果您的健康档案信息有误，请长按错误数据可以进行错误反馈。\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 根据导航菜单渲染列表与折叠内容 -->\r\n\t\t\t\t<view class=\"list-item-box\" v-if='collapseData'>\r\n\t\t\t\t\t<scroll-view class=\"tabs\" scroll-x=\"true\" scroll-with-animation :scroll-left=\"scrollLeft\">\r\n\t\t\t\t\t\t<view class=\"tabs-scroll\" v-if=\"serviceDataTabList.length > 0\">\r\n\t\t\t\t\t\t\t<view class=\"tabs-scroll_item\" :class=\"{ 'tab-active': tabCur == index }\"\r\n\t\t\t\t\t\t\t\tv-for=\" (item, index) in serviceDataTabList.filter(item => item.label != '第一次入户随访详情')\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t@click=\"handleMenuChange(index, item)\">\r\n\t\t\t\t\t\t\t\t<!-- 门诊信息——详情——顶部切换导航， 糖尿病高血压肺结核（只有一个没得切换，当label用） -->\r\n\t\t\t\t\t\t\t\t{{ item.label }}\r\n\t\t\t\t\t\t\t\t<!-- AAA6 -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\r\n\t\t\t\t\t<view class=\"list-content-item\"\r\n\t\t\t\t\t\tv-if=\"collapseData && collapseData.length > 0 || collapseData && collapseData[0].data && collapseData[0].data.length > 0\">\r\n\t\t\t\t\t\t<view class=\"list-item list-item-no-collapse\" v-for=\"(item, index) in collapseData\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"list-cell\" v-if=\"item.value && item.type != 'desc'\" @longpress=\"handleLongPress(item)\">\r\n\t\t\t\t\t\t\t\t<!-- 门诊信息——详情——门诊信息（普通数据，白色背景），肺结核——督导详情（非数组多项部分） -->\r\n\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ item.label }}\r\n\t\t\t\t\t\t\t\t\tAAA7\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-right\">{{ item.value }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!--collapse-content-desc -->\r\n\t\t\t\t\t\t\t<view @longpress=\"handleLongPress(item)\" v-else-if=\"item.type == 'desc'\">\r\n\t\t\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t\tAAA8\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ item.value }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 非折叠多条数据 -->\r\n\t\t\t\t\t\t\t<view class=\"no-collapse-content-box\" v-for=\"(collapseItem, collapseIndex) in collapseItemData(index)\"\r\n\t\t\t\t\t\t\t\t:key=\"collapseIndex\" v-else-if=\"item.label == ''\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(sitem, index) in collapseItem\" :key=\"sitem.value\">\r\n\t\t\t\t\t\t\t\t\t<!-- item.data -->\r\n\t\t\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type == 'detail' && 'list-item2']\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"sitem.type == 'detail' && sitem.value ? goDetail(sitem, collapseItem, 'tab',infoData[0][item.key][index], item.data[collapseIndex]) : ''\"\r\n\t\t\t\t\t\t\t\t\t\t@longpress=\"handleLongPressMul2(sitem, index, collapseItem, collapseIndex, item, index)\"\r\n\t\t\t\t\t\t\t\t\t\tv-if=\"sitem.type != 'desc'\">\r\n\t\t\t\t\t\t\t\t\t\t<view :class=\"['list-cell', sitem.type != 'detail' && 'sub-list-cell']\">\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 门诊信息——详情——除门诊信息外的多选项数据，灰色背景 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lableAI\" @click.stop=\"handleAIReportClick(sitem, collapseItem,type = '',infoData[0][item.key][index])\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image src=\"/static/images/frame.png\" mode=\"aspectFit\" class=\"ai-icon\" v-if=\"sitem.key == 'zyjc_query' || sitem.key == 'jyjc_query'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"ai-label\" v-if=\"sitem.key == 'zyjc_query' || sitem.key == 'jyjc_query'\">AI报告分析</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tAAA9\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 详情 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view :class=\"['text-right', sitem.type == 'detail' && 'text-right-btn']\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ sitem.value }}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-item collapse-content-desc\" @longpress=\"handleLongPress(sitem)\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tAAA10\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ sitem.value }}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 普通带折叠 -->\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"collapse-item\">\r\n\t\t\t\t\t\t\t\t\t<view class='list-cell' @click.stop.prevent='handleChangePanel($event, item)'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='list-lable' @longpress=\"handleLongPress(item)\">\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 糖尿病高血压，肺结核——【随访，体检】详情——多选项——标题 -->\r\n\t\t\t\t\t\t\t\t\t\t\t{{ item.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- AAA11 -->\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"iconfont icon-arrow-right arrow-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'collapse-item-arrow-active': item.isOpen, 'collapse-item--animation': item.isOpen }\"></i>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-show='item.isOpen'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='collapse-content' :class=\"{ 'is--transition': item.isOpen }\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(collapseItem, collapseIndex) in collapseItemData(index)\" :key=\"collapseIndex\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-item\" v-for=\"(collapsesItem, collapsesIndex) in collapseItem\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:key=\"collapsesIndex\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t@longpress=\"handleLongPressMul2(collapsesItem, collapsesIndex, collapseItem, collapseIndex, item, index)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"collapsesItem.type != 'desc'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- 糖尿病高血压，肺结核——【随访，体检】详情——多选项——内容 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ collapsesItem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- AAA12 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-right\">{{ collapsesItem.value || '' }}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"collapse-content-desc\" @longpress=\"handleLongPress(collapsesItem)\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-cell sub-list-cell\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- 门诊信息——列表，体检管理——列表 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ collapsesItem.label }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t <!-- AAA13 -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ collapsesItem.value }}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<empty-placeholder\r\n\t\t\t\t\t\tv-if=\"collapseData && collapseData.length == 0 || collapseData && collapseData[0].data && collapseData[0].data.length == 0\"></empty-placeholder>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 门诊信息：带详情跳转的内容-->\r\n\t\t\t\t<view class=\"list-item-box\" v-for=\"(item, index) in serviceDataDetail.data\" :key=\"index\" v-else>\r\n\t\t\t\t\t<view class=\"list-content-item\">\r\n\t\t\t\t\t\t<block v-for=\"(sitem, index) in item\" :key=\"sitem.value\">\r\n\t\t\t\t\t\t\t<view :class=\"['list-item', sitem.type == 'detail' && 'list-item2']\" v-if=\"sitem.type != 'desc'\">\r\n\t\t\t\t\t\t\t\t<view class=\"list-cell\" @click=\"sitem.type == 'detail' ? goDetail(sitem, item) : ''\"\r\n\t\t\t\t\t\t\t\t\t@longpress=\"handleLongPress(sitem)\">\r\n\t\t\t\t\t\t\t\t\t<!-- 门诊信息——列表，体检管理——列表(textarea) -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">\r\n\t\t\t\t\t\t\t\t\t\t{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\t AAA14\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<!-- 详情 -->\r\n\t\t\t\t\t\t\t\t\t<view :class=\"['text-right', sitem.type == 'detail' && 'text-right-btn']\">{{ sitem.value }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"list-item \" v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"list-cell\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"list-lable\">{{ sitem.label }}\r\n\t\t\t\t\t\t\t\t\t\tAAA15\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-left\">{{ sitem.value }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"main-btn bottom-btn\" v-if=\"false\">错误数据反馈</button>\r\n\t\t</view>\r\n\r\n\t\t<modalDialog :closeVisible='true' :modalData=\"modalData\" @confirm=\"handleConfirm\" @cancel='handleCancel'\r\n\t\t\tv-show=\"modalVisible\">\r\n\t\t</modalDialog>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/api.js\";\r\nimport uCharts from '@/packagePages/components/echarts/echarts.vue';\r\nimport scrollMenu from \"@/packagePages/components/scrollMenu/index.vue\";\r\nimport modalDialog from \"@/components/dialog/dialog.vue\";\r\nimport emptyPlaceholder from '@/packagePages/components/empty.vue'\r\nimport {\r\n\tserviceJsonData\r\n} from './serviceJsonData.js'\r\nimport {\r\n\ttoast,\r\n} from \"@/utils/util.js\";\r\nexport default {\r\n\tcomponents: {\r\n\t\tscrollMenu,\r\n\t\tuCharts,\r\n\t\tmodalDialog,\r\n\t\temptyPlaceholder\r\n\t},\r\n\tcomputed: {\r\n\t\tcollapseItemData() {\r\n\t\t\treturn function (index) {\r\n\t\t\t\tconsole.log('COMPUTED',this.collapseData[index].data)\r\n\t\t\t\treturn this.collapseData[index].data || []\r\n\t\t\t};\r\n\t\t},\r\n\t\tuserInfo() {\r\n\t\t\treturn this.$store.getters.userInfo\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tmodalVisible: false,\r\n\t\t\tmodalData: {\r\n\t\t\t\ttype: 'form',\r\n\t\t\t\ttitle: '错误数据反馈',\r\n\t\t\t\tinfo: '',\r\n\t\t\t\tbtnTxt: '提交',\r\n\t\t\t\tformData: [{\r\n\t\t\t\t\tvalue: null,\r\n\t\t\t\t\tkey: 'reason',\r\n\t\t\t\t\ttype: 'input',\r\n\t\t\t\t\tlabel: '',\r\n\t\t\t\t\tplaceholder: '请输入异常原因'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: null,\r\n\t\t\t\t\tkey: 'amendData',\r\n\t\t\t\t\ttype: 'input',\r\n\t\t\t\t\tlabel: '',\r\n\t\t\t\t\tplaceholder: '请输入更正数据'\r\n\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t},\r\n\t\t\tisOpen: false,\r\n\t\t\tcollapseHeight: 0,\r\n\t\t\tscrollMenu: [],\r\n\t\t\ttabCur: 0,\r\n\t\t\tserviceSData: '',\r\n\t\t\tserviceDataDetail: '',\r\n\t\t\tcollapseData: null,\r\n\t\t\tdetailDate: '',\r\n\t\t\ttabData: '',\r\n\t\t\tserviceDataTabList: [],\r\n\t\t\tapiDataType: '',\r\n\t\t\tid: '',\r\n\t\t\tinfoData: null, // 详情数据\r\n\t\t\tfamilyItem: null,\r\n\t\t\tcolumnName: null,\r\n\t\t\tbusinessId: null,\r\n\t\t\tbusinessName: null,\r\n\t\t\ttabCurParent: null,\r\n\t\t\tisYyjl: false,\r\n\t\t\tyyTjerror: false,\r\n\t\t\ttjlb: '',\r\n\t\t\tcxjmjkdabhObj: {}\r\n\t\t}\r\n\t},\r\n\r\n\tonLoad(options) {\r\n\t\t// console.log('DETAILOption',options)\r\n\t\tthis.tabCurParent = options.tabCurParent\r\n\t\tthis.infoData = options.infoData ? JSON.parse(decodeURIComponent(options.infoData)) : null\r\n\t\tthis.cxjmjkdabhObj = options.familyItem ? JSON.parse(decodeURIComponent(options.familyItem)) : null\r\n\t\tconsole.log('11111111infoData',this.infoData[0],this.cxjmjkdabhObj)\r\n\t\tthis.serviceSData = JSON.parse(options.serviceData)\r\n\t\tthis.detailDate = options.data\r\n\t\tthis.tabData = options.tabData && JSON.parse(options.tabData)\r\n\t\tthis.familyItem = JSON.parse(options.familyItem)\r\n\t\tthis.scrollMenu = [this.familyItem]\r\n\t\tif(options.tjlb && options.tjlb == '8'){\r\n\t\t\tthis.serviceDataDetail = serviceJsonData[`yytjDetail`]\r\n\t\t\tthis.yyTjerror = false\r\n\t\t\tthis.tjlb = '8'\r\n\t\t} else {\r\n\t\t\tthis.serviceDataDetail = serviceJsonData[`${this.serviceSData.key}Detail`]\r\n\t\t\tthis.yyTjerror = true\r\n\t\t\tthis.tjlb = ''\r\n\t\t}\r\n\t\tif (!this.tabData) {\r\n\t\t\tthis.serviceDataTabList = this.serviceDataDetail.tabList\r\n\t\t\tthis.apiDataType = this.serviceDataDetail.tabList && this.serviceDataDetail.tabList[0].type\r\n\t\t\tthis.collapseData = this.serviceDataDetail.tabList && this.serviceDataDetail.data[this.serviceDataDetail\r\n\t\t\t\t.tabList[this.tabCur].value]\r\n\t\t} else {\r\n\t\t\tthis.serviceDataTabList = this.serviceDataDetail[this.tabData.value].tabList\r\n\t\t\tthis.apiDataType = this.serviceDataDetail[this.tabData.value].tabList && this.serviceDataDetail[this\r\n\t\t\t\t.tabData.value].tabList[0].type\r\n\t\t\tthis.collapseData = this.serviceDataTabList && this.serviceDataDetail[this.tabData.value].data[this\r\n\t\t\t\t.serviceDataTabList[this.tabCur].value]\r\n\t\t}\r\n\t\tif (this.serviceSData.key == 'tuberculosis') {\r\n\t\t\tif (this.infoData[0].sfdycsf == 0) {\r\n\t\t\t\tthis.serviceDataDetail.data1.tabList[0].label = '第一次随访记录详情'\r\n\t\t\t} else {\r\n\t\t\t\tthis.serviceDataDetail.data1.tabList[0].label = '随访记录详情'\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (['homeDoctor', \"childManage\", \"physicalExamination\", 'hospitalInfor', \"tuberculosis\", 'diabetes', 'hypertension',\r\n\t\t\t'elderly', 'outpatientInfo',\r\n\t\t].indexOf(this.serviceSData.key) > -1) {\r\n\t\t\tthis.getResultInfo()\r\n\t\t}\r\n\t\tif (['pregnantWoman', \"tuberculosis\"].indexOf(this.serviceSData.key) > -1) {\r\n\t\t\tif (this.tabData.label == \"产前检查\") {\r\n\t\t\t\tthis.serviceDataTabList = this.serviceDataTabList.filter((item) => {\r\n\t\t\t\t\treturn item.label.includes(this.infoData[0]['inspections'])\r\n\t\t\t\t})\r\n\t\t\t\t// this.serviceDataDetail[this.tabData.value].data ——要渲染的数据（第一次，其他次）\r\n\t\t\t\t// this.serviceDataTabList[this.tabCur].value   ——当前dom模型，能拿到到底第几次\r\n\t\t\t\tthis.collapseData = this.serviceDataTabList && this.serviceDataDetail[this.tabData.value].data[this\r\n\t\t\t\t\t.serviceDataTabList[this.tabCur].value]\r\n\t\t\t}\r\n\t\t\t// 肺结核模块详情\r\n\t\t\tif (this.tabData.label == \"随访记录\") {\r\n\t\t\t\t// this.serviceDataTabList = this.serviceDataTabList.filter((item) => {\r\n\t\t\t\t// \treturn item.label.includes(this.infoData[0]['visit'])\r\n\t\t\t\t// })\r\n\t\t\t\tthis.collapseData = this.serviceDataTabList && this.serviceDataDetail[this.tabData.value].data[this\r\n\t\t\t\t\t.serviceDataTabList[this.tabCur].value]\r\n\t\t\t\t// this.collapseData[1].data = this.infoData[1]\r\n\t\t\t\t// console.log(\"this.collapseData\", this.collapseData)\r\n\t\t\t}\r\n\t\t\tthis.getResultInfo()\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 已废弃，在上级查好带下来\r\n\t\tgetData() {\r\n\t\t\tlet params = {\r\n\t\t\t\tfullName: this.familyItem.nameDecrypt || this.familyItem.name,\r\n\t\t\t\tidentityNo: this.familyItem.idCardDecrypt || this.familyItem.idCard,\r\n\t\t\t\tarchiveId: uni.getStorageSync('archiveId'),\r\n\t\t\t}\r\n\t\t\tapi[`${this.serviceSData.key}DetailApi${this.tabCur}`](params).then(res => {\r\n\t\t\t\tif (this.serviceSData.key == \"homeDoctor\") {\r\n\t\t\t\t\tres.data = res.data || []\r\n\t\t\t\t\tlet data = this.serviceDataDetail.data[this.serviceDataDetail.tabList[this.tabCur].value]\r\n\t\t\t\t\tlet temData = data[0].data\r\n\t\t\t\t\tlet result = []\r\n\t\t\t\t\tres.data.forEach((item) => {\r\n\t\t\t\t\t\tlet list = temData[0].map((subItem) => {\r\n\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(subItem))\r\n\t\t\t\t\t\t\tinfo.businessId = item.id\r\n\t\t\t\t\t\t\tinfo.value = item[info.key] || '无'\r\n\t\t\t\t\t\t\treturn info\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tresult.push({\r\n\t\t\t\t\t\t\tdata: [list],\r\n\t\t\t\t\t\t\tkey: data[0].key,\r\n\t\t\t\t\t\t\tlabel: data[0].label,\r\n\t\t\t\t\t\t\tvalue: data[0].value\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.businessName = 'phmdc_sign_service'\r\n\t\t\t\t\tthis.collapseData = result\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetResultInfo() {\r\n\t\t\tlet _that = this\r\n\t\t\tif ([\"homeDoctor\", \"physicalExamination\", 'childManage', 'pregnantWoman', \"tuberculosis\", 'diabetes',\r\n\t\t\t\t'hypertension', 'elderly', 'outpatientInfo', 'hospitalInfor'\r\n\t\t\t].indexOf(this.serviceSData.key) > -1) {\r\n\t\t\t\tif (this.serviceSData.key == 'hospitalInfor' && [2, 3, 4, 6, 7, 8, 9].indexOf(this.tabCur) > -1) {\r\n\t\t\t\t\tlet temData = this.collapseData[0].data\r\n\t\t\t\t\tlet result = []\r\n\t\t\t\t\tthis.infoData[0][this.collapseData[0].key] && this.infoData[0][this.collapseData[0].key].forEach((\r\n\t\t\t\t\t\titem) => {\r\n\t\t\t\t\t\tlet list = temData[0].map((subItem) => {\r\n\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(subItem))\r\n\t\t\t\t\t\t\tif (info.type != 'detail') {\r\n\t\t\t\t\t\t\t\tinfo.value = item[info.key] || '无'\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 第三级内容\r\n\t\t\t\t\t\t\t\tif (['hisZySysjczjl'].indexOf(this.collapseData[0].key) == -1) {\r\n\t\t\t\t\t\t\t\t\tif (info.key == 'zyjc_query') {\r\n\t\t\t\t\t\t\t\t\t\tinfo.label = ''\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tinfo.label = item[info.key] || '无'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tinfo.list = item.hisZySysjcmxjls\r\n\t\t\t\t\t\t\t\t\tinfo.label = ''\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn info\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tresult.push(list)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.collapseData[0].data = result\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 家医签约\r\n\t\t\t\t\tif (this.serviceSData.key == 'homeDoctor') {\r\n\t\t\t\t\t\t// 和上面 hospitalInfor 类似\r\n\t\t\t\t\t\tlet temData = this.collapseData[0].data\r\n\t\t\t\t\t\tlet result = []\r\n\t\t\t\t\t\tthis.infoData.forEach((item) => {\r\n\t\t\t\t\t\t\tlet list = temData[0].map((subItem) => {\r\n\t\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(subItem))\r\n\t\t\t\t\t\t\t\tinfo.value = item[info.key] || '无'\r\n\t\t\t\t\t\t\t\treturn info\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tresult.push(list)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.collapseData[0].data = result\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(\"this.collapseData\", this.collapseData)\r\n\t\t\t\t\tconsole.log(\"4444\", this.infoData)\r\n\t\t\t\t\tif (_that.infoData && _that.infoData.length > 0) {\r\n\t\t\t\t\t\tif (_that.infoData[0].yyjl && _that.infoData[0].yyjl.length > 0) {\r\n\t\t\t\t\t\t\t_that.infoData[0].yyjl.map(item => {\r\n\t\t\t\t\t\t\t\t// item.tjyyjlid\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthis.collapseData.forEach((item, index) => {\r\n\t\t\t\t\t\tconsole.log(\"报错处理: \", this.tabData, this.infoData,item, index)\r\n\t\t\t\t\t\titem.businessId = this.tabData && this.tabData.businessId &&\r\n\t\t\t\t\t\t\tthis.infoData[0][this.tabData.businessId] || this.infoData[0].healthcheckid\r\n\t\t\t\t\t\tif (item.type == 'arr') {\r\n\t\t\t\t\t\t\tlet infoList = this.infoData && this.infoData[0][item.key] || this.infoData || []\r\n\t\t\t\t\t\t\tlet result = []\r\n\r\n\t\t\t\t\t\t\tconsole.log(\"infoList\", infoList)\r\n\r\n\t\t\t\t\t\t\tinfoList.forEach((subItem) => {\r\n\t\t\t\t\t\t\t\tlet arr = item.data[0].map((sitem) => {\r\n\t\t\t\t\t\t\t\t\tlet info = JSON.parse(JSON.stringify(sitem))\r\n\t\t\t\t\t\t\t\t\tinfo.yljgdm = subItem.yljgdm || ''\r\n\t\t\t\t\t\t\t\t\tinfo.bgdh = subItem.bgdh || ''\r\n\t\t\t\t\t\t\t\t\tsubItem.businessId = this.tabData && this.infoData[0][this\r\n\t\t\t\t\t\t\t\t\t\t.tabData\r\n\t\t\t\t\t\t\t\t\t\t.businessId\r\n\t\t\t\t\t\t\t\t\t] || this.infoData[0].healthcheckid\r\n\r\n\t\t\t\t\t\t\t\t\tif (info.type != 'detail') {\r\n\t\t\t\t\t\t\t\t\t\t// console.log(\"info.key\", info.key, subItem[info.key], info.value)\r\n\t\t\t\t\t\t\t\t\t\tinfo.value = subItem[info.key] || '无'\r\n\r\n\t\t\t\t\t\t\t\t\t\tif (info.label == '药物名称') {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.yybh = subItem.tjyyjlid || subItem.yybh\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t// 第三级内容\r\n\t\t\t\t\t\t\t\t\t\tif (['hisMzSysjcmxjl']\r\n\t\t\t\t\t\t\t\t\t\t\t.indexOf(this.collapseData[0].key) == -1) {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.label = item[info.key] || ''\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.list = subItem.cfDetailList || subItem\r\n\t\t\t\t\t\t\t\t\t\t\t\t.detailList\r\n\t\t\t\t\t\t\t\t\t\t\tinfo.label = ''\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\treturn info\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tconsole.log(\"arr\", arr)\r\n\t\t\t\t\t\t\t\tresult.push(arr)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tconsole.log(\"result\", result)\r\n\t\t\t\t\t\t\titem.data = result\r\n\t\t\t\t\t\t\t// 处理女性体检内容\r\n\t\t\t\t\t\t\tif (this.familyItem.sex == '1') {\r\n\t\t\t\t\t\t\t\tif (item.data.length > 0) {\r\n\t\t\t\t\t\t\t\t\titem.data[0] = item.data[0].filter((item) => {\r\n\t\t\t\t\t\t\t\t\t\treturn item.type != \"women\"\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 体检模块  其他\r\n\t\t\t\t\t\t\tif (item.data && item.data[0]) {\r\n\t\t\t\t\t\t\t\titem.data[0].forEach((subItem) => {\r\n\t\t\t\t\t\t\t\t\tsubItem.value = this.infoData[this.tabCur][subItem.key] || '无'\r\n\t\t\t\t\t\t\t\t\tif (this.serviceSData.key == 'physicalExamination') {\r\n\t\t\t\t\t\t\t\t\t\tthis.businessName = 'ehr_jktjjl'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t// 这里this.tabCur不是父级别的导致有问题\r\n\t\t\t\t\t\t\t\t\tsubItem.businessId = this.tabData && this.infoData[this.tabCur][this.tabData\r\n\t\t\t\t\t\t\t\t\t\t.businessId\r\n\t\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t\t\t// console.log(subItem.businessId, 'infoList222222')\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t// 处理女性体检内容\r\n\t\t\t\t\t\t\t\tif (this.familyItem.sex == '1') {\r\n\t\t\t\t\t\t\t\t\titem.data[0] = item.data[0].filter((item) => {\r\n\t\t\t\t\t\t\t\t\t\treturn item.type != \"women\"\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// this.infoData  存在多个【肺结核详情tab】，不能直接取0\r\n\t\t\t\t\t\t\t\titem.value = this.infoData[this.tabCur][item.key] || '无'\r\n\t\t\t\t\t\t\t\t// item.businessId = this.infoData[0][this.tabData.businessId]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(this.serviceDataTabList, 'this.serviceDataTabList====')\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync mjzjzjl_jyjl_mx(str,yljgdm) {\r\n\t\t\t// '2401008728'\r\n\t\t\treturn await api.mjzjzjl_jyjl_mx({ jybgdh: str ,yljgdm: yljgdm})\r\n\t\t},\r\n\t\tasync zybasy_jyjl_mx(str,yljgdm) {\r\n\t\t\t// '1000007'\r\n\t\t\treturn await api.zybasy_jyjl_mx({ jybgdh: str, yljgdm: yljgdm })\r\n\t\t},\r\n\t\tasync yytj_tjjl_mx(data,collapseIndexData) {\r\n\t\t\tconsole.log('医院体检', data,collapseIndexData[0].bgdh)\r\n\t\t\t// '1000007'\r\n\t\t\treturn await api.yytj_tjjl_mx({ tjjlid: data.tjjlid || '', tjjgdm: data.tjjgdm || '', bgdh: collapseIndexData[0].bgdh || '' })\r\n\t\t},\r\n\t\tasync goDetail(item, collapseItem, type = '', parentData, collapseIndexData) {\r\n\t\t\tconsole.log('手动查详情', item, collapseItem, type, parentData, collapseIndexData)\r\n\t\t\tconsole.log('888888医院体检',this.infoData[this.tabCur])\r\n\t\t\tvar tabData = JSON.stringify(this.serviceDataDetail.tabList[this.tabCur])\r\n\t\t\tvar data = item.value\r\n\t\t\tvar serviceData = JSON.stringify(this.serviceSData)\r\n\t\t\tlet infoData = null\r\n\t\t\tinfoData = JSON.stringify(item.list)\r\n\r\n\t\t\t// 获取当前tab下对应的数据\r\n\t\t\tlet currentTabData = null\r\n\t\t\tif (this.infoData && this.infoData.length > 0) {\r\n\t\t\t\t// 如果是数组，获取当前tab索引对应的数据\r\n\t\t\t\tif (Array.isArray(this.infoData)) {\r\n\t\t\t\t\tcurrentTabData = this.infoData[this.tabCur] || this.infoData[0]\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果不是数组，直接使用\r\n\t\t\t\t\tcurrentTabData = this.infoData\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 手动查详情\r\n\t\t\tif (item.key == 'jyjc_query') {\r\n\t\t\t\tlet res = await this.mjzjzjl_jyjl_mx(collapseItem[1].value,item.yljgdm)\r\n\t\t\t\t// console.log(\"res\", res)\r\n\t\t\t\tif (res.code == 200 && res.data.length > 0) {\r\n\t\t\t\t\tinfoData = JSON.stringify(res.data)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (item.key == 'zyjc_query') {\r\n\t\t\t\tlet res = await this.zybasy_jyjl_mx(collapseItem[1].value,item.yljgdm)\r\n\t\t\t\t// console.log(\"res\", res)\r\n\t\t\t\tif (res.code == 200 && res.data.length > 0) {\r\n\t\t\t\t\tinfoData = JSON.stringify(res.data)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (item.key == 'bgrq') {  //医院体检\r\n\t\t\t\tlet res = await this.yytj_tjjl_mx(parentData,collapseIndexData)\r\n\t\t\t\t// console.log(\"res\", res)\r\n\t\t\t\tif (res.code == 200 && res.data.length > 0) {\r\n\t\t\t\t\tinfoData = JSON.stringify(res.data)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (!infoData) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '暂无数据',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\t// 将当前tab下的数据添加到传递参数中\r\n\t\t\t// let currentTabDataParam = currentTabData ? encodeURIComponent(JSON.stringify(currentTabData)) : null\r\n\r\n\t\t\tif (type == '') {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/home/<USER>\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tvar tabData = JSON.stringify(this.serviceDataDetail.tabList[this.tabCur])\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/home/<USER>\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t//跳转AI\r\n\t\t handleAIReportClick(item, collapseItem, type = '', parentData) {\r\n\t\t\tconsole.log('SSSSSSSSSSSSSSSSSSSSS',item, collapseItem, type = '', parentData)\r\n\t\t\tconsole.log('TTTTTTTTTTTTTTTTTTT',this.serviceSData.key)\r\n\t\t\tlet destype = '', jcjybgdh = '', yljgdm = '', jzlsh = '', archiveId = uni.getStorageSync('archiveId')\r\n\t\t\tif (this.serviceSData.key == 'outpatientInfo' && parentData.jcbgdh) {\r\n\t\t\t\tdestype = '1'\r\n\t\t\t\tjcjybgdh = parentData.jcbgdh\r\n\t\t\t\tyljgdm = this.infoData[0].yljgdm\r\n\t\t\t\tjzlsh = this.infoData[0].jzlsh\r\n\t\t\t} else if (this.serviceSData.key == 'hospitalInfor' && parentData.jcbgdh) {\r\n\t\t\t\tdestype = '1'\r\n\t\t\t\tjcjybgdh = parentData.jcbgdh\r\n\t\t\t\tyljgdm = this.infoData[0].yljgdm\r\n\t\t\t\tjzlsh = this.infoData[0].jzlsh\r\n\t\t\t} else if(this.serviceSData.key == 'outpatientInfo' && parentData.jybgdh){\r\n\t\t\t\tdestype = '2'\r\n\t\t\t\tjcjybgdh = parentData.jybgdh\r\n\t\t\t\tyljgdm = this.infoData[0].yljgdm\r\n\t\t\t\tjzlsh = this.infoData[0].jzlsh\r\n\t\t\t} else if(this.serviceSData.key == 'hospitalInfor' && parentData.jybgdh){\r\n\t\t\t\tdestype = '2'\r\n\t\t\t\tjcjybgdh = parentData.jybgdh\r\n\t\t\t\tyljgdm = this.infoData[0].yljgdm\r\n\t\t\t\tjzlsh = this.infoData[0].jzlsh\r\n\t\t\t} \r\n\t\t\t// console.log('AAAAAAAAAAAAAAAAAA',destype,jcjybgdh,yljgdm,jzlsh,archiveId)\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/packagePages/my/dialogue?yljgdm=${yljgdm}&type=2&destype=${destype}&reportid=${jcjybgdh}&jzlsh=${jzlsh}&cxjmjkdabh=${archiveId}`\r\n\t\t\t})\r\n\t\t  },\r\n\t\t// 错误数据反馈\r\n\t\thandleLongPress(item) {\r\n\t\t\tif (!this.$allowFeedback) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif (item.value == '--') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.isYyjl = false\r\n\t\t\t// if (!item.value || item.value == '无' || item.value == '详情') return\r\n\t\t\tif (item.type == 'detail') return\r\n\t\t\tthis.businessId = item.businessId\r\n\t\t\tthis.columnName = item.key || this.businessName\r\n\t\t\tthis.modalData.info = `${item.label} : ${item.value}`\r\n\t\t\t// 门诊-住院不允许错误数据反馈\r\n\t\t\tif (['outpatientInfo', 'hospitalInfor'].indexOf(this.serviceSData.key) == -1) {\r\n\t\t\t\tthis.modalVisible = true\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 多选项的长按—— 糖尿病-高血压，。。。\r\n\t\thandleLongPressMul2(sitem, sindex, collapseItem, collapseIndex, item, index) {\r\n\t\t\tif (!this.$allowFeedback) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif (sitem.value == '--') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.isYyjl = false\r\n\t\t\tif (this.serviceSData.key == 'elderly' && this.tabData['value'] == 'data2') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.abnormalDataType = ''\r\n\t\t\t// 可以从 infoData[0] 获取接口任意值【可能每个接口取值不一样】\r\n\t\t\t// tjjlid——【糖尿病，高血压，体检，老人...】体检主键\r\n\t\t\t// sfbh——【糖尿病，高血压】随访编号\r\n\t\t\t// fjhhzsfbh——肺结核随访编号\r\n\r\n\t\t\t// 多选项的长按——家医签约——履约这种多条得\r\n\t\t\tif (!this.tabData) {\r\n\t\t\t\tconsole.log(this.serviceSData.key, '************************************************************')\r\n\t\t\t}\r\n\t\t\tif (this.serviceSData.key == 'homeDoctor') {\r\n\t\t\t\tthis.businessId = this.infoData[collapseIndex].lyjlid  // 'ehr_jtyslyjlb'\r\n\t\t\t\tthis.businessName = this.serviceDataDetail.tabList[0].businessName\r\n\t\t\t} else if (this.serviceSData.key == 'physicalExamination') {\r\n\t\t\t\tthis.businessId = this.infoData[collapseIndex].tjjlid\r\n\t\t\t} else {\r\n\t\t\t\t// this.businessId = this.infoData[0].tjjlid || this.infoData[0].sfbh || this.infoData[0].fjhhzsfbh\r\n\t\t\t\tthis.businessId = this.infoData[collapseIndex][this.tabData.businessId]\r\n\t\t\t}\r\n\r\n\t\t\tthis.businessName = this.tabData?.businessName || this.businessName //兜底勿删\r\n\r\n\t\t\t// 用药记录的表名称[体检，糖尿病随访，高血压随访]\r\n\t\t\tif (item.key == 'yyjl' || item.key == 'yyqklist') {\r\n\t\t\t\tthis.isYyjl = true\r\n\t\t\t\t// 体检\r\n\t\t\t\tif (item.businessName == 'ehr_jktjyyjl') {\r\n\t\t\t\t\tthis.businessId = item.data[0][0].yybh\r\n\t\t\t\t\tthis.businessName = item.data[0][0].businessName\r\n\t\t\t\t}\r\n\t\t\t\t// 糖尿病\r\n\t\t\t\tif (item.businessName == 'ehr_tnbhzsfyyqk') {\r\n\t\t\t\t\tthis.businessId = item.data[0][0].yybh\r\n\t\t\t\t\tthis.businessName = item.data[0][0].businessName\r\n\t\t\t\t}\r\n\t\t\t\t// 高血压\r\n\t\t\t\tif (item.businessName == 'ehr_gxyhzsfyyqk') {\r\n\t\t\t\t\tthis.businessId = item.data[0][0].yybh\r\n\t\t\t\t\tthis.businessName = item.data[0][0].businessName\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tthis.columnName = sitem.key\r\n\t\t\tthis.modalData.info = `${collapseItem[sindex].label} : ${collapseItem[sindex].value}`\r\n\t\t\tthis.modalVisible = true\r\n\t\t},\r\n\t\thandleCancel() {\r\n\t\t\tthis.modalVisible = false\r\n\t\t},\r\n\t\thandleConfirm(modalData) {\r\n\t\t\tif (!modalData[0].value) {\r\n\t\t\t\ttoast('请输入异常原因')\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.modalVisible = false;\r\n\t\t\tuni.showLoading({})\r\n\t\t\tvar businessName = this.tabData && this.tabData.businessName || this.businessName\r\n\t\t\t// 覆盖上面\r\n\t\t\tif (this.isYyjl) {\r\n\t\t\t\tbusinessName = this.businessName\r\n\t\t\t}\r\n\r\n\t\t\tif (this.serviceSData.key == 'pregnantWoman' && this.serviceDataTabList[0].label == \"第一次产前检查详情\") {\r\n\t\t\t\tbusinessName = this.tabData.businessName2\r\n\t\t\t}\r\n\r\n\t\t\tvar topicCode = this.serviceSData.topicCode\r\n\t\t\tif (businessName == 'ehr_jktjjl' || businessName == 'ehr_jktjyyjl') {\r\n\t\t\t\ttopicCode = '11'\r\n\t\t\t}\r\n\r\n\t\t\tlet params = {\r\n\t\t\t\ttopicCode: topicCode,  // home页面meneu的编码 01-13\r\n\t\t\t\tareaCode: this.familyItem.basicAreaCode || this.familyItem.areaCode, // 用户信息basicAreaCode区划（可能为空）\r\n\t\t\t\tbusinessName: businessName, // 表名\r\n\t\t\t\tcolumnName: this.columnName, // 字段名\r\n\t\t\t\tbusinessId: this.businessId,  //    ？？？  主要是这个字段\r\n\t\t\t\tarchiveid: uni.getStorageSync('archiveId'),\r\n\t\t\t\tidCard: this.familyItem.idCardDecrypt || this.familyItem.idCard,\r\n\t\t\t\tbId: null,\r\n\t\t\t\tsourceType: 3,\r\n\t\t\t\treason: modalData[0].value,\r\n\t\t\t\tamendData: modalData[1].value,\r\n\t\t\t}\r\n\t\t\tapi.serviceFeedback(params).then(res => {\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\ttoast('反馈提交成功', 2000)\r\n\t\t\t}).catch(e => {\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\tthis.modalData.formData.map(item => {\r\n\t\t\t\t\titem.info = ''\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\tthis.modalData.formData.map(item => {\r\n\t\t\t\titem.info = ''\r\n\t\t\t})\r\n\r\n\t\t},\r\n\t\thandleChangePanel(e, item) {\r\n\t\t\tthis.isOpen = !this.isOpen\r\n\t\t\titem.isOpen = !item.isOpen\r\n\t\t\tthis.collapseData.map(data => {\r\n\t\t\t\tif (data.label != item.label) {\r\n\t\t\t\t\tdata.isOpen = false\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tthis.scrollTop()\r\n\t\t},\r\n\t\tscrollTop() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.$nextTick(function () {\r\n\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\tduration: 0,\r\n\t\t\t\t\t\tselector: \".tabs\" //滚动到的元素的id\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t}, 50)\r\n\t\t},\r\n\t\thandleMenuChange(index, item) {\r\n\t\t\t// console.log(\"handleMenuChange\", index, item)\r\n\t\t\tthis.tabCur = index\r\n\t\t\tthis.apiDataType = item.type && item.type || ''\r\n\t\t\tif (!this.tabData) {\r\n\t\t\t\tthis.collapseData = this.serviceDataDetail.data[this.serviceDataDetail.tabList[index].value]\r\n\t\t\t} else {\r\n\t\t\t\tthis.collapseData = this.serviceDataDetail[this.tabData.value].data[this.serviceDataDetail[this\r\n\t\t\t\t\t.tabData.value].tabList[index].value]\r\n\t\t\t}\r\n\t\t\t// 切换时——重新加载数据模型!!!!!\r\n\t\t\tif ([\"childManage\", \"physicalExamination\", 'hospitalInfor', 'outpatientInfo', 'tuberculosis', 'elderly']\r\n\t\t\t\t.indexOf(this.serviceSData.key) > -1) {\r\n\t\t\t\tthis.getResultInfo()\r\n\t\t\t}\r\n\t\t},\r\n\t\tgoHome() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/index/index'\r\n\t\t\t})\r\n\t\t},\r\n\t}\r\n};\r\n</script>\r\n<style>\r\npage {\r\n\tbackground-color: #F5F6F7 !important;\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.main-box {\r\n\tpadding: 16px;\r\n\tpadding-bottom: 70px;\r\n}\r\n\r\n.tabs {\r\n\tborder-bottom: 1px solid #e7e7e7;\r\n}\r\n\r\n.tabs-scroll {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex-wrap: nowrap;\r\n\tbox-sizing: border-box;\r\n\r\n\t.tabs-scroll_item {\r\n\t\theight: 48px;\r\n\t\tline-height: 48px;\r\n\t\tflex-shrink: 0;\r\n\t\tpadding-bottom: 10px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 14px;\r\n\t\tpadding: 0 16px;\r\n\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t}\r\n\r\n\t.tab-active {\r\n\t\tposition: relative;\r\n\t\tcolor: #0052D9 !important;\r\n\r\n\t\t&::after {\r\n\t\t\tcontent: \"\";\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 16px;\r\n\t\t\theight: 3px;\r\n\t\t\tbackground: #0052d9;\r\n\t\t\tborder-radius: 999px;\r\n\t\t\tleft: 50%;\r\n\t\t\tbottom: 0px;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.list-box {\r\n\t.list-item-box2 {\r\n\t\tbackground-color: #EF7029 !important;\r\n\r\n\t\t.list-cell {\r\n\t\t\t.list-lable {\r\n\t\t\t\tfont-size: 14px !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\tfont-weight: 4 00 !important;\r\n\t\t\t}\r\n\r\n\t\t\t.text-right {\r\n\t\t\t\tfont-size: 17px !important;\r\n\t\t\t\tfont-weight: 600 !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\topacity: 1 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.list-item-box {\r\n\t\tmargin-bottom: 17px;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12px;\r\n\t\tmargin-bottom: 17px;\r\n\t}\r\n\r\n\t.list-content-item {\r\n\t\tpadding: 0 16px;\r\n\t}\r\n\r\n\t.list-item2 {\r\n\t\t.list-cell {\r\n\t\t\t.list-lable {\r\n\t\t\t\tcolor: #1C6CED !important;\r\n\t\t\t}\r\n\r\n\t\t\t.text-right {\r\n\t\t\t\tcolor: #333 !important;\r\n\t\t\t}\r\n\r\n\t\t\t.text-right-btn {\r\n\t\t\t\tcolor: #1C6CED !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.list-item {\r\n\t\tpadding: 16px 0;\r\n\t\tborder-bottom: 1px solid #e7e7e7 !important;\r\n\r\n\t\t&:last-child {\r\n\t\t\tborder: 0;\r\n\t\t}\r\n\r\n\t\timage {\r\n\t\t\twidth: 24px;\r\n\t\t\theight: 24px;\r\n\t\t}\r\n\t}\r\n\r\n\t.sub-list-cell {\r\n\t\tfont-size: 12px !important;\r\n\r\n\t\t.list-lable {\r\n\t\t\tfont-weight: 400 !important;\r\n\t\t\tcolor: #666666 !important;\r\n\t\t}\r\n\r\n\r\n\r\n\t\t.text-right-btn {\r\n\t\t\tcolor: #1C6CED !important;\r\n\t\t}\r\n\r\n\t\t.text-left {\r\n\t\t\tcolor: #1C6CED !important;\r\n\t\t\tword-break: break-all;\r\n\t\t}\r\n\t}\r\n\r\n\t.collapse-content-desc {\r\n\t\t.text-right-btn {\r\n\t\t\tcolor: #1C6CED !important;\r\n\t\t}\r\n\r\n\t\t.text-right,\r\n\t\t.text-left {\r\n\t\t\tfont-size: 12px !important;\r\n\t\t\tcolor: #333 !important;\r\n\t\t\tword-break: break-all;\r\n\t\t}\r\n\t}\r\n\r\n\t.list-cell {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tjustify-content: space-between;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #333;\r\n\r\n\t\t.list-label-big {\r\n\t\t\tfont-size: 17px;\r\n\t\t\tdisplay: -webkit-box;\r\n\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t-webkit-line-clamp: 1;\r\n\t\t\tline-clamp: 1;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\r\n\t\t.text-right {\r\n\t\t\tcolor: #333;\r\n\t\t\topacity: 0.9;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t\tmargin-left: 16px;\r\n\t\t\tflex: 1;\r\n\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t.block-label {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-left: 27px;\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.60) !important;\r\n\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\twidth: 12px;\r\n\t\t\t\t\theight: 2px;\r\n\t\t\t\t\tbackground-color: #00919e;\r\n\t\t\t\t\tleft: -20px;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\tborder-radius: 5px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:nth-child(2)::after {\r\n\t\t\t\t\tbackground-color: #FFB546 !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tbutton {\r\n\t\t\t\tfont-size: 14px !important;\r\n\t\t\t\theight: 28px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tborder-radius: 6px;\r\n\t\t\t\tbackground-color: #F2F3FF;\r\n\t\t\t\tcolor: #1C6CED;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.list-lable {\r\n\t\tfont-weight: 600;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\timage {\r\n\t\t\twidth: 24px;\r\n\t\t\theight: 24px;\r\n\t\t\tmargin-right: 8px;\r\n\t\t}\r\n\t}\r\n\r\n\t.text-left {\r\n\t\tfont-size: 14px;\r\n\t\ttext-align: left;\r\n\t\tcolor: #333;\r\n\t\tmargin-top: 5px;\r\n\t\tword-break: break-all;\r\n\t}\r\n}\r\n\r\n.arrow-icon {\r\n\tfont-size: 20px;\r\n\tcolor: #999 !important;\r\n\tmargin-left: 5px;\r\n}\r\n\r\n.hint-label {\r\n\tmargin-bottom: 10px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tcolor: #666;\r\n\r\n\t.icon-tishi {\r\n\t\tfont-size: 20px;\r\n\t\tcolor: #EF7029;\r\n\t}\r\n}\r\n\r\n// 图表\r\n.charts-box {\r\n\twidth: 100%;\r\n\theight: 210px;\r\n}\r\n\r\n.charts-info {\r\n\tfont-size: 12px;\r\n\tfont-weight: 400;\r\n\ttext-align: center;\r\n\tcolor: rgba(0, 0, 0, 0.60);\r\n\tmargin: 15px 0;\r\n\tpadding-bottom: 15px;\r\n}\r\n\r\n.charts-info-left {\r\n\tfont-size: 12px;\r\n\tfont-weight: 400;\r\n\ttext-align: left;\r\n\tcolor: rgba(0, 0, 0, 0.60);\r\n\tmargin-top: 15px;\r\n}\r\n\r\n.charts-label-box {\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 17px;\r\n\r\n\t.charts-label-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\r\n\t\t&:nth-child(2) {\r\n\t\t\tmargin-left: 40px;\r\n\t\t}\r\n\r\n\t\t.charts-label {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: #cccccc;\r\n\r\n\t\t\tspan {\r\n\t\t\t\tfont-size: 45px;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tcolor: #00919e;\r\n\t\t\t\tmargin-right: 5px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n\r\n// 折叠菜单\r\n.collapse-item {\r\n\twidth: 100%;\r\n\r\n\t.collapse-item-arrow-active {\r\n\t\ttransform: rotate(90deg);\r\n\t}\r\n\r\n\t.collapse-item--animation {\r\n\t\ttransition-property: transform;\r\n\t\ttransition-duration: 0.3s;\r\n\t\ttransition-timing-function: ease;\r\n\t}\r\n\r\n\t.collapse-content {\r\n\t\tmargin-top: 17px;\r\n\t\tfont-size: 17px;\r\n\t\tpadding: 0 10px;\r\n\t\tcolor: #666;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 6px;\r\n\r\n\t\t&.is--transition {\r\n\t\t\ttransition-property: width, height, background-color, border-width;\r\n\t\t\ttransition-duration: 0.3s;\r\n\t\t\ttransition-timing-function: ease-in;\r\n\t\t\ttransition-delay: 500ms;\r\n\t\t}\r\n\t}\r\n\r\n\timage {\r\n\t\tmargin-left: 17px;\r\n\t\tmargin-top: 5px;\r\n\t}\r\n}\r\n\r\n.list-item-no-collapse {\r\n\t.no-collapse-content-box {\r\n\t\tmargin-bottom: 16px !important;\r\n\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0 !important;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.no-collapse-content-box {\r\n\t// margin-bottom: 16px!important;\r\n\t.list-lableAI{\r\n\t\tdisplay: inline-flex;\r\n\t\talign-items: center;\r\n\t\tgap: 6px;\r\n\t\timage{\r\n\t\t\twidth: 24px;\r\n\t\t\theight: 24px;\r\n\t\t\tmargin-right: 8px;\r\n\t\t}\r\n\t\t.ai-label{\r\n\t\t\tcolor: #1C6CED !important;\r\n\t\t}\r\n\t}\r\n\t.list-item2 {\r\n\t\tbackground-color: #fff !important;\r\n\t\tborder-bottom: 0px !important;\r\n\t\tpadding: 16px 0 !important;\r\n\r\n\t\t&::after {\r\n\t\t\theight: 0px !important;\r\n\t\t}\r\n\t}\r\n\r\n\t&:first-child {\r\n\t\t.list-item2 {\r\n\t\t\tpadding-top: 0px !important;\r\n\t\t}\r\n\t}\r\n\r\n\t.list-item {\r\n\t\tbackground: #f5f5f5;\r\n\t\tpadding: 16px 10px !important;\r\n\t\tposition: relative;\r\n\t\tborder-bottom: 0;\r\n\r\n\t\t&::after {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 10px;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 44%;\r\n\t\t\theight: 1px;\r\n\t\t\tbackground-color: #dddd;\r\n\t\t}\r\n\r\n\t\t&:nth-child(1) {\r\n\t\t\tborder-radius: 6px 6px 0 0;\r\n\t\t}\r\n\r\n\t\t&:last-child {\r\n\t\t\tborder-radius: 0 0 6px 6px;\r\n\r\n\t\t\t&::after {\r\n\t\t\t\theight: 0px !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.eye-icon {\r\n\tmargin-left: 3px;\r\n\tcolor: #000 !important;\r\n}\r\n\r\n.bottom-btn {\r\n\twidth: 91%;\r\n\tposition: fixed;\r\n\tleft: 50%;\r\n\tbottom: 20px;\r\n\ttransform: translateX(-50%);\r\n\tz-index: 9999;\r\n}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308002\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetail.vue?vue&type=style&index=1&id=99f81d14&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./healthFileDetail.vue?vue&type=style&index=1&id=99f81d14&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542308028\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}