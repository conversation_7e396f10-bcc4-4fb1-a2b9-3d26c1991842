<template>
  <div class="modules-container">
    <el-row :gutter="20">
      <el-col :span="8" v-for="module in modules" :key="module.path">
        <el-card class="module-card" @click="navigateToModule(module.path)">
          <div class="module-icon">
            <el-icon :size="40"><component :is="module.icon" /></el-icon>
          </div>
          <h3 class="module-title">{{ module.title }}</h3>
          <p class="module-desc">{{ module.description }}</p>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { OfficeBuilding, DataLine, Monitor } from '@element-plus/icons-vue'
const modules = ref([
  {
    title: 'OA系统',
    description: '高效的办公自动化系统，提供全面的办公管理解决方案',
    icon: 'OfficeBuilding',
    path: '/module/oa'
  },
  {
    title: '数据驾驶舱',
    description: '直观的数据可视化平台，助力企业决策分析',
    icon: 'DataLine',
    path: '/module/dashboard'
  },
  {
    title: '数据中台',
    description: '统一的数据管理平台，实现数据资产的高效管理',
    icon: 'Monitor',
    path: '/module/datacenter'
  }
])

const navigateToModule = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.modules-container {
  padding: 20px;
}

.module-card {
  height: 200px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.module-card:hover {
  transform: translateY(-5px);
}

.module-icon {
  margin-bottom: 15px;
  color: var(--el-color-primary);
}

.module-title {
  margin: 0 0 10px;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

.module-desc {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  text-align: center;
}
</style>