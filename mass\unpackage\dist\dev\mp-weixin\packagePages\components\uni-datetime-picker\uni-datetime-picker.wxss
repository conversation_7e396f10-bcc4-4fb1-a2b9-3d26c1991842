@charset "UTF-8";
.bottom-btn {
  width: 92%;
  margin: 0 auto;
  position: fixed;
  bottom: 25px;
  left: 16px;
}
.uni-date-btn-box {
  padding: 0 16px;
}
.uni-date {
  width: 100%;
  flex: 1;
}
.uni-date-x {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: #fff;
  color: #666;
  font-size: 14px;
  flex: 1;
}
.uni-date-x .icon-calendar {
  padding-left: 3px;
}
.uni-date-x .range-separator {
  height: 35px;
  line-height: 35px;
}
.uni-date-x--border {
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
}
.uni-date-editor--x {
  display: flex;
  align-items: center;
  position: relative;
}
.uni-date-editor--x .uni-date__icon-clear {
  padding-right: 3px;
  display: flex;
  align-items: center;
}
.uni-date__x-input {
  width: auto;
  height: 35px;
  position: relative;
  flex: 1;
  line-height: 35px;
  font-size: 14px;
  overflow: hidden;
}
.text-center {
  text-align: center;
}
.uni-date__input {
  height: 40px;
  width: 100%;
  line-height: 40px;
  font-size: 14px;
}
.uni-date-range__input {
  text-align: center;
  max-width: 142px;
}
.uni-date-picker__container {
  position: relative;
}
.uni-date-mask--pc {
  position: fixed;
  bottom: 0px;
  top: 0px;
  left: 0px;
  right: 0px;
  background-color: rgba(0, 0, 0, 0);
  transition-duration: 0.3s;
  z-index: 996;
}
.uni-date-single--x {
  background-color: #fff;
  position: absolute;
  top: 0;
  z-index: 999;
  border: 1px solid #EBEEF5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.uni-date-range--x {
  background-color: #fff;
  position: absolute;
  top: 0;
  z-index: 999;
  border: 1px solid #EBEEF5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.uni-date-editor--x__disabled {
  opacity: 0.4;
  cursor: default;
}
.uni-date-editor--logo {
  width: 16px;
  height: 16px;
  vertical-align: middle;
}
/* 添加时间 */
.popup-x-header {
  display: flex;
  flex-direction: row;
}
.popup-x-header--datetime {
  display: flex;
  flex-direction: row;
  flex: 1;
}
.popup-x-body {
  display: flex;
}
.popup-x-footer {
  padding: 0 15px;
  border-top-color: #F1F1F1;
  border-top-style: solid;
  border-top-width: 1px;
  line-height: 40px;
  text-align: right;
  color: #666;
}
.popup-x-footer text:hover {
  color: #007aff;
  cursor: pointer;
  opacity: 0.8;
}
.popup-x-footer .confirm-text {
  margin-left: 20px;
  color: #007aff;
}
.uni-date-changed {
  text-align: center;
  color: #333;
  border-bottom-color: #F1F1F1;
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.uni-date-changed--time text {
  height: 50px;
  line-height: 50px;
}
.uni-date-changed .uni-date-changed--time {
  flex: 1;
}
.uni-date-changed--time-date {
  color: #333;
  opacity: 0.6;
}
.mr-50 {
  margin-right: 50px;
}
/* picker 弹出层通用的指示小三角, todo：扩展至上下左右方向定位 */
.uni-popper__arrow,
.uni-popper__arrow::after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-top-width: 0;
}
.uni-popper__arrow {
  -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
          filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  top: -6px;
  left: 10%;
  margin-right: 3px;
  border-bottom-color: #EBEEF5;
}
.uni-popper__arrow::after {
  content: " ";
  top: 1px;
  margin-left: -6px;
  border-bottom-color: #fff;
}

