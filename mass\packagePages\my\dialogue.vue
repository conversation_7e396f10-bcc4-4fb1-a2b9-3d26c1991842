<template>
	<view class="login-wrapper">
		<zlnavbar :isBack="true">
			<block slot="content">AI健康助手</block>
		</zlnavbar>
		<view class="main-content">
			<view class="dialog-list" v-for="(item,index) in dialogList" :key="index">
				<view :class="item.type === 'question' ? 'dialog-info flex-end-content' : 'dialog-info'">
					<view class="image" v-if="item.type === 'answer'">
						<image src="/static/images/index/AI_icon.png" mode="widthFix"></image>
					</view>
					<view class="dialog-content" :class="item.type === 'answer' ? 'answer' : 'quest'">
						<text v-if="!item.content" class="dotting"></text>
						<rich-text :nodes="item.content">
						</rich-text>
					</view>
					<view class="image image2" v-if="item.type === 'question'">
						<image v-if="item.type === 'question'" src="/static/images/index/userImg.png" mode="widthFix">
						</image>
					</view>
				</view>
			</view>
			<view class="main-button" v-if="!loading">
				<view v-for="(item,index) in btnList">
					<view class="button-list" v-if="item.show" @click="ask(item)">
						<text class="question">{{ item.title }}</text>
						<image src="/static/images/index/arrow-right.png" mode="widthFix"></image>
					</view>
				</view>
				<view class="tips" v-if="dialogList.length > 2 || (dialogList.length >= 2 && type == 2)">
					本内容由AI生成，内容仅供参考，请仔细甄别
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import api from "@/api/api.js";
	import {
		marked
	} from 'marked';
	export default {
		data() {
			return {
				dialogList: [],
				btnList: [],
				loading: false,
				eventSource: null,
				answerContent: '',
				answerInfo: '',
				requestTask: null,
				searchInfo: null,
				conversation_id: '',
				user_info: {},
				type: ''
			};
		},
		async onLoad(options) {
			this.user_info = options
			await this.getContentInfo(options)
			this.type = options.type
			if(!this.searchInfo || !this.user_info.cxjmjkdabh){
				if(options.type == 1){
					this.dialogList.push({
						type: 'answer', 
						content: '您好，欢迎使用AI健康助手服务。我是您的专属健康小管家，能够自动识别并分析您的健康档案，为您提供健康分析及个性化的健康建议。首先，让我为您展示一下我们系统中记录的您的健康档案信息，好吗？'
					})
					
					this.dialogList.push({
						type: 'answer',
						content: '您的健康数据不完善，请稍后重试。'
					})
				}
				if(options.type == 2){
					this.dialogList.push({
						type: 'question',
						content: '利用AI工具分析以上报告内容'
					})
					this.dialogList.push({
						type: 'answer',
						content: '您的健康数据不完善，请稍后重试。'
					})
				}
				return
			}
			if (options.type == 1) {
				this.dialogList.push({
					type: 'answer',
					content: '您好，欢迎使用AI健康助手服务。我是您的专属健康小管家，能够自动识别并分析您的健康档案，为您提供健康分析及个性化的健康建议。首先，让我为您展示一下我们系统中记录的您的健康档案信息，好吗？'
				})
				this.btnList = [{
						id: '1',
						title: '好的，请展示吧',
						show: true,
					},
					{
						id: '2',
						title: '你能告诉我一些健康指导和就医建议吗？',
						show: false,
					},
					// {
					// 	id: '3',
					// 	title: '根据健康状态给出就医指导',
					// 	show: false,
					// },
					{
						id: '4',
						title: '退出，结束此次对话',
						show: true,
					},
				]
			} else if (options.type == 2) {
				this.dialogList.push({
					type: 'question',
					content: '利用AI工具分析以上报告内容'
				}, {
					type: 'answer',
					content: ''
				})
				this.getInfo(4)
			}
		},
		onUnload() {
		    this.stopStream();
		},
		methods: {
			async getContentInfo(info){
				if(!info.cxjmjkdabh) return
				let obj = {
					cxjmjkdabh: info.cxjmjkdabh,
					destype: info.destype || 0,
					yljgdm: info.yljgdm || '',
					reportid: info.reportid || '',
					jzlsh: info.jzlsh || '',
				} 
				await api.getAiInfo(obj).then(res => {
					this.searchInfo = res.data
				})
			},
			ask(info) {
				if (info.id == 4) {
					this.stopStream()
					uni.navigateBack({
						delta: 1
					});
					return
				}
				this.btnList.forEach((item) => {
					if (item.id === info.id) {
						item.show = false
					}
				})
				this.dialogList.push({
					type: 'question',
					content: info.title
				}, {
					type: 'answer',
					content: ''
				})
				this.answerContent = ''
				this.answerInfo = ''
				this.getInfo(info.id)
			},
			getInfo(typeId) {
				this.loading = true
				let Authorization = ''
				
				// 处理密钥
				if(typeId == 1) {
					// 健康状况报告
					Authorization = 'Bearer app-ZbyyAyD3APFIWZUG5Of4UHN3'
				}else if(typeId == 2 || typeId == 3) {
					// 健康指导和就医建议
					Authorization = 'Bearer app-tlFfEqaMybjLoG9joOez2kop'
				}else if(typeId == 4) {
					if (this.user_info.destype == 1 || this.user_info.destype == 2){
						// 检查检验报告分析
						Authorization = 'Bearer app-86t6q6iEjbgGIXcSKFC3BiyQ'
					}else if (this.user_info.destype == 3){
						// 就诊情况分析
						Authorization = 'Bearer app-7fpyQbXksFDvjDYtvmqoZx1K'
					}
				}
				
				let query = typeId == 1 || typeId == 4 ? this.searchInfo : typeId == 2 ? this.searchInfo + '，你能告诉我一些健康指导和就医建议吗' : ''
				this.requestTask = wx.request({

					url: 'https://jsws.bynrws.net/v1/chat-messages', // 线上
					// url: 'http://192.168.3.217/v1/chat-messages', // 测试
					method: 'POST',  
					data: {
						// 请求参数
						inputs: [],
						query,
						responseType: "arraybuffer", // 关键：接收二进制流
						response_mode: 'streaming',
						// conversation_id: this.conversation_id,
						user: this.user_info.cxjmjkdabh
					},
					header: {
						'Authorization': Authorization, // 线上
						// 'Authorization': 'Bearer app-ZhbDl0FSbBE8r98p6wrnnUwT', //测试
						'Content-Type': 'application/json',
					},
					enableChunked: true, // 开启分块传输
					success: (res) => {
						console.log('请求成功:。。。。。。。。。。。。。', res);
						if (res.statusCode !== 200) {
							throw new Error('请求失败');
						}
					},
					fail: (err) => {
						console.error('微信小程序请求失败:', err);
					}
				});
				this.requestTask.onChunkReceived((res) => {
					console.log('收到分块数据:', res);
					const data = res.data
					let txt;
					// 进行判断返回的对象是Uint8Array（开发者工具）或者ArrayBuffer（真机）
					// 1.获取对象的准确的类型
					const type = Object.prototype.toString.call(data); // Uni8Array的原型对象被更改了所以使用字符串的信息进行判断。
					if(type ==="[object Uint8Array]"){
						txt=decodeURIComponent(escape(String.fromCharCode(...data)))
					}else if(data instanceof ArrayBuffer){
						// 将ArrayBuffer转换为Uint8Array
						const uint8Array = new Uint8Array(data);
						txt=decodeURIComponent(escape(String.fromCharCode(...uint8Array)))
					}
					console.log(txt)
					const chunk = txt
					chunk.split('\n').forEach(msg => {
						if (msg.trim()) {
							this.processChunk(msg.trim(), typeId);
						}
					});
				});
			},
			processChunk(chunkStr, typeId) {
				try {
					// 去除可能的前缀 "data:"
					const jsonStr = chunkStr && chunkStr.startsWith('data:') ? chunkStr.substring(5) : chunkStr;

					const data = jsonStr ? JSON.parse(jsonStr) : '';

					if(data && data.conversation_id){
						this.conversation_id = data.conversation_id
					}
					// 更新完整响应对象
					this.fullResponse = {
						...this.fullResponse,
						...data
					};

					// 如果有answer字段，则更新显示内容
					if (data.answer !== undefined) {
						this.answerContent = this.answerContent + this.fullResponse.answer;
					}

					// 处理think部分内容，将其折叠展示
					this.answerContent = this.processThinkContent(this.answerContent);

					this.dialogList[this.dialogList.length - 1].content = marked(this.answerContent)

					// 可以根据其他字段做不同处理
					if (data.event === 'message_end') {
						this.loading = false
						if(typeId == '1'){
							this.btnList.forEach((item) => {
								if (item.id == '2' || item.id == '3') {
									item.show = true
								}
							})
						}
					}
					this.$nextTick(()=>{
						this.scrollToBottom();
					})
				} catch (error) {
					console.error('解析JSON失败:', error, '原始数据:', chunkStr);
				}
			},

			// 处理think部分内容，实现折叠展示
			processThinkContent(content) {
				// 使用正则表达式匹配think标签及其内容
				const thinkRegex = /<think>([\s\S]*?)<\/think>/g;

				return content.replace(thinkRegex, (match, thinkContent) => {
					// 生成唯一ID用于折叠控制
					const thinkId = 'think_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

					// 返回折叠的HTML结构
					return `
						<div class="think-container">
							<div class="think-header" onclick="toggleThink('${thinkId}')">
								<span class="think-icon">🤔</span>
								<span class="think-title">思考过程</span>
								<span class="think-toggle" id="toggle_${thinkId}">▼</span>
							</div>
							<div class="think-content" id="${thinkId}" style="display: none;">
								<div class="think-text">${thinkContent}</div>
							</div>
						</div>
					`;
				});
			},
			// 滚动到底部
			scrollToBottom() {
				uni.pageScrollTo({
					scrollTop: 99999999, // 一个足够大的值，确保滚动到底部，具体值取决于内容长度和设计需求。也可以使用动态计算的方式获取。例如：document.body.scrollHeight - window.innerHeight。但注意，在uni-app中，可能需要使用uni.createSelectorQuery来动态获取。
					duration: 300 // 滚动动画的时长，单位为ms。可以根据需要调整。
				});
			},

			// 停止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.abort();
					this.requestTask = null;
				}
			}
		},
	}
</script>

<style lang="scss">
	.login-wrapper {
		height: 100%;
	}

	.main-content {
		// height: 100%;
		padding: 14px;

		.dialog-list {
			.dialog-info {
				display: flex;
				flex-wrap: wrap;
				flex-shrink: 0;
				column-gap: 10px;
				margin-bottom: 16px;

				view.image {
					width: 32px;
					height: 32px;
					background-color: #F2F2F2;
					border-radius: 50%;
					padding: 8px 4px;
				}

				view.image2 {
					padding: 0;
				}

				.dialog-content {
					max-width: calc(100% - 60px);
					background-color: #F7F7F7;
					padding: 10px;
					font-size: 14px;
				}

				.dialog-content.quest {
					border-radius: 10px 0 10px 10px;
				}

				.dialog-content.answer {
					border-radius: 0 10px 10px 10px;
				}
			}

			.flex-end-content {
				justify-content: flex-end;
			}
		}

		.main-button {
			margin-top: 30px;
			padding: 0 10px 20px 10px;
			text-align: center;

			.button-list {
				margin-top: 10px;
				padding: 10px;
				display: inline-block;
				border-radius: 20px;
				background-color: #F7F7F7;
				font-size: 14px;

				text {
					color: #666;
				}

				text.question {
					margin-right: 10px;
				}

				image {
					width: 20px;
					vertical-align: bottom;
				}
			}
			
			.tips{
				margin-top: 20px;
				color: red;
				font-size: 14px;
			}
		}
	}
	.dotting {
	    display: inline-block;
	    min-width: 2px;
	    min-height: 2px;
	    margin-right: 22px;
	    margin-left: 10px;
	    box-shadow: 2px 0 rgb(28, 108, 237); /* 初始化显示一个点 */
	    animation: dot 1.5s infinite step-start both;
	}
	
	@keyframes dot {
	    0% { box-shadow: 2px 0 rgb(28, 108, 237); } /* 1个点 */
	    25% { box-shadow: none; } /* 0个点 */
	    50% { box-shadow: 2px 0 rgb(28, 108, 237); } /* 1个点 */
	    75% { box-shadow: 2px 0 rgb(28, 108, 237), 6px 0 rgb(28, 108, 237); } /* 2个点 */
	    100% { box-shadow: 2px 0 rgb(28, 108, 237), 6px 0 rgb(28, 108, 237), 10px 0 rgb(28, 108, 237); } /* 3个点 */
	}
</style>