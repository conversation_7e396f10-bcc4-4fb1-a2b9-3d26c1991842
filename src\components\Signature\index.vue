<template>
  <div :class="['signature-modal', orientation]">
    <div class="slot-content">
      <VueSignaturePad ref="signaturePadRef" :options="state.signOptions" width="95%" height="300px" />
    </div>
    <div class="footer">
      <el-button size="small" @click="unDoSign">撤销</el-button>
      <el-button size="small" @click="clearSign">清屏</el-button>
      <el-button size="small" @click="saveUploadSign">确认提交</el-button>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useToggle, useScreenOrientation } from '@vueuse/core';

const signaturePadRef = ref();
const [show, toggle] = useToggle(false);
const { orientation } = useScreenOrientation();
const state = reactive({
  signOptions: {
    penColor: '#000000',
    minWidth: 1.0,
    onBegin: () => {
      signaturePadRef.value.resizeCanvas();
    },
  },
});

const emit = defineEmits(['save']);

// 撤销电子签名
const unDoSign = () => {
  signaturePadRef.value.undoSignature();
  console.log(signaturePadRef.value.options);
};
// 清空电子签名
const clearSign = () => {
  signaturePadRef.value.clearSignature();
};
// 保存并上传电子签名
const saveUploadSign = async () => {
  const { isEmpty, data } = signaturePadRef.value.saveSignature();
  if (isEmpty) return;
  clearSign();
  toggle(false);
  emit('save', data);
};

defineExpose({
  toggle
});
</script>

<style scoped lang="scss">
.signature-modal {
  .footer {
    display: flex;
  }
}
</style>