<view class="wrapper data-v-50f93fd9"><zlnavbar vue-id="63a18766-1" isBack="{{false}}" class="data-v-50f93fd9" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" class="data-v-50f93fd9">AI报告分析</view></zlnavbar><view class="main-box data-v-50f93fd9"><view class="familyList data-v-50f93fd9"><block wx:for="{{familyList}}" wx:for-item="item" wx:for-index="index"><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="{{['data-v-50f93fd9',menuIndex===index?'changedMenu':'']}}" bindtap="__e">{{item.name}}</text></block></view><block wx:if="{{activeIndex===0}}"><view class="main-content data-v-50f93fd9"><image src="/static/images/index/AI_icon.png" mode="widthFix" class="data-v-50f93fd9"></image><view class="main-title data-v-50f93fd9">您好，欢迎使用AI健康助手服务。</view><view class="main-tips data-v-50f93fd9">我是您的专属健康小管家，可以为您提供健康分析以及个性化的健康建议，请把任务交给我吧！</view><view class="main-button data-v-50f93fd9"><view data-event-opts="{{[['tap',[['goPage',[1]]]]]}}" bindtap="__e" class="data-v-50f93fd9">健康指标分析</view><view class="bg1 data-v-50f93fd9"></view><view class="bg2 data-v-50f93fd9"></view></view></view></block><block wx:if="{{activeIndex===2}}"><data-analysis vue-id="63a18766-2" class="data-v-50f93fd9" bind:__l="__l"></data-analysis></block></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="63a18766-3" closeVisible="{{false}}" modalData="{{modalData}}" data-event-opts="{{[['^confirm',[['handleConfirm']]]]}}" bind:confirm="__e" class="data-v-50f93fd9" bind:__l="__l"></modal-dialog><modal-dialog data-custom-hidden="{{!(modalPhoneVisible)}}" vue-id="63a18766-4" closeVisible="{{true}}" modalData="{{modalPhoneData}}" list="{{phoneList}}" data-event-opts="{{[['^confirm',[['handlePhoneConfirm']]],['^cancel',[['handlePhoneCancel']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-50f93fd9" bind:__l="__l"></modal-dialog></view>