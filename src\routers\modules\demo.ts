import Layout from '@/layout/index.vue'
import { RouteRecordRaw } from 'vue-router'

const demoRouter: Array<RouteRecordRaw> = [
  {
    path: '/image-demo',
    // component: Layout,
    name: 'ImageDemo',
    meta: {
      title: '图片演示系统',
      icon: 'Picture',
    },
    children: [
      {
        path: '',
        name: 'ImageDemoIndex',
        component: () => import('@/views/demo/index.vue'),
        meta: { title: '图片演示', icon: 'Picture' },
      },
    ],
  },
  {
    path: '/modules',
    component: Layout,
    name: 'Modules',
    meta: {
      title: '模块页面',
      icon: 'Grid',
    },
    children: [
      {
        path: '',
        name: 'ModulesIndex',
        component: () => import('@/views/modules/index.vue'),
        meta: { title: '模块选择', icon: 'Grid' },
      },
    ],
  },
  {
    path: '/module',
    component: Layout,
    name: 'Module',
    meta: {
      title: '模块详情',
      icon: 'Document',
        component: () => import('@/views/modules/datacenter.vue'),
        meta: { title: '数据中台', icon: 'Monitor' },
      },
  },
]

export default demoRouter