import { defineConfig, ConfigEnv, UserConfig, loadEnv } from 'vite'
import path from 'path'
// vite.config.ts中无法使用import.meta.env 所以需要引入
import vue from '@vitejs/plugin-vue'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
// 增加 vue文件 script name值
import vueSetupExtend from 'vite-plugin-vue-setup-extend'
// 生产gz文件
import viteCompression from 'vite-plugin-compression'
import externalGlobals from "rollup-plugin-external-globals";
import { PACKAGE_NAME } from './src/config/index'
// 按需加载
// import AutoImport from 'unplugin-auto-import/vite'
// import Components from 'unplugin-vue-components/vite'
//import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

function resolve(dir) {
  return path.join(__dirname, '.', dir)
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const VITE_BASE_URL: string = loadEnv(mode, process.cwd()).VITE_BASE_URL;
  return {
    base: VITE_BASE_URL,
    build: {
      outDir: PACKAGE_NAME +VITE_BASE_URL, // 输出文件的目录
      cssCodeSplit: true, // 是否将CSS拆分为更小的块并独立加载
      sourcemap: true, // 开启source map
      rollupOptions: { //解决isCE
        external:  ["vue", "element-plus", "vue-demi"],
        plugins: [
          // externalGlobals({
          //   vue: "Vue",
          //   "element-plus": "ElementPlus",
          //   // 👇 配置 vue-demi 全局变量 👇
          //   "vue-demi": "VueDemi",
          // }),
        ],
        // output: {
        //   globals: {
        //     vue: 'vue'
        //   }
        // }
      },
    },
    plugins: [vue(),
    vueSetupExtend(),
    // AutoImport({
    //   resolvers: [ElementPlusResolver()],
    // }),
    // Components({
    //   resolvers: [ElementPlusResolver()],
    // }),
    // * 使用 svg 图标
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), 'src/icons/svg')],
      // 指定symbolId格式
      symbolId: 'icon-[dir]-[name]',
    }),
    // gzip压缩 生产环境生成 .gz 文件
    mode === 'production' && viteCompression({
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz',
    }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/styles/index.scss" as *;`
        }
      }
    },
    // 配置别名
    resolve: {
      alias: {
        '@': resolve('src'),
        'static': resolve('public/static'),
      },
      // 忽略后缀名的配置选项, 添加 .vue 选项时要记得原本默认忽略的选项也要手动写入
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    //启动服务配置
    server: {
      // 服务器主机名，如果允许外部访问，可设置为 "0.0.0.0" 也可设置成你的ip地址
      // host: '0.0.0.0',
      host: '*************',
      port: 4000,
      open: true,
      https: false,
      cors: true,
      base: VITE_BASE_URL,
      // 代理跨域（模拟示例）
      proxy: {
        // "/api": {
        //   target: "", // easymock
        //   changeOrigin: true,
        //   rewrite: path => path.replace(/^\/api/, "")
        // }
      }
    },
    // 生产环境打包配置
    //去除 console debugger
    // esbuild: {
    //   pure:mode==='production' ? ["console.log", "debugger"] : []
    // },

    // build: {
    //   terserOptions: {
    //     compress: {
    //       drop_console: true,
    //       drop_debugger: true,
    //     },
    //   },
    // },
  }

})

// import { defineConfig,ConfigEnv, UserConfig,loadEnv  } from 'vite'
// import path from 'path'
// // vite.config.ts中无法使用import.meta.env 所以需要引入
// import vue from '@vitejs/plugin-vue'
// import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
// // 增加 vue文件 script name值
// import vueSetupExtend from 'vite-plugin-vue-setup-extend'
// // 生产gz文件
// import viteCompression from 'vite-plugin-compression'
// // 按需加载
// // import AutoImport from 'unplugin-auto-import/vite'
// // import Components from 'unplugin-vue-components/vite'
// //import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// function resolve (dir) {
//   return path.join(__dirname, '.', dir)
// }

// // https://vitejs.dev/config/
// export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
//   const VITE_BASE_URL: string = loadEnv(mode, process.cwd()).VITE_BASE_URL;
//   return {
//     // base: VITE_BASE_URL,
//     base: './', // 默认值，适用于部署在域名根路径下
//     build: {
//       outDir:  "./dist", // 输出文件的目录
//       // outDir: VITE_BASE_URL, // 输出文件的目录
//       cssCodeSplit: true, // 是否将CSS拆分为更小的块并独立加载
//       rollupOptions: { //解决isCE
//         external:  ["vue", "element-plus", "vue-demi"],
//         // plugins: [
//         //   externalGlobals({
//         //     vue: "Vue",
//         //     "element-plus": "ElementPlus",
//         //     // 👇 配置 vue-demi 全局变量 👇
//         //     "vue-demi": "VueDemi",
//         //   }),
//         // ],
//         // output: {
//         //   globals: {
//         //     vue: 'vue'
//         //   }
//         // }
//       },
//     },
//     plugins: [vue(),
//       vueSetupExtend(),
//       // AutoImport({
//       //   resolvers: [ElementPlusResolver()],
//       // }),
//       // Components({
//       //   resolvers: [ElementPlusResolver()],
//       // }),
//       // * 使用 svg 图标
//       createSvgIconsPlugin({
//         // 指定需要缓存的图标文件夹
//         iconDirs: [path.resolve(process.cwd(), 'src/icons/svg')],
//         // 指定symbolId格式
//         symbolId: 'icon-[dir]-[name]',
//       }),
//       // gzip压缩 生产环境生成 .gz 文件
//       mode==='production'&&viteCompression({
//          verbose: true,
//          disable: false,
//          threshold: 10240,
//          algorithm: 'gzip',
//          ext: '.gz',
//        }),
//     ],
//     // base:loadEnv(mode, process.cwd()).VITE_API_URL,
//     css: {
//       preprocessorOptions: {
//         scss: {
//           additionalData: `@use "./src/styles/index.scss" as *;`
//         }
//       }
//     },
//     // 配置别名
//     resolve: {
//       alias: {
//         '@':resolve('src'),
//         'static':resolve('public/static'),
//       },
//       // 忽略后缀名的配置选项, 添加 .vue 选项时要记得原本默认忽略的选项也要手动写入
//       extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
//     },
//     //启动服务配置
//     server: {
//       // 服务器主机名，如果允许外部访问，可设置为 "0.0.0.0" 也可设置成你的ip地址
//       host: '0.0.0.0',
//       port: 8080,
//       open: true,
//       https: false,
//       cors: true,
//       // base: VITE_BASE_URL,
//       base: './',
//       // 代理跨域（模拟示例）
//       proxy: {
//         // "/api": {
//         //   target: "", // easymock
//         //   changeOrigin: true,
//         //   rewrite: path => path.replace(/^\/api/, "")
//         // }
//       }
//     },
//     // 生产环境打包配置
//     //去除 console debugger
//     // esbuild: {
//     //   pure:mode==='production' ? ["console.log", "debugger"] : []
//     // },

//     // build: {
//     //   terserOptions: {
//     //     compress: {
//     //       drop_console: true,
//     //       drop_debugger: true,
//     //     },
//     //   },
//     // },
//   }

// })
