
page {
	background-color: #F5F6F7 !important;
}

button.data-v-4dcceeb0 {
  outline: none;
  padding: 0;
}
.main-box.data-v-4dcceeb0 {
  padding: 16px;
  padding-bottom: 80px;
}
.user-info-box.data-v-4dcceeb0 {
  padding: 16px;
  margin-bottom: 23px;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12px;
}
.user-info-box .user-name.data-v-4dcceeb0 {
  margin-bottom: 5px;
  font-weight: 600;
}
.user-info-box .user-avatar.data-v-4dcceeb0 {
  width: 64px;
  height: 64px;
  background-color: #ccc;
  margin-right: 15px;
  position: relative;
  z-index: 10;
  border-radius: 50%;
  overflow: hidden;
  z-index: 10;
}
.user-info-box .user-avatar imgage.data-v-4dcceeb0 {
  width: 100% !important;
}
.user-info-box .user-avatar ._i.data-v-4dcceeb0 {
  font-size: 20px;
  color: #C9C9D5;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  z-index: 0;
}
.list-box .list-item-box.data-v-4dcceeb0 {
  margin-bottom: 17px;
  background-color: #fff;
  border-radius: 12px;
  padding: 0 16px;
}
.list-box .list-item-box.data-v-4dcceeb0:last-child {
  margin-top: 17px;
}
.list-box .list-item.data-v-4dcceeb0 {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e7e7e7;
  padding: 16px 0;
}
.list-box .list-item.data-v-4dcceeb0:last-child {
  border: 0;
}
.list-box .list-item image.data-v-4dcceeb0 {
  width: 24px;
  height: 24px;
}
.list-box .list-cell.data-v-4dcceeb0 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 400;
  color: #0D0B22;
  margin-left: 18px;
}
.list-box .list-cell .list-cell-label.data-v-4dcceeb0 {
  padding: 1px 5px;
  font-size: 14px;
  color: #FFFFFF;
  background: #D54941;
  border-radius: 16px;
  margin-left: 10px;
}
.list-box .list-cell .text-right.data-v-4dcceeb0 {
  font-size: 14px;
  color: #6E6D7A;
}
.arrow-icon.data-v-4dcceeb0 {
  font-size: 20px;
  color: #999 !important;
  margin-left: 5px;
}

