<view class="data-v-12093e25"><zlnavbar vue-id="2f1f0781-1" bgColor="bg-white" isBack="{{true}}" class="data-v-12093e25" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" class="data-v-12093e25">家庭档案授权管理</view></zlnavbar><view class="main-box data-v-12093e25"><block wx:if="{{$root.g0!=0}}"><view class="list-box data-v-12093e25"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item-box data-v-12093e25"><view class="list-cell data-v-12093e25"><view class="list-lable data-v-12093e25">{{item.$orig.name}}</view><view class="flex text-right data-v-12093e25">{{'与户主关系：'+item.$orig.hzRelation+''}}</view></view><view class="list-item data-v-12093e25"><image mode="widthFix" src="{{item.m0}}" class="data-v-12093e25"></image><view class="list-cell data-v-12093e25"><view class="flex data-v-12093e25">档案授权状态</view><view class="flex switch-box data-v-12093e25"><block wx:if="{{switchVisible}}"><switch color="#1C6CED" disabled-color="red" disabled="{{item.$orig.disabled}}" checked="{{item.$orig.empowerStatus?true:false}}" value="{{item.$orig.empowerStatus}}" data-event-opts="{{[['change',[['handleSwitchChange',['$event','$0'],[[['familyList','',index]]]]]],['input',[['__set_model',['$0','empowerStatus','$event',[]],[[['familyList','',index]]]]]]]}}" bindchange="__e" bindinput="__e" class="data-v-12093e25"></switch></block><block wx:if="{{item.$orig.disabled}}"><label data-event-opts="{{[['tap',[['showAuthToast',['$0'],[[['familyList','',index]]]]]]]}}" class="mask-btn _span data-v-12093e25" bindtap="__e"></label></block></view></view></view><view class="flex data-v-12093e25"><button data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e" class="data-v-12093e25">{{''+(item.$orig.bindStatus?'解除查阅绑定':'档案查阅绑定')+''}}</button><button disabled="{{!item.$orig.empowerStatus||!item.$orig.bindStatus?true:false}}" data-event-opts="{{[['tap',[['handleCodeShowChange',[index,'$0'],[[['familyList','',index]]]]]]]}}" bindtap="__e" class="data-v-12093e25">获取授权码</button></view><block wx:if="{{item.$orig.authCode}}"><view class="list-info-box data-v-12093e25"><view class="data-v-12093e25">请出示或告知医生此授权码</view><view class="auth-code data-v-12093e25">{{item.$orig.authCode}}</view><canvas class="bar-code data-v-12093e25" canvas-id="{{'barcode-'+index}}"></canvas></view></block></view></block></view></block><block wx:else><empty-page vue-id="2f1f0781-2" class="data-v-12093e25" bind:__l="__l"></empty-page></block></view><view class="bottom-tips data-v-12093e25"><view class="data-v-12093e25">注意事项：</view><view class="data-v-12093e25">1.家庭关系通过公共卫生服务系统获取，如有错误，请到您的档案管理机构修改。</view><view class="data-v-12093e25">2.健康档案调阅授权仅限单次授权，再次调阅再次授权。</view><view class="data-v-12093e25">3.授权码的有效期为5分钟，超时则失效，若仍要使用授权码则需要重新获取。</view><view class="data-v-12093e25">4.若档案授权状态关闭，则健康档案无法被医疗机构调用，即无法获取授权码。</view></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="2f1f0781-3" closeVisible="{{true}}" modalData="{{modalData}}" data-event-opts="{{[['^confirm',[['handleConfirm']]],['^cancel',[['handleCancel']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-12093e25" bind:__l="__l"></modal-dialog></view>