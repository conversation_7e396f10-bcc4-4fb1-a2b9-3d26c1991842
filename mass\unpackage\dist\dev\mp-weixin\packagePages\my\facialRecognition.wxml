<view class="login-wrapper"><zlnavbar vue-id="f2f7a3ea-1" isBack="{{true}}" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content">人脸识别验证</view></zlnavbar><view class="login-form"><image class="form-icon" mode="widthFix" src="../../static/images/login/register-success.png"></image><view class="register-info"><view class="info-title">注册成功！</view><view>为保障您的个人隐私及数据安全，确保所有操作为本人操作，请先进行人脸核身，验证通过后方可查看健康档案信息。</view></view><view class="bottom-btn-box"><view data-event-opts="{{[['tap',[['emailLogin',['$event']]]]]}}" class="main-btn" bindtap="__e">下一步</view></view><view class="bottom-info"><checkbox class="checked-btn" color="#1C6CED" value="{{checkValue}}" checked="{{checkState}}" data-event-opts="{{[['change',[['handleRadioChange',['$event']]]]]}}" bindchange="__e"></checkbox><view>登录即代表同意<label data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="_span">《用户服务协议》</label>和<label data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e" class="_span">《个人信息保护政策》</label>，首次登录用户请使用短信验证码方式登录，登录成功后将自动为您创建账号。</view></view></view></view>