<!DOCTYPE html>
<html lang="" >
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/yl/manager/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="/js/vue.js"></script>
    <!-- 👇 添加 vue-demi 的 CDN 地址 👇 -->
    <script src="/js/vue-demi.js"></script>
    <script src="/js/element-plus.js"></script>
    <title>YL</title>
    <script type="module" crossorigin src="/yl/manager/assets/index.dc283410.js"></script>
    <link rel="stylesheet" href="/yl/manager/assets/index.5e77a0f0.css">
  </head>
  <body>
  <div id="app">
      <style>
          .first-loading-wrp {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 90vh;
              min-height: 90vh;
          }
          .first-loading-wrp > h1 {
              font-size: 30px;
              font-weight: bolder;
          }
          .first-loading-wrp .loading-wrp {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 98px;
          }
          .dot {
              position: relative;
              box-sizing: border-box;
              display: inline-block;
              width: 64px;
              height: 64px;
              font-size: 64px;
              transform: rotate(45deg);
              animation: antRotate 1.2s infinite linear;
          }

          .dot i {
              position: absolute;
              display: block;
              width: 28px;
              height: 28px;
              background-color: #1890ff;
              border-radius: 100%;
              opacity: 0.3;
              transform: scale(0.75);
              transform-origin: 50% 50%;
              animation: antSpinMove 1s infinite linear alternate;
          }

          .dot i:nth-child(1) {
              top: 0;
              left: 0;
          }

          .dot i:nth-child(2) {
              top: 0;
              right: 0;
              -webkit-animation-delay: 0.4s;
              animation-delay: 0.4s;
          }

          .dot i:nth-child(3) {
              right: 0;
              bottom: 0;
              -webkit-animation-delay: 0.8s;
              animation-delay: 0.8s;
          }

          .dot i:nth-child(4) {
              bottom: 0;
              left: 0;
              -webkit-animation-delay: 1.2s;
              animation-delay: 1.2s;
          }
          @keyframes antRotate {
              to {
                  -webkit-transform: rotate(405deg);
                  transform: rotate(405deg);
              }
          }

          @-webkit-keyframes antRotate {
              to {
                  -webkit-transform: rotate(405deg);
                  transform: rotate(405deg);
              }
          }

          @keyframes antSpinMove {
              to {
                  opacity: 1;
              }
          }

          @-webkit-keyframes antSpinMove {
              to {
                  opacity: 1;
              }
          }

      </style>
      <div id="YL">
          <div class="first-loading-wrp">
              <div class="loading-wrp">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
              </div>
              <h1>正在加载，请稍等...</h1>
          </div>
      </div>
  </div>
    
  </body>
</html>

