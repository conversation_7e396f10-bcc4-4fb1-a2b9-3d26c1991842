{"name": "YL", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --port 4000 --mode development", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "commit": "git add -A && czg ", "build": "vite build", "preview": "vite preview", "build:ts": "vue-tsc --noEmit --skipLibCheck && vite build", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\""}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@better-scroll/core": "^2.4.2", "@element-plus/icons-vue": "^2.1.0", "@vuemap/vue-amap": "^2.1.1", "@vuemap/vue-amap-extra": "^2.1.1", "@vuemap/vue-amap-loca": "^2.1.1", "@vueuse/core": "^9.1.1", "@wangeditor/editor": "^5.1.14", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^0.27.2", "clipboard": "^2.0.10", "codemirror": "^5.65.9", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.4", "echarts": "^5.3.1", "element-plus": "^2.2.28", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "fuse.js": "^6.6.2", "jszip": "^3.9.1", "mavon-editor": "^2.10.4", "md-editor-v3": "^1.11.3", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.0", "pinia": "2.0.36", "pinia-plugin-persistedstate": "^2.1.1", "print-js": "^1.6.0", "raf": "^3.4.1", "resize-observer-polyfill": "^1.5.1", "sass": "^1.54.0", "splitpanes": "^3.1.1", "svg-sprite-loader": "^6.0.11", "vue": "^3.2.39", "vue-cropper": "^1.0.3", "vue-cropperjs": "^5.0.0", "vue-fuse": "^4.1.1", "vue-mugen-scroll": "^0.2.6", "vue-qr": "^4.0.6", "vue-router": "^4.1.6", "vue-splitpane": "^1.0.6", "vue3-text-clamp": "^0.1.1", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "@vitejs/plugin-vue": "^3.0.0", "commitizen": "^4.2.5", "consola": "^2.15.3", "cz-git": "^1.3.12", "czg": "^1.3.12", "dart-sass": "^1.25.0", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.3.0", "fast-glob": "^3.2.11", "postcss-pxtorem": "^6.0.0", "prettier": "^2.7.1", "rollup-plugin-external-globals": "^0.9.1", "typescript": "^4.6.4", "unplugin-auto-import": "^0.10.3", "unplugin-vue-components": "^0.21.2", "unplugin-vue-define-options": "^0.7.3", "vite": "^3.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^0.38.4"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}