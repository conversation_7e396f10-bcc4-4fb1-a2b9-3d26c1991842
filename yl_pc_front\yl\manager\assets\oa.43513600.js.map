{"version": 3, "file": "oa.43513600.js", "sources": ["../../../../src/assets/image/4oa.png", "../../../../src/views/login/oa.vue"], "sourcesContent": ["export default \"__VITE_ASSET__d195c4e2__\"", "<template>\r\n  <div class=\"login-container\" @click=\"goToModules\">\r\n    <img src=\"@/assets/image/4oa.png\" alt=\"登录页\" class=\"login-image\">\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { useRouter } from 'vue-router'\r\n\r\nconst router = useRouter()\r\n\r\nconst goToModules = () => {\r\n  router.push('/rs')\r\n}\r\n</script>\r\n\r\n<style scoped>\r\nhtml, body, #app {\r\n  height: 100%;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n.login-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n.login-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-position: center; /* 图片居中显示 */\r\n\r\n}\r\n</style>"], "names": ["_imports_0", "router", "useRouter", "goToModules", "_cache"], "mappings": "+CAAA,MAAeA,EAAA,kFCSf,MAAAC,EAAAC,IAEAC,EAAA,IAAA,CACEF,EAAA,KAAA,KAAA,CAAiB,gCAXjB,IAAA,mBAAA,MAAA,CAEM,MAAA,kBAFK,QAAAE,CAA0B,EAAAC,EAAA,KAAAA,EAAA,GAAA,CACnC,8BAAgE,IAAAJ,EAA3D,IAAA,qBAAiC,MAAA,aAAY,EAAA,KAAA,EAAA"}