<view class="login-wrapper"><zlnavbar vue-id="2724521f-1" isBack="{{true}}" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content">密码重置</view></zlnavbar><view class="login-form"><image class="logo" src="../../static/images/logo.png"></image><view class="form-item"><image class="form-icon" src="../../static/images/login/phone-icon.png"></image><input type="text" placeholder="请输入手机号码" placeholder-style="color:#ccc" data-event-opts="{{[['input',[['setInputValue',['$event','phone']]]]]}}" value="{{loginForm.phone}}" bindinput="__e"/></view><view class="form-item"><image class="form-icon" src="../../static/images/login/msg-icon.png"></image><input type="text" placeholder="请输入短信验证码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','messageCode']]]]]}}" value="{{loginForm.messageCode}}" bindinput="__e"/><button class="verify-btn" disabled="{{disabled}}" data-event-opts="{{[['tap',[['getCode',['$event']]]]]}}" bindtap="__e">{{''+btnText}}</button></view><view class="form-item"><image class="form-icon" src="../../static/images/login/lock-icon.png"></image><block wx:if="{{pwdType}}"><input type="password" placeholder="8-20字符须包含数字、字母及特殊字符" adjust-position="{{false}}" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','password']]]]]}}" value="{{loginForm.password}}" bindinput="__e"/></block><block wx:else><input type="text" placeholder="8-20字符须包含数字、字母及特殊字符" adjust-position="{{false}}" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','password']]]]]}}" value="{{loginForm.password}}" bindinput="__e"/></block><block wx:if="{{pwdType}}"><view data-event-opts="{{[['tap',[['handleInputTypeChange',['pwd']]]]]}}" class="iconfont icon-yanjing_xianshi _i" bindtap="__e"></view></block><block wx:else><view data-event-opts="{{[['tap',[['handleInputTypeChange',['pwd']]]]]}}" class="iconfont icon-yanjing_yincang _i" bindtap="__e"></view></block></view><view class="form-item"><image class="form-icon" src="../../static/images/login/lock-icon.png"></image><block wx:if="{{newPwdType}}"><input type="password" placeholder="确认密码" placeholder-style="color:#ccc" maxlength="20" adjust-position="{{false}}" data-event-opts="{{[['input',[['setInputValue',['$event','confirmPassword']]]]]}}" value="{{loginForm.confirmPassword}}" bindinput="__e"/></block><block wx:else><input type="text" placeholder="确认密码" placeholder-style="color:#ccc" adjust-position="{{false}}" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','confirmPassword']]]]]}}" value="{{loginForm.confirmPassword}}" bindinput="__e"/></block><block wx:if="{{newPwdType}}"><view data-event-opts="{{[['tap',[['handleInputTypeChange',['newPwd']]]]]}}" class="iconfont icon-yanjing_xianshi _i" bindtap="__e"></view></block><block wx:else><view data-event-opts="{{[['tap',[['handleInputTypeChange',['newPwd']]]]]}}" class="iconfont icon-yanjing_yincang _i" bindtap="__e"></view></block></view><view class="bottom-btn-box"><view data-event-opts="{{[['tap',[['resetPwdSubmit',['$event']]]]]}}" class="main-btn" bindtap="__e">重置密码</view></view></view><view class="bottom_tips">您正在进行密码重置，若您未注册小程序，密码重置成功后，将自动为您进进行注册</view><footer style="width:100%;" vue-id="2724521f-2" bind:__l="__l"></footer></view>