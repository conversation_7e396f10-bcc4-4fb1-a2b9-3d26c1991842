.login-wrapper {
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  overflow: scroll;
}
.register-btn {
  font-size: 15px;
  color: #0052d9;
  margin-bottom: 25px;
  text-align: right;
  width: 100%;
}
.logo {
  width: 240px;
  height: 51px;
  display: flex;
  align-items: center;
  margin-bottom: 72px;
}
.login-form {
  width: 100%;
  padding: 22px 26px;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.login-form .placeholder-class {
  color: #CCCCCC;
}
.login-form .form-item {
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #CCCCCC;
  padding-bottom: 15px;
  margin-bottom: 35px;
  color: #3D3E4F;
  position: relative;
}
.login-form .form-item .form-icon {
  width: 24px !important;
  height: 24px !important;
}
.login-form .form-item .verifiy-code {
  width: 100px;
  height: 40px;
  background: #d9d9d9;
  margin-left: 10px;
}
.login-form .form-item input {
  flex: 1;
  margin: 0 10px;
  padding: 0;
}
.login-form .form-info {
  width: 100%;
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
}
.login-form .form-info ._span {
  color: #0052d9;
  display: inline-block;
  cursor: pointer;
}
.bottom-btn-box {
  width: 100%;
}
.bottom_tips {
  margin-top: 40px;
  text-align: center;
  width: 100%;
  color: #909399;
  font-size: 12px;
}
.bottom-info {
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  font-size: 11px;
  color: #000;
  margin-top: 10px;
  padding: 0 20px;
  line-height: 20px;
  margin-bottom: 25px;
}
.bottom-info ._span {
  color: #0052d9;
  display: inline-block;
  cursor: pointer;
}
.bottom-info checkbox .wx-checkbox-input {
  -webkit-transform: scale(0.6);
          transform: scale(0.6);
  color: #1C6CED;
  overflow: hidden;
  border-radius: 50% !important;
  margin-right: 2px;
  margin-top: -2px;
}
.bottom-info checkbox:before {
  top: 42% !important;
  -webkit-transform: scale(0.6);
          transform: scale(0.6);
  right: 7px;
}

