import { defineStore } from 'pinia'
import { login } from '@/api/login';
import { Md5 } from 'ts-md5';
export const useUserStore = defineStore({
    // id: 必须的，在所有 Store 中唯一
    id: 'userState',
    // state: 返回对象的函数
    state: () => ({
        // 登录token
        token: null,
        // 登录用户信息
        userInfo: {},
        // 角色
        roles: localStorage.roles ? JSON.parse(localStorage.roles) : []

    }),
    getters: {},
    // 可以同步 也可以异步
    actions: {
        // 登录
        // login(userInfo) {
        //     const { username, password } = userInfo
        //     return new Promise(async (resolve, reject) => {
        //         this.token = username
        //         this.userInfo = userInfo
        //         await this.getRoles()
        //         resolve(username)
        //     })
        // },
        // 登录
        login(userInfo) {
            console.log(userInfo, 'userInfo')
            const md5: any = new Md5()
            md5.appendAsciiStr(userInfo.password)
            const password = md5.end()

            const obj = {
                account: userInfo.username,
                password: password,
                code: userInfo.code,
                uuid: userInfo.uuid,
                // sysCode: userInfo.sysCode
            }
            return new Promise((resolve, reject) => {
                login(obj).then(async res => {
                    console.log('res', res);

                    // (vuex、sessionStorage)存储所以的返回信息（用户、角色、菜单权限、token）

                    if (res.data.code == 200) {
                        this.token = res.data.data.access_token
                        this.userInfo = res.data.data.loginUser
                        await this.getRoles()
                        resolve(res)
                        return
                        // 处理登录用户返回结构
                        let userInfo = res.data.loginUser
                        let sysUser = res.data.loginUser.sysUser
                        for (var key in sysUser) {
                            if (key != "roles") {
                                userInfo[key] = sysUser[key]
                            }
                        }
                        userInfo.roles = userInfo.roles || []
                        userInfo.pers = userInfo.permissions || []
                        if (userInfo.isAdmin) {
                            userInfo.pers.push("admin")
                            userInfo.roles.push("admin")
                        }
                        delete userInfo.permissions
                        delete userInfo.sysUser
                        delete userInfo.password

                        userInfo.reset = res.data.reset || 0
                        userInfo.token = res.data.access_token
                        userInfo.expires_in = res.data.expires_in
                        // commit('SET_USERINFO', userInfo)
                        // setToken(res.data.access_token)
                        // commit('SET_TOKEN', res.data.access_token)
                    }
                    resolve(res)
                }).catch(error => {
                    reject(error)
                })
            })
        },
        // 获取用户授权角色信息，实际应用中 可以通过token通过请求接口在这里获取用户信息
        getRoles() {
            return new Promise((resolve, reject) => {
                // 获取权限列表 默认就是超级管理员，因为没有进行接口请求 写死
                this.roles = ['admin']
                localStorage.roles = JSON.stringify(this.roles)
                resolve(this.roles)
            })
        },
        // 获取用户信息 ，如实际应用中 可以通过token通过请求接口在这里获取用户信息
        getInfo(roles) {
            return new Promise((resolve, reject) => {
                this.roles = roles
                resolve(roles)
            })
        },
        // 退出
        logout() {
            return new Promise((resolve, reject) => {
                this.token = null
                this.userInfo = {}
                this.roles = []
                resolve(null)
            })
        },

    },
    // 进行持久化存储
    persist: {
        // 本地存储的名称
        key: "userState",
        //保存的位置
        storage: window.localStorage,//localstorage
    },

})
