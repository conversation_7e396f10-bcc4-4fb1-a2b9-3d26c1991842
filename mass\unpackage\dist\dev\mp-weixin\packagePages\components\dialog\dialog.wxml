<view class="mask-box"><view class="zl-modal"><view data-event-opts="{{[['tap',[['handleCancel',['$event']]]]]}}" class="iconfont icon-close" bindtap="__e"></view><view class="dialog-content"><block wx:if="{{modalData.title}}"><view class="dialog-title">{{modalData.title}}</view></block><block wx:if="{{modalData.info}}"><view class="dialog-info">{{modalData.info+''}}<block wx:if="{{modalData.info1}}"><label data-event-opts="{{[['tap',[['goAgreement',['service']]]]]}}" class="dialog-info2 _span" bindtap="__e">{{modalData.info1}}</label></block><block wx:if="{{modalData.info2}}"><label data-event-opts="{{[['tap',[['goAgreement',['policy']]]]]}}" class="dialog-info2 _span" bindtap="__e">{{modalData.info2}}</label></block>？</view></block><block wx:if="{{false}}"><view><input type="text" placeholder="{{item.placeholder}}" data-event-opts="{{[['input',[['setInputValue',['$event','$0'],['index']]]]]}}" value="{{item.value}}" bindinput="__e"/></view></block><block wx:if="{{modalData.btnTxt}}"><button data-event-opts="{{[['tap',[['handleBtnClick',['$event']]]]]}}" class="main-btn" bindtap="__e">{{modalData.btnTxt}}</button></block></view><block wx:if="{{btnVisible}}"><view class="dialog-footer"><view data-event-opts="{{[['tap',[['handleCancel',['$event']]]]]}}" bindtap="__e">{{modalData.cancelTxt}}</view><view data-event-opts="{{[['tap',[['handleConfirm',['$event']]]]]}}" bindtap="__e">{{modalData.confirmTxt}}</view></view></block></view></view>