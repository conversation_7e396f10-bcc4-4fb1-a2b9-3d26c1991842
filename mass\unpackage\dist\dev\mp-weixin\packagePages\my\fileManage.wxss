
page {
	background-color: #F5F6F7 !important;
}

.main-box.data-v-deb8c8be {
  padding: 16px;
}
.switch-box.data-v-deb8c8be {
  position: relative;
}
.switch-box .mask-btn.data-v-deb8c8be {
  position: absolute;
  display: inline-block;
  width: 45px;
  height: 25px;
  z-index: 999;
}
.list-box .list-item-box.data-v-deb8c8be {
  background-color: #fff;
  border-radius: 12px;
}
.list-box .list-item-box .list-info-box.data-v-deb8c8be {
  font-size: 20px;
  font-weight: 400;
  text-align: center;
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.list-box .list-item-box .list-info-box .bar-code.data-v-deb8c8be {
  height: 130px;
  margin-top: 15px;
  margin-left: 50px;
}
.list-box .list-item-box .list-info-box .auth-code.data-v-deb8c8be {
  font-size: 60px;
  font-weight: 860;
  color: #1c6ced;
}
.list-box .list-item.data-v-deb8c8be {
  display: flex;
  align-items: center;
  margin-bottom: 17px;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
}
.list-box .list-item.data-v-deb8c8be:last-child {
  border: 0;
}
.list-box .list-item image.data-v-deb8c8be {
  width: 24px;
  height: 24px;
}
.list-box .list-cell.data-v-deb8c8be {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 400;
  color: #0D0B22;
  margin-left: 12px;
}
.list-box .list-cell .list-cell-label.data-v-deb8c8be {
  padding: 1px 5px;
  font-size: 14px;
  color: #FFFFFF;
  background: #D54941;
  border-radius: 16px;
  margin-left: 10px;
}
.list-box .list-cell .icon-shuaxin.data-v-deb8c8be {
  font-size: 12px;
  color: #1C6CED;
}
.list-box .list-cell .text-right.data-v-deb8c8be {
  font-size: 12px;
  color: #666;
  opacity: 0.9;
}
.list-box .list-cell .text-right ._span.data-v-deb8c8be {
  color: #1C6CED;
}
.list-box .list-cell .text-right button.data-v-deb8c8be {
  font-size: 14px !important;
  height: 28px;
  line-height: 28px;
  border-radius: 6px;
  background-color: #F2F3FF;
  color: #1C6CED;
  font-weight: 600;
}
.bottom-tips.data-v-deb8c8be {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 20px;
  line-height: 24px;
  padding: 30px;
  padding-top: 0;
}
.bottom-tips view.data-v-deb8c8be:first-child {
  font-weight: bold;
}

