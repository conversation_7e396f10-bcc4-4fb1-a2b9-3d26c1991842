
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}
  
/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg =
    /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === 'string' || str instanceof String) {
    return true
  }
  return false
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}


/**
 * 手机号码
 * @param val 当前值字符串
 * @returns 返回 true: 手机号码正确
 */
export function verifyPhone(val: string) {
  // false: 手机号码不正确
  if (!/^((12[0-9])|(13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0|1,5-9]))\d{8}$/.test(val)) return false;
  // true: 手机号码正确
  else return true;
}


/**
 * 匹配文字变色（搜索时）
 * @param val 当前值字符串
 * @param text 要处理的字符串值
 * @param color 搜索到时字体高亮颜色
 * @returns 返回处理后的字符串
 */
export function verifyTextColor(val: string, text = '', color = 'red') {
  // 返回内容，添加颜色
  let v = text.replace(new RegExp(val, 'gi'), `<span style='color: ${color}'>${val}</span>`);
  // 返回结果
  return v;
}

/**
 * 身份证号, 支持1/2代(15位/18位数字)
 * @param val 当前值字符串
 * @returns 返回 true: 身份证正确
 */
export function verifyIdCard(val:string) {
  let regx = /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/
  return regx.test(val)
}

/**
 * 网址
 * @param val 当前值字符串
 * @returns 返回 true: 网址正确
 */
export function verifyWebsite(val:string) {
  let regx = /^((https?|ftp):\/\/)?([\da-z.-]+)\.([a-z.]{2,6})(\/\w\.-]*)*\/?/
  return regx.test(val)
}


/**
 * 是否html标签
 * @param val 当前值字符串
 * @returns 返回 true: 是否html标签
 */
export function verifyHtml(val:string) {
  let regx = /<(.*)>.*<\/\1>|<(.*) \/>/
  return regx.test(val)
}

/**
 * 日期
 * @param val 当前值字符串
 * @returns 返回 true: 是否日期
 */
export function verifyDate(val:string) {
  let regx = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/;
  return regx.test(val)
}

/**
 * 邮箱
 * @param val 当前值字符串
 * @returns 返回 true: 邮箱是否正确
 */
export function verifyEmail(val:string) {
  let regx = /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
  return regx.test(val)
}



/**
 * 验证校验器函数封装
 * @param verifyPhone 验证函数
 * @param message 提示
 */
export function validatorMethod(verifyPhone:(string)=>boolean,message:string) {
  return (rule, value, callback) => {
    if (!verifyPhone(value)) {
      callback(new Error(message))
    } else {
      callback()
    }
  }
}

/**
 * 判断是否为空
 * @returns {Boolean} 为空返回true
 */
export function validateNull(val) {
  if (Object.prototype.toString.call(val) == '[object Array]') {
    if (val.length == 0) return true
  } else if (Object.prototype.toString.call(val) == '[object Object]') {
    if (JSON.stringify(val) == '{}') return true
  } else {
    if (val == '' || val == null || val == undefined) return true
    return false
  }
  return false
}

/**
 * 密码格式效验 8-16位 字母数字特殊字符组合
 * @param {string} password
 * @returns {Boolean}
 */
export function validPassWord(password) {
  const reg = /^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*)(?=.*[`~!@#$%^&*()_\-+=<>.?:"{}].*).{8,16}$/
  return reg.test(password)
}


/**
 * 手机号座机号正则验证
 */
export function checkphone(s) {
  if (!(/^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[35189]))\d{8}$/.test(s)) && !(/^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/.test(s))) {
    return false
  }
  return true
}
// 手机号正则验证
export function checkTelphone(s) {
  if (!(/^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[13589]))\d{8}$/.test(s))) {
    return false
  }
  return true
}


/**
 * 校验配置属性名
 * @param {string} attributeKey
 * @returns {Boolean}
 */
export function validAttributeKey(attributeKey) {
  const reg = /^[A-Za-z](\.?[A-Za-z0-9])*$/
  return reg.test(attributeKey)
}

// /**
//  * @param {string} email
//  * @returns {Boolean}
//  */
// export function validEmail(email) {
//   const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
//   return reg.test(email)
// }

/**
 * 证件号码校验
 * @returns {Boolean} 符合校验规则返回true
 * 对于18位长度按照身份证强校验，其余限制只能是输入字母或者数字，限制长度
 */
export function checkIdCard(cardType, cardNo) {
  const reg1 = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/
  const reg2 = /^[a-zA-Z0-9]{4,20}$/
  if (cardType == "1") {
    var city = {
      11: '北京',
      12: '天津',
      13: '河北',
      14: '山西',
      15: '内蒙古',
      21: '辽宁',
      22: '吉林',
      23: '黑龙江 ',
      31: '上海',
      32: '江苏',
      33: '浙江',
      34: '安徽',
      35: '福建',
      36: '江西',
      37: '山东',
      41: '河南',
      42: '湖北 ',
      43: '湖南',
      44: '广东',
      45: '广西',
      46: '海南',
      50: '重庆',
      51: '四川',
      52: '贵州',
      53: '云南',
      54: '西藏 ',
      61: '陕西',
      62: '甘肃',
      63: '青海',
      64: '宁夏',
      65: '新疆',
      71: '台湾',
      81: '香港',
      82: '澳门',
      91: '国外 ',
    }
    var row = true
    if (cardNo.length != 18 && cardNo.length != 15) {
      return false
    }
    if (
      !cardNo ||
      !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/.test(
        cardNo
      )
    ) {
      row = false
    } else if (!city[cardNo.substr(0, 2)]) {
      row = false
    } else {
      if (cardNo.length == 18) {
        cardNo = cardNo.split('')
        //加权因子
        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        //校验位
        var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
        var sum = 0
        var ai = 0
        var wi = 0
        for (var i = 0; i < 17; i++) {
          ai = cardNo[i]
          wi = factor[i]
          sum += ai * wi
        }
        if (parity[sum % 11] != cardNo[17].toUpperCase()) {
          row = false
        }
      }
    }
    return row
  } else {
    let row = reg2.test(cardNo)
    return row
  }
}

/**
 * 姓名校验
 * @returns {Boolean} 符合校验规则返回true
 * 中文中间不允许出现空格，英文中间只能有一个空格
 */
export function checkName(name) {
  const reg = /^(?:[\u4e00-\u9fa5]+)(?:·[\u4e00-\u9fa5]+)*$|^[a-zA-Z0-9]+\s?[\.·\-()a-zA-Z]*[a-zA-Z]+$/
  return reg.test(name.trim())
}

// 根据身份证号获取生日、性别、年龄
export function IdCard(IdCard, type) {
  if (type === 1) {
    let birthday = IdCard.substring(6, 10) + "-" + IdCard.substring(10, 12) + "-" + IdCard.substring(12, 14)
    return birthday
  }
  if (type === 2) {
    //获取性别
    if (parseInt(IdCard.substr(16, 1)) % 2 === 1) {
      return '1' // 男
    } else {
      return '0' // 女
    }
  }
  if (type === 3) {
    //获取年龄
    var ageDate = new Date()
    var month = ageDate.getMonth() + 1
    var day = ageDate.getDate()
    var age = ageDate.getFullYear() - IdCard.substring(6, 10) - 1
    if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) === month && IdCard.substring(12, 14) <= day) {
      age++
    }
    if (age <= 0) {
      age = 1
    }
    return age
  }
}

/**
 * 统一社会信用代码校验
 * @returns {Boolean} 符合校验规则返回true
 
 */
export function creditCode(code) {
  const reg = /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/
  return reg.test(code.trim())
}
