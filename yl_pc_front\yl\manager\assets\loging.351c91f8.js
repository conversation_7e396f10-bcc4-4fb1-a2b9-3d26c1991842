import{_ as n}from"./2dl.e9c516c3.js";import{u as s,_}from"./index.dc283410.js";const c=Vue.defineComponent({__name:"loging",setup(u){const o=s(),t=()=>{o.push("/mk")};return(l,e)=>(Vue.openBlock(),Vue.createElementBlock("div",{class:"login-container",onClick:t},e[0]||(e[0]=[Vue.createElementVNode("img",{src:n,alt:"\u767B\u5F55\u9875",class:"login-image"},null,-1)])))}});const i=_(c,[["__scopeId","data-v-cc30f406"]]);export{i as default};
//# sourceMappingURL=loging.351c91f8.js.map
