<view class="data-v-99f81d14"><zlnavbar vue-id="a50a8360-1" isBack="{{true}}" class="data-v-99f81d14" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" class="data-v-99f81d14">{{serviceSData.name}}</view></zlnavbar><scroll-menu vue-id="a50a8360-2" menuData="{{scrollMenu}}" data-event-opts="{{[['^menuIndex',[['handleTabMenuChange']]]]}}" bind:menuIndex="__e" class="data-v-99f81d14" bind:__l="__l"></scroll-menu><view class="main-box data-v-99f81d14"><view class="list-box data-v-99f81d14"><view class="list-item-box data-v-99f81d14"><view class="list-item data-v-99f81d14" style="padding:16px;"><view class="list-cell data-v-99f81d14"><view class="list-lable list-label-big data-v-99f81d14">{{''+(serviceSData.key=='homeDoctor'?'签约日期：':'')+detailDate}}<label class="_span data-v-99f81d14"></label></view><view class="flex text-right data-v-99f81d14"></view></view></view></view><view class="list-item-box data-v-99f81d14"><block wx:if="{{serviceDataDetail.arrData}}"><view class="list-item-box data-v-99f81d14"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item data-v-99f81d14"><block wx:if="{{item.$orig.value}}"><view data-event-opts="{{[['longpress',[['handleLongPress',['$0'],[[['serviceDataDetail.arrData','',index]]]]]]]}}" class="list-cell data-v-99f81d14" bindlongpress="__e"><view class="list-lable data-v-99f81d14">{{item.$orig.label+''}}</view><view class="text-right data-v-99f81d14">{{item.$orig.value}}</view></view></block><block wx:else><block wx:if="{{item.$orig.label==''}}"><block wx:for="{{item.l0}}" wx:for-item="collapseItem" wx:for-index="collapseIndex" wx:key="collapseIndex"><view class="no-collapse-content-box data-v-99f81d14"><block wx:for="{{collapseItem}}" wx:for-item="sitem" wx:for-index="index" wx:key="value"><block class="data-v-99f81d14"><view data-event-opts="{{[['tap',[['e0',['$event']]]],['longpress',[['e1',['$event']]]]]}}" data-event-params="{{({sitem,collapseItem,sitem,collapseItem})}}" class="{{['data-v-99f81d14','list-item',sitem.type=='detail'&&'list-item2']}}" bindtap="__e" bindlongpress="__e"><view class="{{['data-v-99f81d14','list-cell',sitem.type!='detail'&&'sub-list-cell']}}"><view class="list-lable data-v-99f81d14">{{sitem.label+''}}</view><view class="{{['data-v-99f81d14','text-right',sitem.type=='detail'&&'text-right-btn']}}">{{''+sitem.value+''}}</view></view></view><block wx:if="{{sitem.type=='desc'}}"><view data-event-opts="{{[['longpress',[['e2',['$event']]]]]}}" data-event-params="{{({sitem})}}" class="list-item collapse-content-desc data-v-99f81d14" bindlongpress="__e"><view class="list-cell sub-list-cell data-v-99f81d14"><view class="list-lable data-v-99f81d14">{{sitem.label+''}}</view></view><view class="text-left data-v-99f81d14">{{sitem.value}}</view></view></block></block></block></view></block></block><block wx:else><block class="data-v-99f81d14"><view class="collapse-item data-v-99f81d14"><view data-event-opts="{{[['tap',[['handleChangePanel',['$0'],[[['serviceDataDetail.arrData','',index]]]]]]]}}" class="list-cell data-v-99f81d14" catchtap="__e"><view class="list-lable data-v-99f81d14">{{item.$orig.label+''}}</view><view class="{{['iconfont','icon-arrow-right','arrow-icon','_i','data-v-99f81d14',(item.$orig.isOpen)?'collapse-item-arrow-active':'',(item.$orig.isOpen)?'collapse-item--animation':'']}}"></view></view><view hidden="{{!(item.$orig.isOpen)}}" class="data-v-99f81d14"><block wx:for="{{item.l1}}" wx:for-item="collapseItem" wx:for-index="collapseIndex" wx:key="collapseIndex"><view class="{{['collapse-content','data-v-99f81d14',(item.$orig.isOpen)?'is--transition':'']}}"><block wx:for="{{collapseItem}}" wx:for-item="collapsesItem" wx:for-index="collapsesIndex" wx:key="collapsesIndex"><view class="list-item data-v-99f81d14"><block wx:if="{{collapsesItem.type!='desc'}}"><view data-event-opts="{{[['longpress',[['e3',['$event']]]]]}}" data-event-params="{{({collapseItem})}}" class="list-cell sub-list-cell data-v-99f81d14" bindlongpress="__e"><view class="list-lable data-v-99f81d14">{{collapsesItem.label+''}}</view><view class="text-right data-v-99f81d14">{{collapsesItem.value||''}}</view></view></block><block wx:else><view data-event-opts="{{[['longpress',[['e4',['$event']]]]]}}" data-event-params="{{({collapseItem})}}" class="collapse-content-desc data-v-99f81d14" bindlongpress="__e"><view class="list-cell sub-list-cell data-v-99f81d14"><view class="list-lable data-v-99f81d14">{{''+collapsesItem.label+''}}</view></view><view class="text-left data-v-99f81d14">{{collapsesItem.value}}</view></view></block></view></block></view></block></view></view></block></block></block></view></block></view></block></view><block wx:if="{{$root.g0}}"><view class="hint-label data-v-99f81d14"><label class="iconfont icon-tishi _span data-v-99f81d14"></label>错误数据反馈：如果您的健康档案信息有误，请长按错误数据可以进行错误反馈。</view></block><block wx:if="{{collapseData}}"><view class="list-item-box data-v-99f81d14"><scroll-view class="tabs data-v-99f81d14" scroll-x="true" scroll-with-animation="{{true}}" scroll-left="{{scrollLeft}}"><block wx:if="{{$root.g1>0}}"><view class="tabs-scroll data-v-99f81d14"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" data-event-params="{{({index,item})}}" class="{{['tabs-scroll_item','data-v-99f81d14',(tabCur==index)?'tab-active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view></block></scroll-view><block wx:if="{{$root.g2}}"><view class="list-content-item data-v-99f81d14"><block wx:for="{{$root.l6}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item list-item-no-collapse data-v-99f81d14"><block wx:if="{{item.$orig.value&&item.$orig.type!='desc'}}"><view data-event-opts="{{[['longpress',[['handleLongPress',['$0'],[[['collapseData','',index]]]]]]]}}" class="list-cell data-v-99f81d14" bindlongpress="__e"><view class="list-lable data-v-99f81d14">{{item.$orig.label+'\n\t\t\t\t\t\t\t\tAAA7'}}</view><view class="text-right data-v-99f81d14">{{item.$orig.value}}</view></view></block><block wx:else><block wx:if="{{item.$orig.type=='desc'}}"><view data-event-opts="{{[['longpress',[['handleLongPress',['$0'],[[['collapseData','',index]]]]]]]}}" bindlongpress="__e" class="data-v-99f81d14"><view class="list-cell data-v-99f81d14"><view class="list-lable data-v-99f81d14">{{item.$orig.label+'\n\t\t\t\t\t\t\t\t\tAAA8'}}</view></view><view class="text-left data-v-99f81d14">{{item.$orig.value}}</view></view></block><block wx:else><block wx:if="{{item.$orig.label==''}}"><block wx:for="{{item.l4}}" wx:for-item="collapseItem" wx:for-index="collapseIndex" wx:key="collapseIndex"><view class="no-collapse-content-box data-v-99f81d14"><block wx:for="{{collapseItem}}" wx:for-item="sitem" wx:for-index="index" wx:key="value"><block class="data-v-99f81d14"><block wx:if="{{sitem.type!='desc'}}"><view data-event-opts="{{[['tap',[['e6',['$event']]]],['longpress',[['e7',['$event']]]]]}}" data-event-params="{{({sitem,collapseItem,item:item.$orig,index,collapseIndex,sitem,index,collapseItem,collapseIndex,item:item.$orig})}}" class="{{['data-v-99f81d14','list-item',sitem.type=='detail'&&'list-item2']}}" bindtap="__e" bindlongpress="__e"><view class="{{['data-v-99f81d14','list-cell',sitem.type!='detail'&&'sub-list-cell']}}"><view data-event-opts="{{[['tap',[['e8',['$event']]]]]}}" data-event-params="{{({sitem,collapseItem,item:item.$orig,index})}}" class="list-lableAI data-v-99f81d14" catchtap="__e"><block wx:if="{{sitem.key=='zyjc_query'||sitem.key=='jyjc_query'}}"><image class="ai-icon data-v-99f81d14" src="/static/images/frame.png" mode="aspectFit"></image></block><block wx:if="{{sitem.key=='zyjc_query'||sitem.key=='jyjc_query'}}"><text class="ai-label data-v-99f81d14">AI报告分析</text></block></view><view class="list-lable data-v-99f81d14">{{''+sitem.label+'\n\t\t\t\t\t\t\t\t\t\t\tAAA9'}}</view><view class="{{['data-v-99f81d14','text-right',sitem.type=='detail'&&'text-right-btn']}}">{{''+sitem.value+''}}</view></view></view></block><block wx:else><view data-event-opts="{{[['longpress',[['e9',['$event']]]]]}}" data-event-params="{{({sitem})}}" class="list-item collapse-content-desc data-v-99f81d14" bindlongpress="__e"><view class="list-cell sub-list-cell data-v-99f81d14"><view class="list-lable data-v-99f81d14">{{sitem.label+'\n\t\t\t\t\t\t\t\t\t\t\tAAA10'}}</view></view><view class="text-left data-v-99f81d14">{{sitem.value}}</view></view></block></block></block></view></block></block><block wx:else><block class="data-v-99f81d14"><view class="collapse-item data-v-99f81d14"><view data-event-opts="{{[['tap',[['handleChangePanel',['$event','$0'],[[['collapseData','',index]]]]]]]}}" class="list-cell data-v-99f81d14" catchtap="__e"><view data-event-opts="{{[['longpress',[['handleLongPress',['$0'],[[['collapseData','',index]]]]]]]}}" class="list-lable data-v-99f81d14" bindlongpress="__e">{{''+item.$orig.label+''}}</view><view class="{{['iconfont','icon-arrow-right','arrow-icon','_i','data-v-99f81d14',(item.$orig.isOpen)?'collapse-item-arrow-active':'',(item.$orig.isOpen)?'collapse-item--animation':'']}}"></view></view><view hidden="{{!(item.$orig.isOpen)}}" class="data-v-99f81d14"><block wx:for="{{item.l5}}" wx:for-item="collapseItem" wx:for-index="collapseIndex" wx:key="collapseIndex"><view class="{{['collapse-content','data-v-99f81d14',(item.$orig.isOpen)?'is--transition':'']}}"><block wx:for="{{collapseItem}}" wx:for-item="collapsesItem" wx:for-index="collapsesIndex" wx:key="collapsesIndex"><view class="list-item data-v-99f81d14"><block wx:if="{{collapsesItem.type!='desc'}}"><view data-event-opts="{{[['longpress',[['e10',['$event']]]]]}}" data-event-params="{{({collapsesItem,collapsesIndex,collapseItem,collapseIndex,item:item.$orig,index})}}" class="list-cell sub-list-cell data-v-99f81d14" bindlongpress="__e"><view class="list-lable data-v-99f81d14">{{collapsesItem.label+''}}</view><view class="text-right data-v-99f81d14">{{collapsesItem.value||''}}</view></view></block><block wx:else><view data-event-opts="{{[['longpress',[['e11',['$event']]]]]}}" data-event-params="{{({collapsesItem})}}" class="collapse-content-desc data-v-99f81d14" bindlongpress="__e"><view class="list-cell sub-list-cell data-v-99f81d14"><view class="list-lable data-v-99f81d14">{{collapsesItem.label+''}}</view></view><view class="text-left data-v-99f81d14">{{collapsesItem.value}}</view></view></block></view></block></view></block></view></view></block></block></block></block></view></block></view></block><block wx:if="{{$root.g3}}"><empty-placeholder vue-id="a50a8360-3" class="data-v-99f81d14" bind:__l="__l"></empty-placeholder></block></view></block><block wx:else><block wx:for="{{serviceDataDetail.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item-box data-v-99f81d14"><view class="list-content-item data-v-99f81d14"><block wx:for="{{item}}" wx:for-item="sitem" wx:for-index="index" wx:key="value"><block class="data-v-99f81d14"><block wx:if="{{sitem.type!='desc'}}"><view class="{{['data-v-99f81d14','list-item',sitem.type=='detail'&&'list-item2']}}"><view data-event-opts="{{[['tap',[['e12',['$event']]]],['longpress',[['handleLongPress',['$0'],[[['serviceDataDetail.data','',index],['','value',sitem.value]]]]]]]}}" data-event-params="{{({sitem,item})}}" class="list-cell data-v-99f81d14" bindtap="__e" bindlongpress="__e"><view class="list-lable data-v-99f81d14">{{''+sitem.label+'\n\t\t\t\t\t\t\t\t\t AAA14'}}</view><view class="{{['data-v-99f81d14','text-right',sitem.type=='detail'&&'text-right-btn']}}">{{sitem.value+''}}</view></view></view></block><block wx:else><view class="list-item data-v-99f81d14"><view class="list-cell data-v-99f81d14"><view class="list-lable data-v-99f81d14">{{sitem.label+'\n\t\t\t\t\t\t\t\t\tAAA15'}}</view></view><view class="text-left data-v-99f81d14">{{sitem.value}}</view></view></block></block></block></view></view></block></block></view><block wx:if="{{false}}"><button class="main-btn bottom-btn data-v-99f81d14">错误数据反馈</button></block></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="a50a8360-4" closeVisible="{{true}}" modalData="{{modalData}}" data-event-opts="{{[['^confirm',[['handleConfirm']]],['^cancel',[['handleCancel']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-99f81d14" bind:__l="__l"></modal-dialog></view>