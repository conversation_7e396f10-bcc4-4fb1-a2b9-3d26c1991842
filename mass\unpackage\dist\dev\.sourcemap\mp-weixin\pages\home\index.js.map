{"version": 3, "sources": ["webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/home/<USER>", "uni-app:///pages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/home/<USER>", "webpack:///E:/AnahiWorkWS/WSproject/java/healthView/ehr-web/mass/pages/home/<USER>"], "names": ["components", "scrollMenu", "modalDialog", "data", "menuIndex", "modalVisible", "modalData", "title", "info", "btnTxt", "type", "menuList", "key", "symbol", "icon", "<PERSON><PERSON><PERSON>", "topicCode", "name", "subTitle", "label", "moduleCodes", "userInfo", "familyList", "familyItem", "moduleCode", "tempMenuList", "modalPhoneVisible", "modalPhoneData", "phoneList", "phone", "methods", "checkMsgType", "getUserInfo", "item", "callback", "getFamilyUser", "res", "console", "getFuncState", "fullName", "identityNo", "idCard", "api", "getArchiveId", "sfzjhm", "uni", "handleCancel", "handleConfirm", "url", "checkVerify", "getWxPhone", "handlePhoneCancel", "handlePhoneConfirm", "checkWxLogin", "setAuthorization", "setInfoLogin", "setPhone", "handleTabMenuChange", "handleMenuChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACsM;AACtM,gBAAgB,8MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,yBAAQ,IAAwB;AAC7C;AACA,MAAM,MAAM,CAAC;AACb;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAowB,CAAgB,oxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkCxxB;AAGA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAA;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAT;MACAU;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAC;QACAR;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAC;QAAA;QACAR;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAC;QACAR;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAC;QACAR;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAT;QACAE;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAC;QAAA;QACAR;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAjB;QACAH;QACAC;QACAC;MACA;MACAmB;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UAAA,iEACAP;QAAA;QACA;MACA;IACA;IACAQ;MAAA;MACA;QACA;UACA;UACA;UACAV;UACAA;YACAL;YACAP;UACA;UACAY;YACA;cACAW;YACA;YACA;cACAA;cACAA,yEACA,8BACA;YACA;UACA;UACA;UACA;UACA;YACA;UACA;;UAEA;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACAC;UACAA;YACAnB;YACAP;UACA;UACA0B;YACA;cACAH;YACA;UACA;UACA;UACA;UACAI;QACA;MACA;IACA;IACAC;MAAA;QAAA;QAAA;MACA;QACAC;QACAC,0MACAC;MACA;MACAC;QACA;QACA/B;UACA;UACA;UACA;YACA;YACAsB;YACA;UACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;UACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAU;MAAA;QAAA;MACA;QACAC,sMACAH;MACA;MACAC;QAAA;QACAG;QACA;UACAA;QACA;MACA;IACA;IACAC;MACAD;MACA;IACA;IACAE;MACA;MACAF;QACAG;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;QACA;UACA;QACA;UACA;UACAJ;YACAG,qFACAzB;UACA;QACA;MACA;IACA;IACA2B;MAAA;MACAR;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QAAA;MAGA;IACA;IACAS;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QAAA;QACA;QACA;UACAR;UACAS;UACAC;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAd;QACAb;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA4B;MACA;MACA;MACA;MACA;QACA;QACAZ;UACAG;QACA;MACA;QACA;QACA;MACA;IACA;IACAU;MAAA;MACA;MACAb;MACA;MACA;MACA;MACAA;QACAG;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACjaA;AAAA;AAAA;AAAA;AAA28C,CAAgB,+5CAAG,EAAC,C;;;;;;;;;;;ACA/9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/home/<USER>", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=71e217db&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=71e217db&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"71e217db\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/home/<USER>\"\nexport default component.exports", "export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=71e217db&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.tempMenuList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = require(\"@/static/images/index/\" +\n      (item.type == \"grey\" ? item.iconGrey : item.icon) +\n      \".svg\")\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"wrapper\">\r\n\t\t<zlnavbar :isBack=\"false\">\r\n\t\t\t<block slot=\"content\">健康档案</block>\r\n\t\t</zlnavbar>\r\n\t\t<!-- 滚动菜单 -->\r\n\t\t<scrollMenu :menuData='familyList' :menuIndex=\"menuIndex\" @menuChange='handleTabMenuChange'\r\n\t\t\tv-if=\"familyList[0].name\">\r\n\t\t</scrollMenu>\r\n\t\t<!-- main -->\r\n\t\t<view class=\"main-box\">\r\n\t\t\t<view class=\"menu-title\">档案服务记录</view>\r\n\t\t\t<view class=\"menu-box\">\r\n\t\t\t\t<view :class=\"['menu-item','menu-grey-item']\" v-for=\"(item,index) in tempMenuList\" :key=\"index\"\r\n\t\t\t\t\t@click=\"handleMenuChange(item)\">\r\n\t\t\t\t\t<image :src=\"require(`@/static/images/index/${item.type=='grey'?item.iconGrey:item.icon}.svg`)\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"menu-info\">{{item.label}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<modalDialog :closeVisible='false' :modalData=\"modalData\" @confirm=\"handleConfirm\" v-show=\"modalVisible\">\r\n\t\t</modalDialog>\r\n\t\t<!-- 选择手机号 -->\r\n\t\t<modalDialog :closeVisible='true' :modalData=\"modalPhoneData\" :list='phoneList' @confirm=\"handlePhoneConfirm\"\r\n\t\t\t@cancel='handlePhoneCancel' v-show=\"modalPhoneVisible\">\r\n\t\t</modalDialog>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from \"@/api/api.js\";\r\n\timport scrollMenu from \"@/components/scrollMenu/index.vue\";\r\n\timport modalDialog from \"@/components/dialog/dialog.vue\";\r\n\timport {\r\n\t\tparseTime\r\n\t} from \"@/utils/util\";\r\n\timport {\r\n\t\tloginFn,\r\n\t\tgetPhoneNumberFn\r\n\t} from '@/api/login.js'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tscrollMenu,\r\n\t\t\tmodalDialog\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuIndex: 0,\r\n\t\t\t\tmodalVisible: false,\r\n\t\t\t\tmodalData: {\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tinfo: '为保障您的个人隐私及数据安全，确保所有操作为本人操作，请先进行人脸核身，验证通过后方可查看健康档案信息。',\r\n\t\t\t\t\tbtnTxt: '人脸识别认证'\r\n\t\t\t\t},\r\n\t\t\t\ttype: 'grey',\r\n\t\t\t\tscrollMenu: [],\r\n\t\t\t\tmenuList: [{\r\n\t\t\t\t\t\tkey: 'fileSummary',\r\n\t\t\t\t\t\tsymbol: 'dazy',\r\n\t\t\t\t\t\ticon: 'menu-icon0',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon0',\r\n\t\t\t\t\t\ttopicCode: '01',\r\n\t\t\t\t\t\tname: '档案摘要',\r\n\t\t\t\t\t\tsubTitle: '健康档案摘要',\r\n\t\t\t\t\t\tlabel: '健康档案基本信息'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmoduleCodes: [340],\r\n\t\t\t\t\t\tkey: 'vaccination',\r\n\t\t\t\t\t\tsymbol: 'yfjz',\r\n\t\t\t\t\t\ticon: 'menu-icon1',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon1',\r\n\t\t\t\t\t\ttopicCode: '02',\r\n\t\t\t\t\t\tname: '预防接种',\r\n\t\t\t\t\t\tsubTitle: '预防接种服务',\r\n\t\t\t\t\t\tlabel: '疫苗接种信息查询'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: 'childManage',\r\n\t\t\t\t\t\tsymbol: 'etjkgl',\r\n\t\t\t\t\t\ticon: 'menu-icon2',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon2',\r\n\t\t\t\t\t\ttopicCode: '03',\r\n\t\t\t\t\t\tname: '儿童健康管理',\r\n\t\t\t\t\t\tsubTitle: '儿童健康管理',\r\n\t\t\t\t\t\tlabel: '0-6岁儿童管理'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmoduleCodes: [331, 332, 333, 334, 335], //产前检查,分娩记录,新生儿记录,产后访视,产后42天随访\r\n\t\t\t\t\t\tkey: 'pregnantWoman',\r\n\t\t\t\t\t\tsymbol: 'ycf',\r\n\t\t\t\t\t\ticon: 'menu-icon3',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon3',\r\n\t\t\t\t\t\ttopicCode: '04',\r\n\t\t\t\t\t\tname: '孕产妇',\r\n\t\t\t\t\t\tsubTitle: '孕产妇健康管理服务',\r\n\t\t\t\t\t\tlabel: '全产程健康管理'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: 'elderly',\r\n\t\t\t\t\t\tsymbol: 'lnr',\r\n\t\t\t\t\t\ticon: 'menu-icon4',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon4',\r\n\t\t\t\t\t\ttopicCode: '05',\r\n\t\t\t\t\t\tname: '老年人',\r\n\t\t\t\t\t\tsubTitle: '老年人健康管理服务',\r\n\t\t\t\t\t\tlabel: '体检和健康管理'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmoduleCodes: [311],\r\n\t\t\t\t\t\tkey: 'hypertension',\r\n\t\t\t\t\t\tsymbol: 'gxy',\r\n\t\t\t\t\t\ticon: 'menu-icon5',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon5',\r\n\t\t\t\t\t\ttopicCode: '06',\r\n\t\t\t\t\t\tname: '高血压',\r\n\t\t\t\t\t\tsubTitle: '高血压患者健康管理服务',\r\n\t\t\t\t\t\tlabel: '监测 随访 体检'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmoduleCodes: [312],\r\n\t\t\t\t\t\tkey: 'diabetes',\r\n\t\t\t\t\t\tsymbol: 'tnb',\r\n\t\t\t\t\t\ticon: 'menu-icon6',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon6',\r\n\t\t\t\t\t\ttopicCode: '07',\r\n\t\t\t\t\t\tname: 'Ⅱ型糖尿病',\r\n\t\t\t\t\t\tsubTitle: 'Ⅱ型糖尿病患者健康管理服务',\r\n\t\t\t\t\t\tlabel: '监测 随访 体检'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: '',\r\n\t\t\t\t\t\tkey: 'tuberculosis',\r\n\t\t\t\t\t\tsymbol: 'fjh',\r\n\t\t\t\t\t\ticon: 'menu-icon7',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon7',\r\n\t\t\t\t\t\ttopicCode: '08',\r\n\t\t\t\t\t\tname: '肺结核',\r\n\t\t\t\t\t\tsubTitle: '肺结核患者健康管理服务',\r\n\t\t\t\t\t\tlabel: '监测 随访 体检'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmoduleCodes: [361, 362, 363, 364], //老年人中医健康管理,儿童中医健康管理,高血压中医健康管理,糖尿病中医健康管理\r\n\t\t\t\t\t\tkey: 'chineseMedicine',\r\n\t\t\t\t\t\tsymbol: 'zyy',\r\n\t\t\t\t\t\ticon: 'menu-icon8',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon8',\r\n\t\t\t\t\t\ttopicCode: '09',\r\n\t\t\t\t\t\tname: '中医药',\r\n\t\t\t\t\t\tsubTitle: '中医药健康管理服务',\r\n\t\t\t\t\t\tlabel: '中医药健康服务'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: 'homeDoctor',\r\n\t\t\t\t\t\tsymbol: 'jyqy',\r\n\t\t\t\t\t\ticon: 'menu-icon9',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon9',\r\n\t\t\t\t\t\ttopicCode: '10',\r\n\t\t\t\t\t\tname: '家医签约',\r\n\t\t\t\t\t\tsubTitle: '家医签约',\r\n\t\t\t\t\t\tlabel: '家医服务记录查询'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: 'physicalExamination',\r\n\t\t\t\t\t\tsymbol: 'tjgl',\r\n\t\t\t\t\t\ticon: 'menu-icon10',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon10',\r\n\t\t\t\t\t\ttopicCode: '11',\r\n\t\t\t\t\t\tname: '体检管理',\r\n\t\t\t\t\t\tsubTitle: '体检管理',\r\n\t\t\t\t\t\tlabel: '健康体检记录'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: 'outpatientInfo',\r\n\t\t\t\t\t\tsymbol: 'mzxx',\r\n\t\t\t\t\t\ticon: 'menu-icon11',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon11',\r\n\t\t\t\t\t\ttopicCode: '12',\r\n\t\t\t\t\t\tname: '门诊信息',\r\n\t\t\t\t\t\tsubTitle: '门诊信息',\r\n\t\t\t\t\t\tlabel: '就诊记录查询'\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tkey: 'hospitalInfor',\r\n\t\t\t\t\t\tsymbol: 'zyxx',\r\n\t\t\t\t\t\ticon: 'menu-icon12',\r\n\t\t\t\t\t\ticonGrey: 'menu-grey-icon12',\r\n\t\t\t\t\t\ttopicCode: '13',\r\n\t\t\t\t\t\tname: '住院信息',\r\n\t\t\t\t\t\tsubTitle: '住院信息',\r\n\t\t\t\t\t\tlabel: '住院记录查询'\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\tuserInfo: null,\r\n\t\t\t\tfamilyList: [],\r\n\t\t\t\tfamilyItem: null,\r\n\t\t\t\tmoduleCode: '',\r\n\t\t\t\ttempMenuList: [],\r\n\t\t\t\tmodalPhoneVisible: false,\r\n\t\t\t\tmodalPhoneData: {\r\n\t\t\t\t\ttype: 'list',\r\n\t\t\t\t\ttitle: '选择常用手机号',\r\n\t\t\t\t\tinfo: '',\r\n\t\t\t\t\tbtnTxt: '提交',\r\n\t\t\t\t},\r\n\t\t\t\tphoneList: ['18710646315', '345'],\r\n\t\t\t\tphone: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tcheckMsgType() {\r\n\t\t\t\tif (this.moduleCode) {\r\n\t\t\t\t\tvar menuItem = this.menuList.filter(item => item.moduleCodes && item.moduleCodes.indexOf(Number(this\r\n\t\t\t\t\t\t.moduleCode)) > -1)\r\n\t\t\t\t\tthis.handleMenuChange(menuItem[0], this.moduleCode)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetUserInfo(callback) {\r\n\t\t\t\tthis.$store.dispatch('user/storeSetUserInfo').then((res) => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.userInfo = res.data\t\r\n\t\t\t\t\t\tvar familyList = JSON.parse(JSON.stringify(res.data.familyList)) || []\r\n\t\t\t\t\t\tfamilyList.unshift(this.userInfo)\r\n\t\t\t\t\t\tfamilyList.push({\r\n\t\t\t\t\t\t\tname: '+',\r\n\t\t\t\t\t\t\ttype: 'button'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tfamilyList.map(item => {\r\n\t\t\t\t\t\t\tif (item.nameDecrypt) {\r\n\t\t\t\t\t\t\t\titem.name = '本人'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (!item.type && !item.nameDecrypt && res.data.isRealAttestation) {\r\n\t\t\t\t\t\t\t\titem.fullName = JSON.parse(JSON.stringify(item.name))\r\n\t\t\t\t\t\t\t\titem.name = item.name.replace(item.name.substring(item.name.length > 2 ?\r\n\t\t\t\t\t\t\t\t\t\t1 : 2, item.name.length - 1),\r\n\t\t\t\t\t\t\t\t\t\"*\")\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.familyList = familyList\r\n\t\t\t\t\t\tthis.familyItem = this.familyList[this.menuIndex]\r\n\t\t\t\t\t\tif (this.userInfo.idCard) {\r\n\t\t\t\t\t\t\tthis.getArchiveId()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// res.data.familyList_all = familyList\r\n\t\t\t\t\t\tcallback(res.data)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetFamilyUser() {\r\n\t\t\t\tthis.$store.dispatch('user/storeFamilyUserInfo').then((res) => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tres.data.unshift(this.userInfo)\r\n\t\t\t\t\t\tres.data.push({\r\n\t\t\t\t\t\t\tname: '+',\r\n\t\t\t\t\t\t\ttype: 'button'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tres.data.map(item => {\r\n\t\t\t\t\t\t\tif (item.nameDecrypt) {\r\n\t\t\t\t\t\t\t\titem.name = item.realName\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.familyList = res.data\r\n\t\t\t\t\t\tthis.familyItem = this.familyList[this.menuIndex]\r\n\t\t\t\t\t\tconsole.log(this.familyItem, 'familyItem===')\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetFuncState() {\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tfullName: this.familyList[this.menuIndex]?.nameDecrypt || this.familyList[this.menuIndex].fullName,\r\n\t\t\t\t\tidentityNo: this.familyList[this.menuIndex]?.idCardDecrypt || this.familyList[this.menuIndex]\r\n\t\t\t\t\t\t.idCard,\r\n\t\t\t\t}\r\n\t\t\t\tapi.fetchFuncState(params).then(res => {\r\n\t\t\t\t\tvar menuList = JSON.parse(JSON.stringify(this.menuList))\r\n\t\t\t\t\tmenuList.map((item, index) => {\r\n\t\t\t\t\t\t// console.log('item', item)\r\n\t\t\t\t\t\t// console.log('res.data[item.symbol]',!res.data[item.symbol], item.symbol, )\r\n\t\t\t\t\t\tif (!res.data[item.symbol]) {\r\n\t\t\t\t\t\t\tlet temp = [\"pregnantWoman\", \"childManage\", \"elderly\", \"vaccination\"]\r\n\t\t\t\t\t\t\titem.del = temp.indexOf(item.key) > -1 ? true : false\r\n\t\t\t\t\t\t\treturn item.type = 'grey'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 【开发模式】\r\n\t\t\t\t\t// this.tempMenuList = menuList\r\n\t\t\t\t\tthis.tempMenuList = menuList.filter((item) => {\r\n\t\t\t\t\t\treturn !item.del\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\t\t//处理健康通辽跳转到通辽健康档案对接的需求\r\n\t\t\t\t\tif(uni.getStorageSync('referrerInfo') && uni.getStorageSync('referrerInfo').from == 'jktl' && uni.getStorageSync('referrerInfo').symbol){\t\r\n\t\t\t\t\t\tthis.tempMenuList.forEach(item => {\r\n\t\t\t\t\t\t\tif(item.symbol == uni.getStorageSync('referrerInfo').symbol){\r\n\t\t\t\t\t\t\t\tthis.handleMenuChange(item)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetArchiveId() {\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tsfzjhm: this.familyList[this.menuIndex]?.idCardDecrypt || this.familyList[this.menuIndex]\r\n\t\t\t\t\t\t.idCard,\r\n\t\t\t\t}\r\n\t\t\t\tapi.fetchArchiveId(params).then((res) => {\r\n\t\t\t\t\tuni.setStorageSync('archiveId', res.data)\r\n\t\t\t\t\tif (this.familyList[this.menuIndex]?.nameDecrypt) {\r\n\t\t\t\t\t\tuni.setStorageSync('curUserArchiveId', res.data)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandleCancel() {\r\n\t\t\t\tuni.setStorageSync('isVerify', false)\r\n\t\t\t\tthis.modalVisible = false\r\n\t\t\t},\r\n\t\t\thandleConfirm() {\r\n\t\t\t\tthis.modalVisible = false;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/packagePages/login/registerSuccess?type=1'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 判断是否实名\r\n\t\t\tcheckVerify() {\r\n\t\t\t\t// 未认证\r\n\t\t\t\tif (!uni.getStorageSync('isVerify')) {\r\n\t\t\t\t\tthis.modalVisible = true\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 已认证--不需要重新实名认证，需要调用选择手机号\r\n\t\t\t\t\tif (!this.userInfo.resumeRecognize) {\r\n\t\t\t\t\t\tthis.getWxPhone()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 已认证--需要重新实名认证，更换手机号的情况，自动填充\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/packagePages/login/registerSuccess?type=1&&familyItem=' + JSON.stringify(this\r\n\t\t\t\t\t\t\t\t.familyItem)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetWxPhone() {\r\n\t\t\t\tapi.fetchCurPhone({}).then(res => {\r\n\t\t\t\t\tif (res.data.length > 1) {\r\n\t\t\t\t\t\tthis.phoneList = res.data\r\n\t\t\t\t\t\tthis.modalPhoneVisible = true\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t\t\t// \turl: '/pages/index/index',\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandlePhoneCancel() {\r\n\t\t\t\tthis.modalPhoneVisible = false\r\n\t\t\t},\r\n\t\t\thandlePhoneConfirm(val) {\r\n\t\t\t\tthis.phone = val\r\n\t\t\t\tthis.modalPhoneVisible = false\r\n\t\t\t\tthis.setPhone()\r\n\t\t\t},\r\n\t\t\t// 首次状态判断\r\n\t\t\tcheckWxLogin() {\r\n\t\t\t\tloginFn(1).then(res => { // 微信登录&服务端获取openid\r\n\t\t\t\t\t// console.log(res.data,res.data.userExist, '接口换取的openid')\r\n\t\t\t\t\tif (res.data.userExist) {\r\n\t\t\t\t\t\tuni.setStorageSync('resident_id', res.data.loginUser.id)\r\n\t\t\t\t\t\tsetAuthorization(res.data.access_token)\r\n\t\t\t\t\t\tsetInfoLogin(true)\r\n\t\t\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t\t\t// \turl: `/pages/index/index`\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetPhone() {\r\n\t\t\t\tapi.chooePhone({\r\n\t\t\t\t\tphone: this.phone\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.modalPhoneVisible = false\r\n\t\t\t\t\tthis.checkWxLogin() // 由于账号合并产品需求，和后端沟通这里必须加，后续这里慎重改动\r\n\t\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t\t// \turl: '/pages/index/index',\r\n\t\t\t\t\t// })\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\thandleTabMenuChange(menuData) {\r\n\t\t\t\t// console.log('menuData', menuData)\r\n\t\t\t\tthis.menuIndex = menuData.index\r\n\t\t\t\tthis.familyItem = menuData.item\r\n\t\t\t\tif (menuData.item.type == 'button') {\r\n\t\t\t\t\tthis.menuIndex = 0\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/packagePages/my/familyFileManage`\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.getArchiveId()\r\n\t\t\t\t\tthis.getFuncState()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thandleMenuChange(item, moduleCode = '') {\r\n\t\t\t\t// console.log('8888888item', item)\r\n\t\t\t\tuni.setStorageSync('menuIndex',  this.menuIndex)\r\n\t\t\t\t// 【开发模式】\r\n\t\t\t\tif (item.type == 'grey') return\r\n\t\t\t\tvar data = JSON.stringify(item)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/packagePages/home/<USER>\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.main-box {\r\n\t\tpadding: 16px;\r\n\t\tpadding-bottom: 100px;\r\n\t\t// position: relative;\r\n\t\t// z-index: 99;\r\n\t\tpadding-top: 26px;\r\n\r\n\r\n\t\t.menu-box {\r\n\t\t\tdisplay: grid;\r\n\t\t\tgrid-template-columns: repeat(2, 50%);\r\n\t\t\t// grid-gap: 30px;\r\n\t\t\tmargin-top: 26px;\r\n\t\t\t// padding: 15px;\r\n\t\t}\r\n\r\n\t\t.menu-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: rgba(0, 0, 0, 0.90);\r\n\t\t\tposition: relative;\r\n\t\t\tborder-bottom: 1px solid #eee;\r\n\t\t\tpadding-bottom: 16px;\r\n\t\t\tmargin-bottom: 16px;\r\n\t\t\tpadding-left: 16px;\r\n\r\n\t\t\t&:nth-child(even):last-child {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\r\n\t\t\t&:nth-last-child(2):nth-child(odd) {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\r\n\t\t\t&:nth-child(odd):last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 48px;\r\n\t\t\t\theight: 48px;\r\n\t\t\t\tmargin-right: 12px;\r\n\t\t\t}\r\n\r\n\t\t\t.menu-info {\r\n\t\t\t\tfont-size: 10px;\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.4);\r\n\t\t\t\tmargin-top: 3px;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t-webkit-line-clamp: 1;\r\n\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\tline-break: anywhere;\r\n\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=71e217db&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\SOFT\\\\HBuilderX.4.36.2024112817\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=71e217db&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749542313768\n      var cssReload = require(\"D:/SOFT/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}