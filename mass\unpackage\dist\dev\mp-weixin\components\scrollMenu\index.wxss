@charset "UTF-8";
.tabs {
  height: 52px;
  padding-top: 5px;
  overflow: hidden;
  box-sizing: border-box;
  background-color: #1C6CED;
  width: 100%;
}
.tabs .tabs-scroll {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  box-sizing: border-box;
}
.tabs .tabs-scroll .tabs-scroll_item {
  height: 47px;
  line-height: 47px;
  flex-shrink: 0;
  padding-bottom: 10px;
  display: flex;
  justify-content: center;
  font-size: 14px;
  padding: 0 16px;
  padding-left: 16px;
  color: #fff;
}
.tabs .tabs-scroll .tabs-scroll_item:first-child {
  border-radius: 0 9px 0 0 !important;
}
.active {
  position: relative;
  color: #0052D9 !important;
  background-color: #fff !important;
  border-radius: 9px 9px 0 0 !important;
}
.active::after {
  content: "";
  position: absolute;
  height: 12px;
  width: 12px;
  background: radial-gradient(24px at left top, transparent 50%, #fff 50%);
  left: -12px;
  right: 0px;
  bottom: 0px;
}
.active::before {
  content: "";
  position: absolute;
  right: -12px;
  bottom: 0px;
  height: 12px;
  width: 12px;
  background: radial-gradient(24px at right top, transparent 50%, #fff 50%);
}
/* 隐藏滚动条，但依旧具备可以滚动的功能 */
.uni-scroll-view::-webkit-scrollbar {
  display: none;
}

