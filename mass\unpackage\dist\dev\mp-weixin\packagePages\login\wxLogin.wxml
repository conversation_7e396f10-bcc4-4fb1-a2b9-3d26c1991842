<view class="login-wrapper" style="{{'padding-top:'+((iPhoneX?70+JawHeight:70)+'px')+';'}}"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="iconfont icon-close _i" bindtap="__e"></view><view class="logo-box"><image class="logo" src="../../static/images/login/logo.png" mode="widthFix"></image></view><image class="login-img" src="../../static/images/login/center-img.png" mode="widthFix"></image><view class="bottom-btn-box"><button class="bottom-btn" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">一键快捷登录</button></view><view class="bottom-info"><checkbox class="checked-btn" color="#578AFF" checked="{{checkState}}" data-event-opts="{{[['tap',[['handleCheckboxChange',['$0'],['checkState']]]]]}}" bindtap="__e"></checkbox><view><text>我已阅读并同意</text><label data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="_span">《用户服务协议》</label><text>和</text><label data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e" class="_span">《个人信息保护政策》</label></view></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="dc7033b2-1" modalData="{{modalData}}" btnVisible="{{true}}" data-event-opts="{{[['^cancel',[['handleCancel']]],['^confirm',[['handleConfirm']]]]}}" bind:cancel="__e" bind:confirm="__e" bind:__l="__l"></modal-dialog><footer style="width:100%;" vue-id="dc7033b2-2" type="white" bind:__l="__l"></footer></view>