{"version": 3, "file": "purify.es.92ee18ee.js", "sources": ["../../../../node_modules/.store/dompurify@2.5.8/node_modules/dompurify/dist/purify.es.js"], "sourcesContent": ["/*! @license DOMPurify 2.5.8 | (c) <PERSON><PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/2.5.8/LICENSE */\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct;\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n  return _construct.apply(null, arguments);\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar hasOwnProperty = Object.hasOwnProperty,\n  setPrototypeOf = Object.setPrototypeOf,\n  isFrozen = Object.isFrozen,\n  getPrototypeOf = Object.getPrototypeOf,\n  getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar freeze = Object.freeze,\n  seal = Object.seal,\n  create = Object.create; // eslint-disable-line import/no-mutable-exports\nvar _ref = typeof Reflect !== 'undefined' && Reflect,\n  apply = _ref.apply,\n  construct = _ref.construct;\nif (!apply) {\n  apply = function apply(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\nif (!freeze) {\n  freeze = function freeze(x) {\n    return x;\n  };\n}\nif (!seal) {\n  seal = function seal(x) {\n    return x;\n  };\n}\nif (!construct) {\n  construct = function construct(Func, args) {\n    return _construct(Func, _toConsumableArray(args));\n  };\n}\nvar arrayForEach = unapply(Array.prototype.forEach);\nvar arrayPop = unapply(Array.prototype.pop);\nvar arrayPush = unapply(Array.prototype.push);\nvar stringToLowerCase = unapply(String.prototype.toLowerCase);\nvar stringToString = unapply(String.prototype.toString);\nvar stringMatch = unapply(String.prototype.match);\nvar stringReplace = unapply(String.prototype.replace);\nvar stringIndexOf = unapply(String.prototype.indexOf);\nvar stringTrim = unapply(String.prototype.trim);\nvar regExpTest = unapply(RegExp.prototype.test);\nvar typeErrorCreate = unconstruct(TypeError);\nfunction unapply(func) {\n  return function (thisArg) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return apply(func, thisArg, args);\n  };\n}\nfunction unconstruct(func) {\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return construct(func, args);\n  };\n}\n\n/* Add properties to a lookup table */\nfunction addToSet(set, array, transformCaseFunc) {\n  var _transformCaseFunc;\n  transformCaseFunc = (_transformCaseFunc = transformCaseFunc) !== null && _transformCaseFunc !== void 0 ? _transformCaseFunc : stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n  var l = array.length;\n  while (l--) {\n    var element = array[l];\n    if (typeof element === 'string') {\n      var lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n        element = lcElement;\n      }\n    }\n    set[element] = true;\n  }\n  return set;\n}\n\n/* Shallow clone an object */\nfunction clone(object) {\n  var newObject = create(null);\n  var property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property]) === true) {\n      newObject[property] = object[property];\n    }\n  }\n  return newObject;\n}\n\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    var desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n    object = getPrototypeOf(object);\n  }\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n  return fallbackValue;\n}\n\nvar html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']);\n\n// SVG\nvar svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\nvar svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nvar svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'fedropshadow', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\nvar mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover']);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nvar mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\nvar text = freeze(['#text']);\n\nvar html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'xmlns', 'slot']);\nvar svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\nvar mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\nvar xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\n// eslint-disable-next-line unicorn/better-regex\nvar MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nvar ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nvar TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nvar DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nvar ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nvar IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nvar IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nvar ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nvar DOCTYPE_NAME = seal(/^html$/i);\nvar CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n\nvar getGlobal = function getGlobal() {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nvar _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, document) {\n  if (_typeof(trustedTypes) !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  var suffix = null;\n  var ATTR_NAME = 'data-tt-policy-suffix';\n  if (document.currentScript && document.currentScript.hasAttribute(ATTR_NAME)) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n  var policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML: function createHTML(html) {\n        return html;\n      },\n      createScriptURL: function createScriptURL(scriptUrl) {\n        return scriptUrl;\n      }\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n    return null;\n  }\n};\nfunction createDOMPurify() {\n  var window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n  var DOMPurify = function DOMPurify(root) {\n    return createDOMPurify(root);\n  };\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = '2.5.8';\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n    return DOMPurify;\n  }\n  var originalDocument = window.document;\n  var document = window.document;\n  var DocumentFragment = window.DocumentFragment,\n    HTMLTemplateElement = window.HTMLTemplateElement,\n    Node = window.Node,\n    Element = window.Element,\n    NodeFilter = window.NodeFilter,\n    _window$NamedNodeMap = window.NamedNodeMap,\n    NamedNodeMap = _window$NamedNodeMap === void 0 ? window.NamedNodeMap || window.MozNamedAttrMap : _window$NamedNodeMap,\n    HTMLFormElement = window.HTMLFormElement,\n    DOMParser = window.DOMParser,\n    trustedTypes = window.trustedTypes;\n  var ElementPrototype = Element.prototype;\n  var cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  var getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  var getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  var getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    var template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n  var trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, originalDocument);\n  var emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n  var _document = document,\n    implementation = _document.implementation,\n    createNodeIterator = _document.createNodeIterator,\n    createDocumentFragment = _document.createDocumentFragment,\n    getElementsByTagName = _document.getElementsByTagName;\n  var importNode = originalDocument.importNode;\n  var documentMode = {};\n  try {\n    documentMode = clone(document).documentMode ? document.documentMode : {};\n  } catch (_) {}\n  var hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported = typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined && documentMode !== 9;\n  var MUSTACHE_EXPR$1 = MUSTACHE_EXPR,\n    ERB_EXPR$1 = ERB_EXPR,\n    TMPLIT_EXPR$1 = TMPLIT_EXPR,\n    DATA_ATTR$1 = DATA_ATTR,\n    ARIA_ATTR$1 = ARIA_ATTR,\n    IS_SCRIPT_OR_DATA$1 = IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE$1 = ATTR_WHITESPACE,\n    CUSTOM_ELEMENT$1 = CUSTOM_ELEMENT;\n  var IS_ALLOWED_URI$1 = IS_ALLOWED_URI;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  var ALLOWED_TAGS = null;\n  var DEFAULT_ALLOWED_TAGS = addToSet({}, [].concat(_toConsumableArray(html$1), _toConsumableArray(svg$1), _toConsumableArray(svgFilters), _toConsumableArray(mathMl$1), _toConsumableArray(text)));\n\n  /* Allowed attribute names */\n  var ALLOWED_ATTR = null;\n  var DEFAULT_ALLOWED_ATTR = addToSet({}, [].concat(_toConsumableArray(html), _toConsumableArray(svg), _toConsumableArray(mathMl), _toConsumableArray(xml)));\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  var CUSTOM_ELEMENT_HANDLING = Object.seal(Object.create(null, {\n    tagNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    attributeNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    allowCustomizedBuiltInElements: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: false\n    }\n  }));\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  var FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  var FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  var ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  var ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  var ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  var ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  var SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  var SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  var WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  var SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  var FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  var RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  var RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  var RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  var SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  var SANITIZE_NAMED_PROPS = false;\n  var SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  var KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  var IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  var USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  var FORBID_CONTENTS = null;\n  var DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n\n  /* Tags that are safe for data: URIs */\n  var DATA_URI_TAGS = null;\n  var DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n\n  /* Attributes safe for values like \"javascript:\" */\n  var URI_SAFE_ATTRIBUTES = null;\n  var DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n  var MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  var SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  var HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  var NAMESPACE = HTML_NAMESPACE;\n  var IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  var ALLOWED_NAMESPACES = null;\n  var DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n\n  /* Parsing of strict XHTML documents */\n  var PARSER_MEDIA_TYPE;\n  var SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  var DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  var transformCaseFunc;\n\n  /* Keep a reference to config to pass to hooks */\n  var CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  var formElement = document.createElement('form');\n  var isRegexOrFunction = function isRegexOrFunction(testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  var _parseConfig = function _parseConfig(cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || _typeof(cfg) !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n    PARSER_MEDIA_TYPE =\n    // eslint-disable-next-line unicorn/prefer-includes\n    SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE : PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE;\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS = 'ALLOWED_TAGS' in cfg ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = 'ALLOWED_ATTR' in cfg ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = 'ALLOWED_NAMESPACES' in cfg ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = 'ADD_URI_SAFE_ATTR' in cfg ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n    // eslint-disable-line indent\n    cfg.ADD_URI_SAFE_ATTR,\n    // eslint-disable-line indent\n    transformCaseFunc // eslint-disable-line indent\n    ) // eslint-disable-line indent\n    : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = 'ADD_DATA_URI_TAGS' in cfg ? addToSet(clone(DEFAULT_DATA_URI_TAGS),\n    // eslint-disable-line indent\n    cfg.ADD_DATA_URI_TAGS,\n    // eslint-disable-line indent\n    transformCaseFunc // eslint-disable-line indent\n    ) // eslint-disable-line indent\n    : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = 'FORBID_CONTENTS' in cfg ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n    FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI$1;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, _toConsumableArray(text));\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, html$1);\n        addToSet(ALLOWED_ATTR, html);\n      }\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, svg$1);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, svgFilters);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, mathMl$1);\n        addToSet(ALLOWED_ATTR, mathMl);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n    CONFIG = cfg;\n  };\n  var MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n  var HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  var COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  var ALL_SVG_TAGS = addToSet({}, svg$1);\n  addToSet(ALL_SVG_TAGS, svgFilters);\n  addToSet(ALL_SVG_TAGS, svgDisallowed);\n  var ALL_MATHML_TAGS = addToSet({}, mathMl$1);\n  addToSet(ALL_MATHML_TAGS, mathMlDisallowed);\n\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  var _checkValidNamespace = function _checkValidNamespace(element) {\n    var parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template'\n      };\n    }\n    var tagName = stringToLowerCase(element.tagName);\n    var parentTagName = stringToLowerCase(parent.tagName);\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  var _forceRemove = function _forceRemove(node) {\n    arrayPush(DOMPurify.removed, {\n      element: node\n    });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      try {\n        node.outerHTML = emptyHTML;\n      } catch (_) {\n        node.remove();\n      }\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  var _removeAttribute = function _removeAttribute(name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node\n      });\n    }\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  var _initDocument = function _initDocument(dirty) {\n    /* Create a HTML document */\n    var doc;\n    var leadingWhitespace;\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      var matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n    }\n    var dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n    var body = doc.body || doc.documentElement;\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n    }\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  var _createIterator = function _createIterator(root) {\n    return createNodeIterator.call(root.ownerDocument || root, root,\n    // eslint-disable-next-line no-bitwise\n    NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null, false);\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  var _isClobbered = function _isClobbered(elm) {\n    return elm instanceof HTMLFormElement && (typeof elm.nodeName !== 'string' || typeof elm.textContent !== 'string' || typeof elm.removeChild !== 'function' || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== 'function' || typeof elm.setAttribute !== 'function' || typeof elm.namespaceURI !== 'string' || typeof elm.insertBefore !== 'function' || typeof elm.hasChildNodes !== 'function');\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  var _isNode = function _isNode(object) {\n    return _typeof(Node) === 'object' ? object instanceof Node : object && _typeof(object) === 'object' && typeof object.nodeType === 'number' && typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  var _executeHook = function _executeHook(entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n    arrayForEach(hooks[entryPoint], function (hook) {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  var _sanitizeElements = function _sanitizeElements(currentNode) {\n    var content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check if tagname contains Unicode */\n    if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    var tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName: tagName,\n      allowedTags: ALLOWED_TAGS\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && (!_isNode(currentNode.content) || !_isNode(currentNode.content.firstElementChild)) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Mitigate a problem with templates inside select */\n    if (tagName === 'select' && regExpTest(/<template/i, currentNode.innerHTML)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any ocurrence of processing instructions */\n    if (currentNode.nodeType === 7) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (SAFE_FOR_XML && currentNode.nodeType === 8 && regExpTest(/<[/\\w]/g, currentNode.data)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) return false;\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) return false;\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        var parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        var childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n        if (childNodes && parentNode) {\n          var childCount = childNodes.length;\n          for (var i = childCount - 1; i >= 0; --i) {\n            var childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR$1, ' ');\n      content = stringReplace(content, ERB_EXPR$1, ' ');\n      content = stringReplace(content, TMPLIT_EXPR$1, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, {\n          element: currentNode.cloneNode()\n        });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  var _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR$1, lcName)) ; else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR$1, lcName)) ; else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n      // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n      // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n      _basicCustomElementTest(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) ||\n      // Alternative, second condition checks if it's an `is`-attribute, AND\n      // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ; else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) ; else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE$1, ''))) ; else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ; else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA$1, stringReplace(value, ATTR_WHITESPACE$1, ''))) ; else if (value) {\n      return false;\n    } else ;\n    return true;\n  };\n\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n  var _basicCustomElementTest = function _basicCustomElementTest(tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT$1);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  var _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n    var attr;\n    var value;\n    var lcName;\n    var l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n    var attributes = currentNode.attributes;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n    var hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      var _attr = attr,\n        name = _attr.name,\n        namespaceURI = _attr.namespaceURI;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR$1, ' ');\n        value = stringReplace(value, ERB_EXPR$1, ' ');\n        value = stringReplace(value, TMPLIT_EXPR$1, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      var lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (trustedTypesPolicy && _typeof(trustedTypes) === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n        if (namespaceURI) ; else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML':\n              {\n                value = trustedTypesPolicy.createHTML(value);\n                break;\n              }\n            case 'TrustedScriptURL':\n              {\n                value = trustedTypesPolicy.createScriptURL(value);\n                break;\n              }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  var _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n    var shadowNode;\n    var shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n    while (shadowNode = shadowIterator.nextNode()) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty) {\n    var cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var body;\n    var importedNode;\n    var currentNode;\n    var oldNode;\n    var returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (_typeof(window.toStaticHTML) === 'object' || typeof window.toStaticHTML === 'function') {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        var tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT &&\n      // eslint-disable-next-line unicorn/prefer-includes\n      dirty.indexOf('<') === -1) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    var nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while (currentNode = nodeIterator.nextNode()) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n      oldNode = currentNode;\n    }\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n      return returnNode;\n    }\n    var serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n      serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR$1, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR$1, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR$1, ' ');\n    }\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n    var lcTag = transformCaseFunc(tag);\n    var lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n  return DOMPurify;\n}\nvar purify = createDOMPurify();\n\nexport { purify as default };\n//# sourceMappingURL=purify.es.js.map\n"], "names": ["_typeof", "obj", "_setPrototypeOf", "o", "p", "_isNativeReflectConstruct", "_construct", "Parent", "args", "Class", "a", "<PERSON><PERSON><PERSON><PERSON>", "instance", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "iter", "minLen", "n", "len", "i", "arr2", "hasOwnProperty", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "_ref", "apply", "construct", "fun", "thisValue", "x", "Func", "arrayForEach", "unapply", "arrayPop", "arrayPush", "stringToLowerCase", "stringToString", "stringMatch", "stringReplace", "stringIndexOf", "stringTrim", "regExpTest", "typeErrorCreate", "unconstruct", "func", "thisArg", "_len", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "l", "element", "lcElement", "clone", "object", "newObject", "property", "lookupGetter", "prop", "desc", "fallback<PERSON><PERSON><PERSON>", "html$1", "svg$1", "svgFilters", "svgDisallowed", "mathMl$1", "mathMlDisallowed", "text", "html", "svg", "mathMl", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "getGlobal", "_createTrustedTypesPolicy", "trustedTypes", "document", "suffix", "ATTR_NAME", "policyName", "scriptUrl", "createDOMPurify", "window", "DOMPurify", "root", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "_window$NamedNodeMap", "NamedNodeMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "trustedTypesPolicy", "emptyHTML", "_document", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "hooks", "MUSTACHE_EXPR$1", "ERB_EXPR$1", "TMPLIT_EXPR$1", "DATA_ATTR$1", "ARIA_ATTR$1", "IS_SCRIPT_OR_DATA$1", "ATTR_WHITESPACE$1", "CUSTOM_ELEMENT$1", "IS_ALLOWED_URI$1", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "CUSTOM_ELEMENT_HANDLING", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "_parseConfig", "cfg", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "parentTagName", "_forceRemove", "node", "_removeAttribute", "name", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "body", "_createIterator", "_isClobbered", "elm", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "content", "_basicCustomElementTest", "parentNode", "childNodes", "childCount", "child<PERSON>lone", "_isValidAttribute", "lcTag", "lcName", "value", "_sanitizeAttributes", "attr", "attributes", "hookEvent", "_attr", "namespaceURI", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "importedNode", "oldNode", "returnNode", "nodeIterator", "serializedHTML", "tag", "hookFunction", "purify"], "mappings": "AAAA,2LAEA,SAASA,EAAQC,EAAK,CAGpB,OAAOD,EAAwB,OAAO,QAArB,YAA2C,OAAO,OAAO,UAA1B,SAAqC,SAAUC,EAAK,CAClG,OAAO,OAAOA,CACf,EAAG,SAAUA,EAAK,CACjB,OAAOA,GAAqB,OAAO,QAArB,YAA+BA,EAAI,cAAgB,QAAUA,IAAQ,OAAO,UAAY,SAAW,OAAOA,CAC5H,EAAKD,EAAQC,CAAG,CAChB,CACA,SAASC,GAAgBC,EAAGC,EAAG,CAC7B,OAAAF,GAAkB,OAAO,gBAAkB,SAAyBC,EAAGC,EAAG,CACxE,OAAAD,EAAE,UAAYC,EACPD,CACX,EACSD,GAAgBC,EAAGC,CAAC,CAC7B,CACA,SAASC,IAA4B,CAEnC,GADI,OAAO,QAAY,KAAe,CAAC,QAAQ,WAC3C,QAAQ,UAAU,KAAM,MAAO,GACnC,GAAI,OAAO,OAAU,WAAY,MAAO,GACxC,GAAI,CACF,eAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,QAAS,CAAE,EAAE,UAAY,CAAE,CAAA,CAAC,EACtE,EACR,MAAC,CACA,MAAO,EACR,CACH,CACA,SAASC,GAAWC,EAAQC,EAAMC,EAAO,CACvC,OAAIJ,GAAyB,EAC3BC,GAAa,QAAQ,UAErBA,GAAa,SAAoBC,EAAQC,EAAMC,EAAO,CACpD,IAAIC,EAAI,CAAC,IAAI,EACbA,EAAE,KAAK,MAAMA,EAAGF,CAAI,EACpB,IAAIG,EAAc,SAAS,KAAK,MAAMJ,EAAQG,CAAC,EAC3CE,EAAW,IAAID,EACnB,OAAIF,GAAOP,GAAgBU,EAAUH,EAAM,SAAS,EAC7CG,CACb,EAESN,GAAW,MAAM,KAAM,SAAS,CACzC,CACA,SAASO,EAAmBC,EAAK,CAC/B,OAAOC,GAAmBD,CAAG,GAAKE,GAAiBF,CAAG,GAAKG,GAA4BH,CAAG,GAAKI,IACjG,CACA,SAASH,GAAmBD,EAAK,CAC/B,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOK,GAAkBL,CAAG,CACtD,CACA,SAASE,GAAiBI,EAAM,CAC9B,GAAI,OAAO,OAAW,KAAeA,EAAK,OAAO,WAAa,MAAQA,EAAK,eAAiB,KAAM,OAAO,MAAM,KAAKA,CAAI,CAC1H,CACA,SAASH,GAA4Bd,EAAGkB,EAAQ,CAC9C,GAAI,EAAClB,EACL,IAAI,OAAOA,GAAM,SAAU,OAAOgB,GAAkBhB,EAAGkB,CAAM,EAC7D,IAAIC,EAAI,OAAO,UAAU,SAAS,KAAKnB,CAAC,EAAE,MAAM,EAAG,EAAE,EAErD,GADImB,IAAM,UAAYnB,EAAE,cAAamB,EAAInB,EAAE,YAAY,MACnDmB,IAAM,OAASA,IAAM,MAAO,OAAO,MAAM,KAAKnB,CAAC,EACnD,GAAImB,IAAM,aAAe,2CAA2C,KAAKA,CAAC,EAAG,OAAOH,GAAkBhB,EAAGkB,CAAM,EACjH,CACA,SAASF,GAAkBL,EAAKS,EAAK,EAC/BA,GAAO,MAAQA,EAAMT,EAAI,UAAQS,EAAMT,EAAI,QAC/C,QAASU,EAAI,EAAGC,EAAO,IAAI,MAAMF,CAAG,EAAGC,EAAID,EAAKC,IAAKC,EAAKD,GAAKV,EAAIU,GACnE,OAAOC,CACT,CACA,SAASP,IAAqB,CAC5B,MAAM,IAAI,UAAU;AAAA,mFAAsI,CAC5J,CAEA,IAAIQ,GAAiB,OAAO,eAC1BC,GAAiB,OAAO,eACxBC,GAAW,OAAO,SAClBC,GAAiB,OAAO,eACxBC,GAA2B,OAAO,yBAChCC,EAAS,OAAO,OAClBC,EAAO,OAAO,KACdC,GAAS,OAAO,OACdC,GAAO,OAAO,QAAY,KAAe,QAC3CC,GAAQD,GAAK,MACbE,GAAYF,GAAK,UACdC,KACHA,GAAQ,SAAeE,EAAKC,EAAW9B,EAAM,CAC3C,OAAO6B,EAAI,MAAMC,EAAW9B,CAAI,CACpC,GAEKuB,IACHA,EAAS,SAAgBQ,EAAG,CAC1B,OAAOA,CACX,GAEKP,IACHA,EAAO,SAAcO,EAAG,CACtB,OAAOA,CACX,GAEKH,KACHA,GAAY,SAAmBI,EAAMhC,EAAM,CACzC,OAAOF,GAAWkC,EAAM3B,EAAmBL,CAAI,CAAC,CACpD,GAEA,IAAIiC,GAAeC,EAAQ,MAAM,UAAU,OAAO,EAC9CC,GAAWD,EAAQ,MAAM,UAAU,GAAG,EACtCE,EAAYF,EAAQ,MAAM,UAAU,IAAI,EACxCG,GAAoBH,EAAQ,OAAO,UAAU,WAAW,EACxDI,GAAiBJ,EAAQ,OAAO,UAAU,QAAQ,EAClDK,GAAcL,EAAQ,OAAO,UAAU,KAAK,EAC5CM,EAAgBN,EAAQ,OAAO,UAAU,OAAO,EAChDO,GAAgBP,EAAQ,OAAO,UAAU,OAAO,EAChDQ,GAAaR,EAAQ,OAAO,UAAU,IAAI,EAC1CS,EAAaT,EAAQ,OAAO,UAAU,IAAI,EAC1CU,GAAkBC,GAAY,SAAS,EAC3C,SAASX,EAAQY,EAAM,CACrB,OAAO,SAAUC,EAAS,CACxB,QAASC,EAAO,UAAU,OAAQhD,EAAO,IAAI,MAAMgD,EAAO,EAAIA,EAAO,EAAI,CAAC,EAAGC,EAAO,EAAGA,EAAOD,EAAMC,IAClGjD,EAAKiD,EAAO,GAAK,UAAUA,GAE7B,OAAOtB,GAAMmB,EAAMC,EAAS/C,CAAI,CACpC,CACA,CACA,SAAS6C,GAAYC,EAAM,CACzB,OAAO,UAAY,CACjB,QAASI,EAAQ,UAAU,OAAQlD,EAAO,IAAI,MAAMkD,CAAK,EAAGC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFnD,EAAKmD,GAAS,UAAUA,GAE1B,OAAOvB,GAAUkB,EAAM9C,CAAI,CAC/B,CACA,CAGA,SAASoD,EAASC,EAAKC,EAAOC,EAAmB,CAC/C,IAAIC,EACJD,GAAqBC,EAAqBD,KAAuB,MAAQC,IAAuB,OAASA,EAAqBnB,GAC1HlB,IAIFA,GAAekC,EAAK,IAAI,EAG1B,QADII,EAAIH,EAAM,OACPG,KAAK,CACV,IAAIC,EAAUJ,EAAMG,GACpB,GAAI,OAAOC,GAAY,SAAU,CAC/B,IAAIC,EAAYJ,EAAkBG,CAAO,EACrCC,IAAcD,IAEXtC,GAASkC,CAAK,IACjBA,EAAMG,GAAKE,GAEbD,EAAUC,EAEb,CACDN,EAAIK,GAAW,EAChB,CACD,OAAOL,CACT,CAGA,SAASO,EAAMC,EAAQ,CACrB,IAAIC,EAAYrC,GAAO,IAAI,EACvBsC,EACJ,IAAKA,KAAYF,EACXlC,GAAMT,GAAgB2C,EAAQ,CAACE,CAAQ,CAAC,IAAM,KAChDD,EAAUC,GAAYF,EAAOE,IAGjC,OAAOD,CACT,CAMA,SAASE,GAAaH,EAAQI,EAAM,CAClC,KAAOJ,IAAW,MAAM,CACtB,IAAIK,EAAO5C,GAAyBuC,EAAQI,CAAI,EAChD,GAAIC,EAAM,CACR,GAAIA,EAAK,IACP,OAAOhC,EAAQgC,EAAK,GAAG,EAEzB,GAAI,OAAOA,EAAK,OAAU,WACxB,OAAOhC,EAAQgC,EAAK,KAAK,CAE5B,CACDL,EAASxC,GAAewC,CAAM,CAC/B,CACD,SAASM,EAAcT,EAAS,CAC9B,eAAQ,KAAK,qBAAsBA,CAAO,EACnC,IACR,CACD,OAAOS,CACT,CAEA,IAAIC,GAAS7C,EAAO,CAAC,IAAK,OAAQ,UAAW,UAAW,OAAQ,UAAW,QAAS,QAAS,IAAK,MAAO,MAAO,MAAO,QAAS,aAAc,OAAQ,KAAM,SAAU,SAAU,UAAW,SAAU,OAAQ,OAAQ,MAAO,WAAY,UAAW,OAAQ,WAAY,KAAM,YAAa,MAAO,UAAW,MAAO,SAAU,MAAO,MAAO,KAAM,KAAM,UAAW,KAAM,WAAY,aAAc,SAAU,OAAQ,SAAU,OAAQ,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAAQ,SAAU,SAAU,KAAM,OAAQ,IAAK,MAAO,QAAS,MAAO,MAAO,QAAS,SAAU,KAAM,OAAQ,MAAO,OAAQ,UAAW,OAAQ,WAAY,QAAS,MAAO,OAAQ,KAAM,WAAY,SAAU,SAAU,IAAK,UAAW,MAAO,WAAY,IAAK,KAAM,KAAM,OAAQ,IAAK,OAAQ,UAAW,SAAU,SAAU,QAAS,SAAU,SAAU,OAAQ,SAAU,SAAU,QAAS,MAAO,UAAW,MAAO,QAAS,QAAS,KAAM,WAAY,WAAY,QAAS,KAAM,QAAS,OAAQ,KAAM,QAAS,KAAM,IAAK,KAAM,MAAO,QAAS,KAAK,CAAC,EAGz+B8C,GAAQ9C,EAAO,CAAC,MAAO,IAAK,WAAY,cAAe,eAAgB,eAAgB,gBAAiB,mBAAoB,SAAU,WAAY,OAAQ,OAAQ,UAAW,SAAU,OAAQ,IAAK,QAAS,WAAY,QAAS,QAAS,OAAQ,iBAAkB,SAAU,OAAQ,WAAY,QAAS,OAAQ,UAAW,UAAW,WAAY,iBAAkB,OAAQ,OAAQ,QAAS,SAAU,SAAU,OAAQ,WAAY,QAAS,OAAQ,QAAS,OAAQ,OAAO,CAAC,EACnd+C,GAAa/C,EAAO,CAAC,UAAW,gBAAiB,sBAAuB,cAAe,mBAAoB,oBAAqB,oBAAqB,iBAAkB,UAAW,UAAW,UAAW,UAAW,UAAW,iBAAkB,UAAW,UAAW,cAAe,eAAgB,WAAY,eAAgB,qBAAsB,cAAe,SAAU,cAAc,CAAC,EAM/XgD,GAAgBhD,EAAO,CAAC,UAAW,gBAAiB,SAAU,UAAW,eAAgB,YAAa,mBAAoB,iBAAkB,gBAAiB,gBAAiB,gBAAiB,QAAS,YAAa,OAAQ,eAAgB,YAAa,UAAW,gBAAiB,SAAU,MAAO,aAAc,UAAW,KAAK,CAAC,EACtUiD,GAAWjD,EAAO,CAAC,OAAQ,WAAY,SAAU,UAAW,QAAS,SAAU,KAAM,aAAc,gBAAiB,KAAM,KAAM,QAAS,UAAW,WAAY,QAAS,OAAQ,KAAM,SAAU,QAAS,SAAU,OAAQ,OAAQ,UAAW,SAAU,MAAO,QAAS,MAAO,SAAU,YAAY,CAAC,EAIvSkD,GAAmBlD,EAAO,CAAC,UAAW,cAAe,aAAc,WAAY,YAAa,UAAW,UAAW,SAAU,SAAU,QAAS,YAAa,aAAc,iBAAkB,cAAe,MAAM,CAAC,EAClNmD,GAAOnD,EAAO,CAAC,OAAO,CAAC,EAEvBoD,GAAOpD,EAAO,CAAC,SAAU,SAAU,QAAS,MAAO,iBAAkB,eAAgB,uBAAwB,WAAY,aAAc,UAAW,SAAU,UAAW,cAAe,cAAe,UAAW,OAAQ,QAAS,QAAS,QAAS,OAAQ,UAAW,WAAY,eAAgB,SAAU,cAAe,WAAY,WAAY,UAAW,MAAO,WAAY,0BAA2B,wBAAyB,WAAY,YAAa,UAAW,eAAgB,OAAQ,MAAO,UAAW,SAAU,SAAU,OAAQ,OAAQ,WAAY,KAAM,YAAa,YAAa,QAAS,OAAQ,QAAS,OAAQ,OAAQ,UAAW,OAAQ,MAAO,MAAO,YAAa,QAAS,SAAU,MAAO,YAAa,WAAY,QAAS,OAAQ,QAAS,UAAW,aAAc,SAAU,OAAQ,UAAW,UAAW,cAAe,cAAe,SAAU,UAAW,UAAW,aAAc,WAAY,MAAO,WAAY,MAAO,WAAY,OAAQ,OAAQ,UAAW,aAAc,QAAS,WAAY,QAAS,OAAQ,QAAS,OAAQ,UAAW,QAAS,MAAO,SAAU,OAAQ,QAAS,UAAW,WAAY,QAAS,YAAa,OAAQ,SAAU,SAAU,QAAS,QAAS,QAAS,MAAM,CAAC,EACxqCqD,GAAMrD,EAAO,CAAC,gBAAiB,aAAc,WAAY,qBAAsB,SAAU,gBAAiB,gBAAiB,UAAW,gBAAiB,iBAAkB,QAAS,OAAQ,KAAM,QAAS,OAAQ,gBAAiB,YAAa,YAAa,QAAS,sBAAuB,8BAA+B,gBAAiB,kBAAmB,KAAM,KAAM,IAAK,KAAM,KAAM,kBAAmB,YAAa,UAAW,UAAW,MAAO,WAAY,YAAa,MAAO,OAAQ,eAAgB,YAAa,SAAU,cAAe,cAAe,gBAAiB,cAAe,YAAa,mBAAoB,eAAgB,aAAc,eAAgB,cAAe,KAAM,KAAM,KAAM,KAAM,aAAc,WAAY,gBAAiB,oBAAqB,SAAU,OAAQ,KAAM,kBAAmB,KAAM,MAAO,IAAK,KAAM,KAAM,KAAM,KAAM,UAAW,YAAa,aAAc,WAAY,OAAQ,eAAgB,iBAAkB,eAAgB,mBAAoB,iBAAkB,QAAS,aAAc,aAAc,eAAgB,eAAgB,cAAe,cAAe,mBAAoB,YAAa,MAAO,OAAQ,QAAS,SAAU,OAAQ,MAAO,OAAQ,aAAc,SAAU,WAAY,UAAW,QAAS,SAAU,cAAe,SAAU,WAAY,cAAe,OAAQ,aAAc,sBAAuB,mBAAoB,eAAgB,SAAU,gBAAiB,sBAAuB,iBAAkB,IAAK,KAAM,KAAM,SAAU,OAAQ,OAAQ,cAAe,YAAa,UAAW,SAAU,SAAU,QAAS,OAAQ,kBAAmB,mBAAoB,mBAAoB,eAAgB,cAAe,eAAgB,cAAe,aAAc,eAAgB,mBAAoB,oBAAqB,iBAAkB,kBAAmB,oBAAqB,iBAAkB,SAAU,eAAgB,QAAS,eAAgB,iBAAkB,WAAY,UAAW,UAAW,YAAa,mBAAoB,cAAe,kBAAmB,iBAAkB,aAAc,OAAQ,KAAM,KAAM,UAAW,SAAU,UAAW,aAAc,UAAW,aAAc,gBAAiB,gBAAiB,QAAS,eAAgB,OAAQ,eAAgB,mBAAoB,mBAAoB,IAAK,KAAM,KAAM,QAAS,IAAK,KAAM,KAAM,IAAK,YAAY,CAAC,EAC3wEsD,GAAStD,EAAO,CAAC,SAAU,cAAe,QAAS,WAAY,QAAS,eAAgB,cAAe,aAAc,aAAc,QAAS,MAAO,UAAW,eAAgB,WAAY,QAAS,QAAS,SAAU,OAAQ,KAAM,UAAW,SAAU,gBAAiB,SAAU,SAAU,iBAAkB,YAAa,WAAY,cAAe,UAAW,UAAW,gBAAiB,WAAY,WAAY,OAAQ,WAAY,WAAY,aAAc,UAAW,SAAU,SAAU,cAAe,gBAAiB,uBAAwB,YAAa,YAAa,aAAc,WAAY,iBAAkB,iBAAkB,YAAa,UAAW,QAAS,OAAO,CAAC,EAC7pBuD,GAAMvD,EAAO,CAAC,aAAc,SAAU,cAAe,YAAa,aAAa,CAAC,EAGhFwD,GAAgBvD,EAAK,2BAA2B,EAChDwD,GAAWxD,EAAK,uBAAuB,EACvCyD,GAAczD,EAAK,eAAe,EAClC0D,GAAY1D,EAAK,8BAA8B,EAC/C2D,GAAY3D,EAAK,gBAAgB,EACjC4D,GAAiB5D,EAAK,uFAC1B,EACI6D,GAAoB7D,EAAK,uBAAuB,EAChD8D,GAAkB9D,EAAK,6DAC3B,EACI+D,GAAe/D,EAAK,SAAS,EAC7BgE,GAAiBhE,EAAK,0BAA0B,EAEhDiE,GAAY,UAAqB,CACnC,OAAO,OAAO,OAAW,IAAc,KAAO,MAChD,EAUIC,GAA4B,SAAmCC,EAAcC,EAAU,CACzF,GAAIpG,EAAQmG,CAAY,IAAM,UAAY,OAAOA,EAAa,cAAiB,WAC7E,OAAO,KAMT,IAAIE,EAAS,KACTC,EAAY,wBACZF,EAAS,eAAiBA,EAAS,cAAc,aAAaE,CAAS,IACzED,EAASD,EAAS,cAAc,aAAaE,CAAS,GAExD,IAAIC,EAAa,aAAeF,EAAS,IAAMA,EAAS,IACxD,GAAI,CACF,OAAOF,EAAa,aAAaI,EAAY,CAC3C,WAAY,SAAoBpB,EAAM,CACpC,OAAOA,CACR,EACD,gBAAiB,SAAyBqB,EAAW,CACnD,OAAOA,CACR,CACP,CAAK,CACF,MAAC,CAIA,eAAQ,KAAK,uBAAyBD,EAAa,wBAAwB,EACpE,IACR,CACH,EACA,SAASE,IAAkB,CACzB,IAAIC,EAAS,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAKT,GAAS,EACtFU,EAAY,SAAmBC,EAAM,CACvC,OAAOH,GAAgBG,CAAI,CAC/B,EAaE,GAPAD,EAAU,QAAU,QAMpBA,EAAU,QAAU,GAChB,CAACD,GAAU,CAACA,EAAO,UAAYA,EAAO,SAAS,WAAa,EAG9D,OAAAC,EAAU,YAAc,GACjBA,EAET,IAAIE,EAAmBH,EAAO,SAC1BN,EAAWM,EAAO,SAClBI,EAAmBJ,EAAO,iBAC5BK,EAAsBL,EAAO,oBAC7BM,EAAON,EAAO,KACdO,EAAUP,EAAO,QACjBQ,EAAaR,EAAO,WACpBS,EAAuBT,EAAO,aAC9BU,GAAeD,IAAyB,OAAST,EAAO,cAAgBA,EAAO,gBAAkBS,EACjGE,GAAkBX,EAAO,gBACzBY,GAAYZ,EAAO,UACnBP,EAAeO,EAAO,aACpBa,EAAmBN,EAAQ,UAC3BO,GAAYhD,GAAa+C,EAAkB,WAAW,EACtDE,GAAiBjD,GAAa+C,EAAkB,aAAa,EAC7DG,GAAgBlD,GAAa+C,EAAkB,YAAY,EAC3DI,GAAgBnD,GAAa+C,EAAkB,YAAY,EAQ/D,GAAI,OAAOR,GAAwB,WAAY,CAC7C,IAAIa,GAAWxB,EAAS,cAAc,UAAU,EAC5CwB,GAAS,SAAWA,GAAS,QAAQ,gBACvCxB,EAAWwB,GAAS,QAAQ,cAE/B,CACD,IAAIC,EAAqB3B,GAA0BC,EAAcU,CAAgB,EAC7EiB,GAAYD,EAAqBA,EAAmB,WAAW,EAAE,EAAI,GACrEE,EAAY3B,EACd4B,GAAiBD,EAAU,eAC3BE,GAAqBF,EAAU,mBAC/BG,GAAyBH,EAAU,uBACnCI,GAAuBJ,EAAU,qBAC/BK,GAAavB,EAAiB,WAC9BwB,GAAe,CAAA,EACnB,GAAI,CACFA,GAAejE,EAAMgC,CAAQ,EAAE,aAAeA,EAAS,aAAe,EAC1E,MAAI,CAAY,CACd,IAAIkC,EAAQ,CAAA,EAKZ3B,EAAU,YAAc,OAAOgB,IAAkB,YAAcK,IAAkBA,GAAe,qBAAuB,QAAaK,KAAiB,EACrJ,IAAIE,GAAkBhD,GACpBiD,GAAahD,GACbiD,GAAgBhD,GAChBiD,GAAchD,GACdiD,GAAchD,GACdiD,GAAsB/C,GACtBgD,GAAoB/C,GACpBgD,GAAmB9C,GACjB+C,GAAmBnD,GAQnBoD,EAAe,KACfC,GAAuBrF,EAAS,GAAI,CAAA,EAAG,OAAO/C,EAAmB+D,EAAM,EAAG/D,EAAmBgE,EAAK,EAAGhE,EAAmBiE,EAAU,EAAGjE,EAAmBmE,EAAQ,EAAGnE,EAAmBqE,EAAI,CAAC,CAAC,EAG5LgE,EAAe,KACfC,GAAuBvF,EAAS,CAAE,EAAE,CAAE,EAAC,OAAO/C,EAAmBsE,EAAI,EAAGtE,EAAmBuE,EAAG,EAAGvE,EAAmBwE,EAAM,EAAGxE,EAAmByE,EAAG,CAAC,CAAC,EAQrJ8D,EAA0B,OAAO,KAAK,OAAO,OAAO,KAAM,CAC5D,aAAc,CACZ,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,IACR,EACD,mBAAoB,CAClB,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,IACR,EACD,+BAAgC,CAC9B,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,EACR,CACF,CAAA,CAAC,EAGEC,EAAc,KAGdC,GAAc,KAGdC,GAAkB,GAGlBC,GAAkB,GAGlBC,GAA0B,GAI1BC,GAA2B,GAK3BC,EAAqB,GAKrBC,GAAe,GAGfC,EAAiB,GAGjBC,GAAa,GAIbC,GAAa,GAMbC,EAAa,GAIbC,EAAsB,GAItBC,GAAsB,GAKtBC,GAAe,GAefC,GAAuB,GACvBC,GAA8B,gBAG9BC,GAAe,GAIfC,EAAW,GAGXC,EAAe,CAAA,EAGfC,EAAkB,KAClBC,GAA0B9G,EAAS,CAAE,EAAE,CAAC,iBAAkB,QAAS,WAAY,OAAQ,gBAAiB,OAAQ,SAAU,OAAQ,KAAM,KAAM,KAAM,KAAM,QAAS,UAAW,WAAY,WAAY,YAAa,SAAU,QAAS,MAAO,WAAY,QAAS,QAAS,QAAS,KAAK,CAAC,EAG1R+G,GAAgB,KAChBC,GAAwBhH,EAAS,CAAE,EAAE,CAAC,QAAS,QAAS,MAAO,SAAU,QAAS,OAAO,CAAC,EAG1FiH,GAAsB,KACtBC,GAA8BlH,EAAS,GAAI,CAAC,MAAO,QAAS,MAAO,KAAM,QAAS,OAAQ,UAAW,cAAe,OAAQ,UAAW,QAAS,QAAS,QAAS,OAAO,CAAC,EAC1KmH,GAAmB,qCACnBC,GAAgB,6BAChBC,EAAiB,+BAEjBC,EAAYD,EACZE,GAAiB,GAGjBC,GAAqB,KACrBC,GAA6BzH,EAAS,GAAI,CAACmH,GAAkBC,GAAeC,CAAc,EAAGnI,EAAc,EAG3GwI,EACAC,GAA+B,CAAC,wBAAyB,WAAW,EACpEC,GAA4B,YAC5BzH,EAGA0H,EAAS,KAKTC,GAActF,EAAS,cAAc,MAAM,EAC3CuF,GAAoB,SAA2BC,EAAW,CAC5D,OAAOA,aAAqB,QAAUA,aAAqB,QAC/D,EAQMC,GAAe,SAAsBC,EAAK,CACxCL,GAAUA,IAAWK,KAKrB,CAACA,GAAO9L,EAAQ8L,CAAG,IAAM,YAC3BA,EAAM,CAAA,GAIRA,EAAM1H,EAAM0H,CAAG,EACfR,EAEAC,GAA6B,QAAQO,EAAI,iBAAiB,IAAM,GAAKR,EAAoBE,GAA4BF,EAAoBQ,EAAI,kBAG7I/H,EAAoBuH,IAAsB,wBAA0BxI,GAAiBD,GAGrFmG,EAAe,iBAAkB8C,EAAMlI,EAAS,CAAA,EAAIkI,EAAI,aAAc/H,CAAiB,EAAIkF,GAC3FC,EAAe,iBAAkB4C,EAAMlI,EAAS,CAAA,EAAIkI,EAAI,aAAc/H,CAAiB,EAAIoF,GAC3FiC,GAAqB,uBAAwBU,EAAMlI,EAAS,CAAA,EAAIkI,EAAI,mBAAoBhJ,EAAc,EAAIuI,GAC1GR,GAAsB,sBAAuBiB,EAAMlI,EAASQ,EAAM0G,EAA2B,EAE7FgB,EAAI,kBAEJ/H,CACC,EACC+G,GACFH,GAAgB,sBAAuBmB,EAAMlI,EAASQ,EAAMwG,EAAqB,EAEjFkB,EAAI,kBAEJ/H,CACC,EACC6G,GACFH,EAAkB,oBAAqBqB,EAAMlI,EAAS,CAAA,EAAIkI,EAAI,gBAAiB/H,CAAiB,EAAI2G,GACpGrB,EAAc,gBAAiByC,EAAMlI,EAAS,CAAA,EAAIkI,EAAI,YAAa/H,CAAiB,EAAI,GACxFuF,GAAc,gBAAiBwC,EAAMlI,EAAS,CAAA,EAAIkI,EAAI,YAAa/H,CAAiB,EAAI,GACxFyG,EAAe,iBAAkBsB,EAAMA,EAAI,aAAe,GAC1DvC,GAAkBuC,EAAI,kBAAoB,GAC1CtC,GAAkBsC,EAAI,kBAAoB,GAC1CrC,GAA0BqC,EAAI,yBAA2B,GACzDpC,GAA2BoC,EAAI,2BAA6B,GAC5DnC,EAAqBmC,EAAI,oBAAsB,GAC/ClC,GAAekC,EAAI,eAAiB,GACpCjC,EAAiBiC,EAAI,gBAAkB,GACvC9B,EAAa8B,EAAI,YAAc,GAC/B7B,EAAsB6B,EAAI,qBAAuB,GACjD5B,GAAsB4B,EAAI,qBAAuB,GACjD/B,GAAa+B,EAAI,YAAc,GAC/B3B,GAAe2B,EAAI,eAAiB,GACpC1B,GAAuB0B,EAAI,sBAAwB,GACnDxB,GAAewB,EAAI,eAAiB,GACpCvB,EAAWuB,EAAI,UAAY,GAC3B/C,GAAmB+C,EAAI,oBAAsB/C,GAC7CmC,EAAYY,EAAI,WAAab,EAC7B7B,EAA0B0C,EAAI,yBAA2B,GACrDA,EAAI,yBAA2BH,GAAkBG,EAAI,wBAAwB,YAAY,IAC3F1C,EAAwB,aAAe0C,EAAI,wBAAwB,cAEjEA,EAAI,yBAA2BH,GAAkBG,EAAI,wBAAwB,kBAAkB,IACjG1C,EAAwB,mBAAqB0C,EAAI,wBAAwB,oBAEvEA,EAAI,yBAA2B,OAAOA,EAAI,wBAAwB,gCAAmC,YACvG1C,EAAwB,+BAAiC0C,EAAI,wBAAwB,gCAEnFnC,IACFH,GAAkB,IAEhBS,IACFD,EAAa,IAIXQ,IACFxB,EAAepF,EAAS,CAAA,EAAI/C,EAAmBqE,EAAI,CAAC,EACpDgE,EAAe,CAAA,EACXsB,EAAa,OAAS,KACxB5G,EAASoF,EAAcpE,EAAM,EAC7BhB,EAASsF,EAAc/D,EAAI,GAEzBqF,EAAa,MAAQ,KACvB5G,EAASoF,EAAcnE,EAAK,EAC5BjB,EAASsF,EAAc9D,EAAG,EAC1BxB,EAASsF,EAAc5D,EAAG,GAExBkF,EAAa,aAAe,KAC9B5G,EAASoF,EAAclE,EAAU,EACjClB,EAASsF,EAAc9D,EAAG,EAC1BxB,EAASsF,EAAc5D,EAAG,GAExBkF,EAAa,SAAW,KAC1B5G,EAASoF,EAAchE,EAAQ,EAC/BpB,EAASsF,EAAc7D,EAAM,EAC7BzB,EAASsF,EAAc5D,EAAG,IAK1BwG,EAAI,WACF9C,IAAiBC,KACnBD,EAAe5E,EAAM4E,CAAY,GAEnCpF,EAASoF,EAAc8C,EAAI,SAAU/H,CAAiB,GAEpD+H,EAAI,WACF5C,IAAiBC,KACnBD,EAAe9E,EAAM8E,CAAY,GAEnCtF,EAASsF,EAAc4C,EAAI,SAAU/H,CAAiB,GAEpD+H,EAAI,mBACNlI,EAASiH,GAAqBiB,EAAI,kBAAmB/H,CAAiB,EAEpE+H,EAAI,kBACFrB,IAAoBC,KACtBD,EAAkBrG,EAAMqG,CAAe,GAEzC7G,EAAS6G,EAAiBqB,EAAI,gBAAiB/H,CAAiB,GAI9DuG,KACFtB,EAAa,SAAW,IAItBa,GACFjG,EAASoF,EAAc,CAAC,OAAQ,OAAQ,MAAM,CAAC,EAI7CA,EAAa,QACfpF,EAASoF,EAAc,CAAC,OAAO,CAAC,EAChC,OAAOK,EAAY,OAKjBtH,GACFA,EAAO+J,CAAG,EAEZL,EAASK,EACb,EACMC,GAAiCnI,EAAS,CAAA,EAAI,CAAC,KAAM,KAAM,KAAM,KAAM,OAAO,CAAC,EAC/EoI,GAA0BpI,EAAS,CAAE,EAAE,CAAC,gBAAgB,CAAC,EAMzDqI,GAA+BrI,EAAS,CAAA,EAAI,CAAC,QAAS,QAAS,OAAQ,IAAK,QAAQ,CAAC,EAKrFsI,GAAetI,EAAS,CAAE,EAAEiB,EAAK,EACrCjB,EAASsI,GAAcpH,EAAU,EACjClB,EAASsI,GAAcnH,EAAa,EACpC,IAAIoH,GAAkBvI,EAAS,CAAE,EAAEoB,EAAQ,EAC3CpB,EAASuI,GAAiBlH,EAAgB,EAU1C,IAAImH,GAAuB,SAA8BlI,EAAS,CAChE,IAAImI,EAAS1E,GAAczD,CAAO,GAI9B,CAACmI,GAAU,CAACA,EAAO,WACrBA,EAAS,CACP,aAAcnB,EACd,QAAS,UACjB,GAEI,IAAIoB,EAAUzJ,GAAkBqB,EAAQ,OAAO,EAC3CqI,EAAgB1J,GAAkBwJ,EAAO,OAAO,EACpD,OAAKjB,GAAmBlH,EAAQ,cAG5BA,EAAQ,eAAiB8G,GAIvBqB,EAAO,eAAiBpB,EACnBqB,IAAY,MAMjBD,EAAO,eAAiBtB,GACnBuB,IAAY,QAAUC,IAAkB,kBAAoBR,GAA+BQ,IAK7F,QAAQL,GAAaI,EAAQ,EAElCpI,EAAQ,eAAiB6G,GAIvBsB,EAAO,eAAiBpB,EACnBqB,IAAY,OAKjBD,EAAO,eAAiBrB,GACnBsB,IAAY,QAAUN,GAAwBO,GAKhD,QAAQJ,GAAgBG,EAAQ,EAErCpI,EAAQ,eAAiB+G,EAIvBoB,EAAO,eAAiBrB,IAAiB,CAACgB,GAAwBO,IAGlEF,EAAO,eAAiBtB,IAAoB,CAACgB,GAA+BQ,GACvE,GAKF,CAACJ,GAAgBG,KAAaL,GAA6BK,IAAY,CAACJ,GAAaI,IAI1F,GAAAhB,IAAsB,yBAA2BF,GAAmBlH,EAAQ,eAxDvE,EAiEb,EAOMsI,EAAe,SAAsBC,EAAM,CAC7C7J,EAAU+D,EAAU,QAAS,CAC3B,QAAS8F,CACf,CAAK,EACD,GAAI,CAEFA,EAAK,WAAW,YAAYA,CAAI,CACjC,MAAC,CACA,GAAI,CACFA,EAAK,UAAY3E,EAClB,MAAC,CACA2E,EAAK,OAAM,CACZ,CACF,CACL,EAQMC,GAAmB,SAA0BC,EAAMF,EAAM,CAC3D,GAAI,CACF7J,EAAU+D,EAAU,QAAS,CAC3B,UAAW8F,EAAK,iBAAiBE,CAAI,EACrC,KAAMF,CACd,CAAO,CACF,MAAC,CACA7J,EAAU+D,EAAU,QAAS,CAC3B,UAAW,KACX,KAAM8F,CACd,CAAO,CACF,CAID,GAHAA,EAAK,gBAAgBE,CAAI,EAGrBA,IAAS,MAAQ,CAACzD,EAAayD,GACjC,GAAI3C,GAAcC,EAChB,GAAI,CACFuC,EAAaC,CAAI,CAC3B,MAAU,CAAY,KAEd,IAAI,CACFA,EAAK,aAAaE,EAAM,EAAE,CACpC,MAAU,CAAY,CAGtB,EAQMC,GAAgB,SAAuBC,EAAO,CAEhD,IAAIC,EACAC,EACJ,GAAIhD,GACF8C,EAAQ,oBAAsBA,MACzB,CAEL,IAAIG,EAAUjK,GAAY8J,EAAO,aAAa,EAC9CE,EAAoBC,GAAWA,EAAQ,EACxC,CACG1B,IAAsB,yBAA2BJ,IAAcD,IAEjE4B,EAAQ,iEAAmEA,EAAQ,kBAErF,IAAII,EAAepF,EAAqBA,EAAmB,WAAWgF,CAAK,EAAIA,EAK/E,GAAI3B,IAAcD,EAChB,GAAI,CACF6B,EAAM,IAAIxF,GAAW,EAAC,gBAAgB2F,EAAc3B,CAAiB,CAC7E,MAAQ,CAAY,CAIhB,GAAI,CAACwB,GAAO,CAACA,EAAI,gBAAiB,CAChCA,EAAM9E,GAAe,eAAekD,EAAW,WAAY,IAAI,EAC/D,GAAI,CACF4B,EAAI,gBAAgB,UAAY3B,GAAiBrD,GAAYmF,CAC9D,MAAC,CAED,CACF,CACD,IAAIC,EAAOJ,EAAI,MAAQA,EAAI,gBAM3B,OALID,GAASE,GACXG,EAAK,aAAa9G,EAAS,eAAe2G,CAAiB,EAAGG,EAAK,WAAW,IAAM,IAAI,EAItFhC,IAAcD,EACT9C,GAAqB,KAAK2E,EAAKjD,EAAiB,OAAS,MAAM,EAAE,GAEnEA,EAAiBiD,EAAI,gBAAkBI,CAClD,EAQMC,GAAkB,SAAyBvG,EAAM,CACnD,OAAOqB,GAAmB,KAAKrB,EAAK,eAAiBA,EAAMA,EAE3DM,EAAW,aAAeA,EAAW,aAAeA,EAAW,UAAYA,EAAW,4BAA8BA,EAAW,mBAAoB,KAAM,EAAK,CAClK,EAQMkG,GAAe,SAAsBC,EAAK,CAC5C,OAAOA,aAAehG,KAAoB,OAAOgG,EAAI,UAAa,UAAY,OAAOA,EAAI,aAAgB,UAAY,OAAOA,EAAI,aAAgB,YAAc,EAAEA,EAAI,sBAAsBjG,KAAiB,OAAOiG,EAAI,iBAAoB,YAAc,OAAOA,EAAI,cAAiB,YAAc,OAAOA,EAAI,cAAiB,UAAY,OAAOA,EAAI,cAAiB,YAAc,OAAOA,EAAI,eAAkB,WACrZ,EAQMC,EAAU,SAAiBjJ,EAAQ,CACrC,OAAOrE,EAAQgH,CAAI,IAAM,SAAW3C,aAAkB2C,EAAO3C,GAAUrE,EAAQqE,CAAM,IAAM,UAAY,OAAOA,EAAO,UAAa,UAAY,OAAOA,EAAO,UAAa,QAC7K,EAUMkJ,EAAe,SAAsBC,EAAYC,EAAaC,EAAM,CAClE,CAACpF,EAAMkF,IAGX/K,GAAa6F,EAAMkF,GAAa,SAAUG,EAAM,CAC9CA,EAAK,KAAKhH,EAAW8G,EAAaC,EAAMjC,CAAM,CACpD,CAAK,CACL,EAYMmC,GAAoB,SAA2BH,EAAa,CAC9D,IAAII,EAYJ,GATAN,EAAa,yBAA0BE,EAAa,IAAI,EAGpDL,GAAaK,CAAW,GAMxBtK,EAAW,kBAAmBsK,EAAY,QAAQ,EACpD,OAAAjB,EAAaiB,CAAW,EACjB,GAIT,IAAInB,EAAUvI,EAAkB0J,EAAY,QAAQ,EA2BpD,GAxBAF,EAAa,sBAAuBE,EAAa,CAC/C,QAASnB,EACT,YAAatD,CACnB,CAAK,EAGGyE,EAAY,iBAAmB,CAACH,EAAQG,EAAY,iBAAiB,IAAM,CAACH,EAAQG,EAAY,OAAO,GAAK,CAACH,EAAQG,EAAY,QAAQ,iBAAiB,IAAMtK,EAAW,UAAWsK,EAAY,SAAS,GAAKtK,EAAW,UAAWsK,EAAY,WAAW,GAM7PnB,IAAY,UAAYnJ,EAAW,aAAcsK,EAAY,SAAS,GAMtEA,EAAY,WAAa,GAMzB7D,IAAgB6D,EAAY,WAAa,GAAKtK,EAAW,UAAWsK,EAAY,IAAI,EACtF,OAAAjB,EAAaiB,CAAW,EACjB,GAIT,GAAI,CAACzE,EAAasD,IAAYjD,EAAYiD,GAAU,CAElD,GAAI,CAACjD,EAAYiD,IAAYwB,GAAwBxB,CAAO,IACtDlD,EAAwB,wBAAwB,QAAUjG,EAAWiG,EAAwB,aAAckD,CAAO,GAClHlD,EAAwB,wBAAwB,UAAYA,EAAwB,aAAakD,CAAO,GAAG,MAAO,GAIxH,GAAIhC,IAAgB,CAACG,EAAgB6B,GAAU,CAC7C,IAAIyB,EAAapG,GAAc8F,CAAW,GAAKA,EAAY,WACvDO,EAAatG,GAAc+F,CAAW,GAAKA,EAAY,WAC3D,GAAIO,GAAcD,EAEhB,QADIE,EAAaD,EAAW,OACnBxM,EAAIyM,EAAa,EAAGzM,GAAK,EAAG,EAAEA,EAAG,CACxC,IAAI0M,EAAa1G,GAAUwG,EAAWxM,GAAI,EAAI,EAC9C0M,EAAW,gBAAkBT,EAAY,gBAAkB,GAAK,EAChEM,EAAW,aAAaG,EAAYzG,GAAegG,CAAW,CAAC,CAChE,CAEJ,CACD,OAAAjB,EAAaiB,CAAW,EACjB,EACR,CASD,OANIA,aAAuBxG,GAAW,CAACmF,GAAqBqB,CAAW,IAMlEnB,IAAY,YAAcA,IAAY,WAAaA,IAAY,aAAenJ,EAAW,8BAA+BsK,EAAY,SAAS,GAChJjB,EAAaiB,CAAW,EACjB,KAIL9D,GAAsB8D,EAAY,WAAa,IAEjDI,EAAUJ,EAAY,YACtBI,EAAU7K,EAAc6K,EAAStF,GAAiB,GAAG,EACrDsF,EAAU7K,EAAc6K,EAASrF,GAAY,GAAG,EAChDqF,EAAU7K,EAAc6K,EAASpF,GAAe,GAAG,EAC/CgF,EAAY,cAAgBI,IAC9BjL,EAAU+D,EAAU,QAAS,CAC3B,QAAS8G,EAAY,UAAW,CAC1C,CAAS,EACDA,EAAY,YAAcI,IAK9BN,EAAa,wBAAyBE,EAAa,IAAI,EAChD,GACX,EAWMU,GAAoB,SAA2BC,EAAOC,EAAQC,EAAO,CAEvE,GAAInE,KAAiBkE,IAAW,MAAQA,IAAW,UAAYC,KAASlI,GAAYkI,KAAS5C,IAC3F,MAAO,GAOT,GAAI,EAAAlC,IAAmB,CAACF,GAAY+E,IAAWlL,EAAWuF,GAAa2F,CAAM,IAAU,GAAI,EAAA9E,IAAmBpG,EAAWwF,GAAa0F,CAAM,IAAU,GAAI,CAACnF,EAAamF,IAAW/E,GAAY+E,IAC7L,GAIA,EAAAP,GAAwBM,CAAK,IAAMhF,EAAwB,wBAAwB,QAAUjG,EAAWiG,EAAwB,aAAcgF,CAAK,GAAKhF,EAAwB,wBAAwB,UAAYA,EAAwB,aAAagF,CAAK,KAAOhF,EAAwB,8BAA8B,QAAUjG,EAAWiG,EAAwB,mBAAoBiF,CAAM,GAAKjF,EAAwB,8BAA8B,UAAYA,EAAwB,mBAAmBiF,CAAM,IAG1fA,IAAW,MAAQjF,EAAwB,iCAAmCA,EAAwB,wBAAwB,QAAUjG,EAAWiG,EAAwB,aAAckF,CAAK,GAAKlF,EAAwB,wBAAwB,UAAYA,EAAwB,aAAakF,CAAK,IACvS,MAAO,WAGA,CAAAzD,GAAoBwD,IAAgB,GAAI,CAAAlL,EAAW4F,GAAkB/F,EAAcsL,EAAOzF,GAAmB,EAAE,CAAC,GAAU,GAAK,GAAAwF,IAAW,OAASA,IAAW,cAAgBA,IAAW,SAAWD,IAAU,UAAYnL,GAAcqL,EAAO,OAAO,IAAM,GAAK3D,GAAcyD,KAAe,GAAI,EAAA3E,IAA2B,CAACtG,EAAWyF,GAAqB5F,EAAcsL,EAAOzF,GAAmB,EAAE,CAAC,IAAU,GAAIyF,EACha,MAAO,QAET,MAAO,EACX,EAQMR,GAA0B,SAAiCxB,EAAS,CACtE,OAAOA,IAAY,kBAAoBvJ,GAAYuJ,EAASxD,EAAgB,CAChF,EAYMyF,GAAsB,SAA6Bd,EAAa,CAClE,IAAIe,EACAF,EACAD,EACApK,EAEJsJ,EAAa,2BAA4BE,EAAa,IAAI,EAC1D,IAAIgB,EAAahB,EAAY,WAG7B,GAAI,GAACgB,GAAcrB,GAAaK,CAAW,GAG3C,KAAIiB,EAAY,CACd,SAAU,GACV,UAAW,GACX,SAAU,GACV,kBAAmBxF,CACzB,EAII,IAHAjF,EAAIwK,EAAW,OAGRxK,KAAK,CACVuK,EAAOC,EAAWxK,GAClB,IAAI0K,EAAQH,EACV7B,EAAOgC,EAAM,KACbC,GAAeD,EAAM,aAavB,GAZAL,EAAQ3B,IAAS,QAAU6B,EAAK,MAAQtL,GAAWsL,EAAK,KAAK,EAC7DH,EAAStK,EAAkB4I,CAAI,EAG/B+B,EAAU,SAAWL,EACrBK,EAAU,UAAYJ,EACtBI,EAAU,SAAW,GACrBA,EAAU,cAAgB,OAC1BnB,EAAa,wBAAyBE,EAAaiB,CAAS,EAC5DJ,EAAQI,EAAU,UAGd,CAAAA,EAAU,gBAKdhC,GAAiBC,EAAMc,CAAW,EAG9B,EAACiB,EAAU,UAKf,IAAI,CAAChF,IAA4BvG,EAAW,OAAQmL,CAAK,EAAG,CAC1D5B,GAAiBC,EAAMc,CAAW,EAClC,QACD,CAGG9D,IACF2E,EAAQtL,EAAcsL,EAAO/F,GAAiB,GAAG,EACjD+F,EAAQtL,EAAcsL,EAAO9F,GAAY,GAAG,EAC5C8F,EAAQtL,EAAcsL,EAAO7F,GAAe,GAAG,GAIjD,IAAI2F,GAAQrK,EAAkB0J,EAAY,QAAQ,EAClD,GAAI,EAACU,GAAkBC,GAAOC,EAAQC,CAAK,EAgB3C,IATIlE,KAAyBiE,IAAW,MAAQA,IAAW,UAEzD3B,GAAiBC,EAAMc,CAAW,EAGlCa,EAAQjE,GAA8BiE,GAIpC1E,IAAgBzG,EAAW,gCAAiCmL,CAAK,EAAG,CACtE5B,GAAiBC,EAAMc,CAAW,EAClC,QACD,CAGD,GAAI5F,GAAsB7H,EAAQmG,CAAY,IAAM,UAAY,OAAOA,EAAa,kBAAqB,YACnG,CAAAyI,GACF,OAAQzI,EAAa,iBAAiBiI,GAAOC,CAAM,EAAC,CAClD,IAAK,cACH,CACEC,EAAQzG,EAAmB,WAAWyG,CAAK,EAC3C,KACD,CACH,IAAK,mBACH,CACEA,EAAQzG,EAAmB,gBAAgByG,CAAK,EAChD,KACD,CACJ,CAKL,GAAI,CACEM,GACFnB,EAAY,eAAemB,GAAcjC,EAAM2B,CAAK,EAGpDb,EAAY,aAAad,EAAM2B,CAAK,EAElClB,GAAaK,CAAW,EAC1BjB,EAAaiB,CAAW,EAExB9K,GAASgE,EAAU,OAAO,CAEpC,MAAQ,CAAY,GACf,CAGD4G,EAAa,0BAA2BE,EAAa,IAAI,EAC7D,EAOMoB,GAAqB,SAASA,EAAmBC,EAAU,CAC7D,IAAIC,EACAC,EAAiB7B,GAAgB2B,CAAQ,EAI7C,IADAvB,EAAa,0BAA2BuB,EAAU,IAAI,EAC/CC,EAAaC,EAAe,YAEjCzB,EAAa,yBAA0BwB,EAAY,IAAI,EAEvDnB,GAAkBmB,CAAU,EAG5BR,GAAoBQ,CAAU,EAG1BA,EAAW,mBAAmBjI,GAChC+H,EAAmBE,EAAW,OAAO,EAKzCxB,EAAa,yBAA0BuB,EAAU,IAAI,CACzD,EAUE,OAAAnI,EAAU,SAAW,SAAUkG,EAAO,CACpC,IAAIf,EAAM,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAA,EAC1EoB,EACA+B,EACAxB,EACAyB,EACAC,EAUJ,GANAhE,GAAiB,CAAC0B,EACd1B,KACF0B,EAAQ,SAIN,OAAOA,GAAU,UAAY,CAACS,EAAQT,CAAK,EAC7C,GAAI,OAAOA,EAAM,UAAa,YAE5B,GADAA,EAAQA,EAAM,WACV,OAAOA,GAAU,SACnB,MAAMzJ,GAAgB,iCAAiC,MAGzD,OAAMA,GAAgB,4BAA4B,EAKtD,GAAI,CAACuD,EAAU,YAAa,CAC1B,GAAI3G,EAAQ0G,EAAO,YAAY,IAAM,UAAY,OAAOA,EAAO,cAAiB,WAAY,CAC1F,GAAI,OAAOmG,GAAU,SACnB,OAAOnG,EAAO,aAAamG,CAAK,EAElC,GAAIS,EAAQT,CAAK,EACf,OAAOnG,EAAO,aAAamG,EAAM,SAAS,CAE7C,CACD,OAAOA,CACR,CAcD,GAXK/C,IACH+B,GAAaC,CAAG,EAIlBnF,EAAU,QAAU,GAGhB,OAAOkG,GAAU,WACnBtC,EAAW,IAETA,GAEF,GAAIsC,EAAM,SAAU,CAClB,IAAIP,EAAUvI,EAAkB8I,EAAM,QAAQ,EAC9C,GAAI,CAAC7D,EAAasD,IAAYjD,EAAYiD,GACxC,MAAMlJ,GAAgB,yDAAyD,CAElF,UACQyJ,aAAiB7F,EAG1BkG,EAAON,GAAc,SAAS,EAC9BqC,EAAe/B,EAAK,cAAc,WAAWL,EAAO,EAAI,EACpDoC,EAAa,WAAa,GAAKA,EAAa,WAAa,QAGlDA,EAAa,WAAa,OADnC/B,EAAO+B,EAKP/B,EAAK,YAAY+B,CAAY,MAE1B,CAEL,GAAI,CAACjF,GAAc,CAACL,GAAsB,CAACE,GAE3CgD,EAAM,QAAQ,GAAG,IAAM,GACrB,OAAOhF,GAAsBqC,GAAsBrC,EAAmB,WAAWgF,CAAK,EAAIA,EAO5F,GAHAK,EAAON,GAAcC,CAAK,EAGtB,CAACK,EACH,OAAOlD,EAAa,KAAOE,GAAsBpC,GAAY,EAEhE,CAGGoF,GAAQnD,IACVyC,EAAaU,EAAK,UAAU,EAO9B,QAHIkC,EAAejC,GAAgB5C,EAAWsC,EAAQK,CAAI,EAGnDO,EAAc2B,EAAa,YAE5B3B,EAAY,WAAa,GAAKA,IAAgByB,IAKlDtB,GAAkBH,CAAW,EAG7Bc,GAAoBd,CAAW,EAG3BA,EAAY,mBAAmB3G,GACjC+H,GAAmBpB,EAAY,OAAO,EAExCyB,EAAUzB,GAKZ,GAHAyB,EAAU,KAGN3E,EACF,OAAOsC,EAIT,GAAI7C,EAAY,CACd,GAAIC,EAEF,IADAkF,EAAajH,GAAuB,KAAKgF,EAAK,aAAa,EACpDA,EAAK,YAEViC,EAAW,YAAYjC,EAAK,UAAU,OAGxCiC,EAAajC,EAEf,OAAIhE,EAAa,YAAcA,EAAa,iBAQ1CiG,EAAa/G,GAAW,KAAKvB,EAAkBsI,EAAY,EAAI,GAE1DA,CACR,CACD,IAAIE,EAAiBxF,EAAiBqD,EAAK,UAAYA,EAAK,UAG5D,OAAIrD,GAAkBb,EAAa,aAAekE,EAAK,eAAiBA,EAAK,cAAc,SAAWA,EAAK,cAAc,QAAQ,MAAQ/J,EAAW4C,GAAcmH,EAAK,cAAc,QAAQ,IAAI,IAC/LmC,EAAiB,aAAenC,EAAK,cAAc,QAAQ,KAAO;AAAA,EAAQmC,GAIxE1F,IACF0F,EAAiBrM,EAAcqM,EAAgB9G,GAAiB,GAAG,EACnE8G,EAAiBrM,EAAcqM,EAAgB7G,GAAY,GAAG,EAC9D6G,EAAiBrM,EAAcqM,EAAgB5G,GAAe,GAAG,GAE5DZ,GAAsBqC,GAAsBrC,EAAmB,WAAWwH,CAAc,EAAIA,CACvG,EAQE1I,EAAU,UAAY,SAAUmF,EAAK,CACnCD,GAAaC,CAAG,EAChBhC,GAAa,EACjB,EAOEnD,EAAU,YAAc,UAAY,CAClC8E,EAAS,KACT3B,GAAa,EACjB,EAYEnD,EAAU,iBAAmB,SAAU2I,EAAKd,EAAMF,EAAO,CAElD7C,GACHI,GAAa,CAAE,CAAA,EAEjB,IAAIuC,EAAQrK,EAAkBuL,CAAG,EAC7BjB,EAAStK,EAAkByK,CAAI,EACnC,OAAOL,GAAkBC,EAAOC,EAAQC,CAAK,CACjD,EASE3H,EAAU,QAAU,SAAU6G,EAAY+B,EAAc,CAClD,OAAOA,GAAiB,aAG5BjH,EAAMkF,GAAclF,EAAMkF,IAAe,CAAA,EACzC5K,EAAU0F,EAAMkF,GAAa+B,CAAY,EAC7C,EAUE5I,EAAU,WAAa,SAAU6G,EAAY,CAC3C,GAAIlF,EAAMkF,GACR,OAAO7K,GAAS2F,EAAMkF,EAAW,CAEvC,EAQE7G,EAAU,YAAc,SAAU6G,EAAY,CACxClF,EAAMkF,KACRlF,EAAMkF,GAAc,GAE1B,EAOE7G,EAAU,eAAiB,UAAY,CACrC2B,EAAQ,CAAA,CACZ,EACS3B,CACT,CACG,IAAC6I,GAAS/I,GAAe"}