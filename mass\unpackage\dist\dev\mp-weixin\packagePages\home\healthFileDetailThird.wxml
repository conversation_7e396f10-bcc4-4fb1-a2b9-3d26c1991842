<view class="data-v-960aaf7e"><zlnavbar vue-id="2b4a3e36-1" isBack="{{true}}" class="data-v-960aaf7e" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" class="data-v-960aaf7e">{{serviceSData.name}}</view></zlnavbar><scroll-menu vue-id="2b4a3e36-2" menuData="{{scrollMenu}}" data-event-opts="{{[['^menuIndex',[['handleTabMenuChange']]]]}}" bind:menuIndex="__e" class="data-v-960aaf7e" bind:__l="__l"></scroll-menu><view class="main-box data-v-960aaf7e"><view class="list-box data-v-960aaf7e"><block wx:if="{{serviceDataDetail.arrData}}"><view class="list-item-box data-v-960aaf7e"><block wx:for="{{serviceDataDetail.arrData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item data-v-960aaf7e"><block wx:if="{{item.value}}"><view class="list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{item.label}}</view><view class="text-right data-v-960aaf7e">{{item.value}}</view></view></block><block wx:else><block wx:if="{{item.label==''}}"><block wx:for="{{collapseData[index].data}}" wx:for-item="collapseItem" wx:for-index="collapseIndex" wx:key="collapseIndex"><view class="no-collapse-content-box data-v-960aaf7e"><block wx:for="{{collapseItem}}" wx:for-item="sitem" wx:for-index="index" wx:key="value"><block class="data-v-960aaf7e"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({sitem})}}" class="{{['data-v-960aaf7e','list-item',sitem.type=='detail'&&'list-item2']}}" bindtap="__e"><view class="{{['data-v-960aaf7e','list-cell',sitem.type!='detail'&&'sub-list-cell']}}"><view class="list-lable data-v-960aaf7e">{{"==="+sitem.label}}</view><view class="{{['data-v-960aaf7e','text-right',sitem.type=='detail'&&'text-right-btn']}}">{{''+sitem.value+''}}</view></view></view><block wx:if="{{sitem.type=='desc'}}"><view class="list-item collapse-content-desc data-v-960aaf7e"><view class="list-cell sub-list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{sitem.label}}</view></view><view class="text-left data-v-960aaf7e">{{sitem.value}}</view></view></block></block></block></view></block></block><block wx:else><block class="data-v-960aaf7e"><view class="collapse-item data-v-960aaf7e"><view data-event-opts="{{[['tap',[['handleChangePanel',['$0'],[[['serviceDataDetail.arrData','',index]]]]]]]}}" class="list-cell data-v-960aaf7e" catchtap="__e"><view class="list-lable data-v-960aaf7e">{{item.label}}</view><view class="{{['iconfont','icon-arrow-right','arrow-icon','_i','data-v-960aaf7e',(item.isOpen)?'collapse-item-arrow-active':'',(item.isOpen)?'collapse-item--animation':'']}}"></view></view><view hidden="{{!(item.isOpen)}}" class="data-v-960aaf7e"><block wx:for="{{collapseData[index].data}}" wx:for-item="collapseItem" wx:for-index="collapseIndex" wx:key="collapseIndex"><view class="{{['collapse-content','data-v-960aaf7e',(item.isOpen)?'is--transition':'']}}"><block wx:for="{{collapseItem}}" wx:for-item="collapsesItem" wx:for-index="collapsesIndex" wx:key="collapsesIndex"><view class="list-item data-v-960aaf7e"><block wx:if="{{false}}"><view class="list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{collapsesItem.label}}</view><view class="text-right data-v-960aaf7e">{{collapsesItem.value}}</view></view></block><block wx:if="{{collapsesItem.type!='desc'}}"><view class="list-cell sub-list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{collapsesItem.label}}</view><view class="text-right data-v-960aaf7e">{{collapsesItem.value||''}}</view></view></block><block wx:else><view class="collapse-content-desc data-v-960aaf7e"><view class="list-cell sub-list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{collapsesItem.label}}</view></view><view class="text-left data-v-960aaf7e">{{collapsesItem.value}}</view></view></block></view></block></view></block></view></view></block></block></block></view></block></view></block><block wx:if="{{collapseData}}"><view class="list-item-box data-v-960aaf7e"><scroll-view class="tabs data-v-960aaf7e" scroll-x="true" scroll-with-animation="{{true}}" scroll-left="{{scrollLeft}}"><block wx:if="{{$root.g0>0}}"><view class="tabs-scroll data-v-960aaf7e"><block wx:for="{{serviceSDataTabList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleMenuChange',[index,'$0'],[[['serviceSDataTabList','',index]]]]]]]}}" class="{{['tabs-scroll_item','data-v-960aaf7e',(tabCur==index)?'tab-active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view></block></scroll-view><view class="list-content-item data-v-960aaf7e"><block wx:for="{{collapseData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item data-v-960aaf7e"><block wx:if="{{item.value&&item.type!='desc'}}"><view class="list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{item.label}}</view><view class="text-right data-v-960aaf7e">{{item.value}}</view></view></block><block wx:else><block wx:if="{{item.type=='desc'}}"><view class="data-v-960aaf7e"><view class="list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{item.label}}</view></view><view class="text-left data-v-960aaf7e">{{item.value}}</view></view></block><block wx:else><block wx:if="{{item.label==''}}"><block wx:for="{{collapseData[index].data}}" wx:for-item="collapseItem" wx:for-index="collapseIndex" wx:key="collapseIndex"><view class="no-collapse-content-box data-v-960aaf7e"><block wx:for="{{collapseItem}}" wx:for-item="sitem" wx:for-index="index" wx:key="value"><block class="data-v-960aaf7e"><block wx:if="{{sitem.type!='desc'}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({sitem})}}" class="{{['data-v-960aaf7e','list-item',sitem.type=='detail'&&'list-item2']}}" bindtap="__e"><view class="{{['data-v-960aaf7e','list-cell',sitem.type!='detail'&&'sub-list-cell']}}"><view class="list-lable data-v-960aaf7e">{{sitem.label}}</view><view class="{{['data-v-960aaf7e','text-right',sitem.type=='detail'&&'text-right-btn']}}">{{''+sitem.value+''}}</view></view></view></block><block wx:else><view class="list-item collapse-content-desc data-v-960aaf7e"><view class="list-cell sub-list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{sitem.label}}</view></view><view class="text-left data-v-960aaf7e">{{sitem.value}}</view></view></block></block></block></view></block></block><block wx:else><block class="data-v-960aaf7e"><view class="collapse-item data-v-960aaf7e"><view data-event-opts="{{[['tap',[['handleChangePanel',['$0'],[[['collapseData','',index]]]]]]]}}" class="list-cell data-v-960aaf7e" catchtap="__e"><view class="list-lable data-v-960aaf7e">{{item.label}}</view><view class="{{['iconfont','icon-arrow-right','arrow-icon','_i','data-v-960aaf7e',(item.isOpen)?'collapse-item-arrow-active':'',(item.isOpen)?'collapse-item--animation':'']}}"></view></view><view hidden="{{!(item.isOpen)}}" class="data-v-960aaf7e"><block wx:for="{{collapseData[index].data}}" wx:for-item="collapseItem" wx:for-index="collapseIndex" wx:key="collapseIndex"><view class="{{['collapse-content','data-v-960aaf7e',(item.isOpen)?'is--transition':'']}}"><block wx:for="{{collapseItem}}" wx:for-item="collapsesItem" wx:for-index="collapsesIndex" wx:key="collapsesIndex"><view class="list-item data-v-960aaf7e"><block wx:if="{{false}}"><view class="list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{collapsesItem.label}}</view><view class="text-right data-v-960aaf7e">{{collapsesItem.value}}</view></view></block><block wx:if="{{collapsesItem.type!='desc'}}"><view class="list-cell sub-list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{collapsesItem.label}}</view><view class="text-right data-v-960aaf7e">{{collapsesItem.value||''}}</view></view></block><block wx:else><view class="collapse-content-desc data-v-960aaf7e"><view class="list-cell sub-list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{collapsesItem.label}}</view></view><view class="text-left data-v-960aaf7e">{{collapsesItem.value}}</view></view></block></view></block></view></block></view></view></block></block></block></block></view></block></view></view></block><block wx:else><block wx:for="{{serviceDataDetail.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item-box data-v-960aaf7e"><view class="list-content-item data-v-960aaf7e"><block wx:for="{{item}}" wx:for-item="sitem" wx:for-index="index" wx:key="value"><block class="data-v-960aaf7e"><block wx:if="{{sitem.type!='desc'}}"><view class="{{['data-v-960aaf7e','list-item',sitem.type=='detail'&&'list-item2']}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({sitem})}}" class="list-cell data-v-960aaf7e" bindtap="__e"><view class="list-lable data-v-960aaf7e">{{sitem.label}}</view><view class="{{['data-v-960aaf7e','text-right',sitem.type=='detail'&&'text-right-btn']}}">{{''+sitem.value+''}}</view></view></view></block><block wx:else><view class="list-item data-v-960aaf7e"><view class="list-cell data-v-960aaf7e"><view class="list-lable data-v-960aaf7e">{{sitem.label}}</view></view><view class="text-left data-v-960aaf7e">{{sitem.value}}</view></view></block></block></block></view></view></block></block></view><block wx:if="{{false}}"><button class="main-btn bottom-btn data-v-960aaf7e">异常数据反馈</button></block></view></view>