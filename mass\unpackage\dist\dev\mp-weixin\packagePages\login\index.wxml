<view class="login-wrapper"><zlnavbar vue-id="86ac4646-1" isBack="{{false}}" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content">验证码登录</view></zlnavbar><view class="login-form"><image class="logo" src="../../static/images/logo.png"></image><view class="form-item"><image class="form-icon" src="../../static/images/login/phone-icon.png"></image><input type="text" placeholder="请输入手机号码" placeholder-style="color:#ccc" data-event-opts="{{[['input',[['setInputValue',['$event','phone']]]]]}}" value="{{loginForm.phone}}" bindinput="__e"/></view><view class="form-item"><image class="form-icon" src="../../static/images/login/msg-icon.png"></image><input type="text" placeholder="请输入短信验证码" placeholder-style="color:#ccc" maxlength="20" data-event-opts="{{[['input',[['setInputValue',['$event','messageCode']]]]]}}" value="{{loginForm.messageCode}}" bindinput="__e"/><button class="verify-btn" disabled="{{disabled}}" data-event-opts="{{[['tap',[['getCode',['$event']]]]]}}" bindtap="__e">{{''+btnText}}</button></view><view class="bottom-btn-box"><view data-event-opts="{{[['tap',[['handleLogin',['$event']]]]]}}" class="main-btn" bindtap="__e">登录</view></view><view data-event-opts="{{[['tap',[['goPath',['msgLogin']]]]]}}" class="pwd-login-btn" bindtap="__e">密码登录</view></view><view class="bottom-info"><checkbox class="checked-btn" color="#1C6CED" checked="{{checkState}}" data-event-opts="{{[['tap',[['handleCheckboxChange',['$0'],['checkState']]]]]}}" bindtap="__e"></checkbox><view>登录即代表同意<label data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="_span">用户服务协议</label>和<label data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e" class="_span">个人信息保护政策</label>，首次登录用户请使用短信验证码方式登录，登录成功后将自动为您创建账号。</view></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="86ac4646-2" modalData="{{modalData}}" btnVisible="{{true}}" data-event-opts="{{[['^cancel',[['handleCancel']]],['^confirm',[['handleConfirm']]]]}}" bind:cancel="__e" bind:confirm="__e" bind:__l="__l"></modal-dialog><footer style="width:100%;" vue-id="86ac4646-3" bind:__l="__l"></footer></view>