<view class="login-wrapper"><zlnavbar vue-id="75c29562-1" isBack="{{true}}" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content">人脸识别</view></zlnavbar><view class="login-form"><image class="form-icon" mode="widthFix" src="../../static/images/login/register-success.png"></image><view class="center-box"><view class="register-info"><view>为保障您的个人隐私及数据安全，确保所有操作为本人操作，请先进行人脸核身，验证通过后方可查看健康档案信息。<block wx:if="{{type=='2'}}"><label class="_span">若姓名或证件号码有误，请联系档案管理机构进行修改。</label></block></view></view><view class="form-wrap"><view class="form-item"><text>姓名:</text><input type="text" disabled="{{disabled}}" placeholder="请输入姓名" placeholder-style="color:#ccc" maxlength="50" data-event-opts="{{[['input',[['__set_model',['','name','$event',[]]],['setInputValue',['$event','name']]]]]}}" value="{{name}}" bindinput="__e"/></view><view class="form-item"><text>身份证号:</text><input type="text" disabled="{{IDdisabled}}" placeholder="请输入身份证号" placeholder-style="color:#ccc" maxlength="18" data-event-opts="{{[['input',[['__set_model',['','idCardNumber','$event',[]]],['setInputValue',['$event','idCardNumber']]]]]}}" value="{{idCardNumber}}" bindinput="__e"/></view><block wx:if="{{familyItem.resumeRecognize==1}}"><view data-event-opts="{{[['tap',[['resetInputVal',['$event']]]]]}}" class="tips" bindtap="__e">如信息有误，点此重置。</view></block></view></view><view class="bottom-btn-box"><block wx:if="{{ageRange}}"><view data-event-opts="{{[['tap',[['faceAuthentication',['$event']]]]]}}" class="main-btn" bindtap="__e">人脸识别认证</view></block><block wx:else><view data-event-opts="{{[['tap',[['faceAuthenticationChild',['$event']]]]]}}" class="main-btn" bindtap="__e">提交</view></block></view></view><modal-dialog data-custom-hidden="{{!(modalVisible)}}" vue-id="75c29562-2" closeVisible="{{true}}" modalData="{{modalData}}" list="{{phoneList}}" data-event-opts="{{[['^confirm',[['handleConfirm']]],['^cancel',[['handleCancel']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:__l="__l"></modal-dialog></view>