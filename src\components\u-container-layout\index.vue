<template>
  <div class="m-container-layout">
    <div class="m-container-layout-inner">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped>
  .m-container-layout {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 10px 12px;
    box-sizing: border-box;
    .m-container-layout-inner {
      flex: 1;
      display: flex;
      flex-direction: column;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      background: white;
      padding: 20px;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
    }
  }
</style>
