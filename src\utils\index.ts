
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xdc00 && code <= 0xdfff) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    }),
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

export function getColor() {
  var str = '#'
  var arr = ['1', '2', '3', '4', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f']
  for (var i = 0; i < 6; i++) {
    var num = parseInt(Math.random() * 16)
    str += arr[num]
  }
  return str
}
// 检查给定的值是否是数组
export const isArray = function (value) {
  return objToString.call(value) === '[object Array]'
}

let funProto = Function.prototype
let objProto = Object.prototype

let getPrototypeOf = Object.getPrototypeOf

let objToString = objProto.toString
let hasOwnProperty = objProto.hasOwnProperty
let funToString = funProto.toString
// 检查给定的值是否是字符串
export const isString = function (value) {
  return objToString.call(value) === '[object String]'
}
// 检查给定的值是否是纯对象，纯对象是指通过 {} 或 new Object() 声明的对象
export const isPlainObject = function (value) {
  if (!value || objToString.call(value) !== '[object Object]') {
    return false
  }

  let prototype = getPrototypeOf(value)

  if (prototype === null) {
    return true
  }

  let constructor = hasOwnProperty.call(prototype, 'constructor') && prototype.constructor

  return (
    typeof constructor === 'function' && funToString.call(constructor) === funToString.call(Object)
  )
}

// // 深度克隆 array 数组或 json 对象，返回克隆后的副本
export const deepObjClone = function (obj) {
  let weakMap = new WeakMap()
  function clone(obj) {
    if (obj == null) {
      return obj
    }
    if (obj instanceof Date) {
      return new Date(obj)
    }
    if (obj instanceof RegExp) {
      return new RegExp(obj)
    }
    if (typeof obj !== 'object') return obj

    if (weakMap.get(obj)) {
      return weakMap.get(obj)
    }
    let copy = new obj.constructor()
    weakMap.set(obj, copy)
    for (let key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        let value = obj[key]
        copy[key] = clone(value)
      }
    }
    return copy
  }
  return clone(obj)
}


export function getDatePicker(v, n = 'date', split = '--') {
  if (v === null || v === '' || v === undefined) {
    return split;
  } else {
    //let value = Number(v)
    let value = new Date(v).getTime()
    let now = new Date();
    let nowYear = now.getFullYear();
    let nowMonth = now.getMonth() + 1;
    nowMonth = nowMonth < 10 ? ('0' + nowMonth) : nowMonth;
    let nowDate = now.getDate();
    nowDate = nowDate < 10 ? ('0' + nowDate) : nowDate;

    let date = new Date(value);
    let y = date.getFullYear();// 年
    let MM = date.getMonth() + 1;// 月
    MM = MM < 10 ? ('0' + MM) : MM;
    let d = date.getDate();// 日
    d = d < 10 ? ('0' + d) : d;
    let h = date.getHours();// 时
    h = h < 10 ? ('0' + h) : h;
    let m = date.getMinutes();// 分
    m = m < 10 ? ('0' + m) : m;
    let s = date.getSeconds();// 秒
    s = s < 10 ? ('0' + s) : s;
    if (n == "date") {
      var timer = y + '-' + MM + '-' + d;
    } else if (n == "monthDate") {
      var timer = MM + '-' + d;
    } else if (n == "chinese") {
      var timer = y + '年' + MM + '月' + d + '日';
    } else if (n == "chineseDateTime") {
      var timer = y + '年' + MM + '月' + d + '日' + ' ' + h + ':' + m + ':' + s;
    } else if (n == "hourDate") {
      var timer = y + '年' + MM + '月' + d + '日' + h + '时';
    } else if (n == "time") {
      var timer = h + ':' + m + ':' + s;
    } else if (n == "specialDate") {
      if (nowYear === y && nowMonth === MM && nowDate === d) {
        var timer = "今天" + h + ':' + m;
      } else {
        var timer = y + '-' + MM + '-' + d;
      }
    } else if (n == "dateline") {
      var timer = y + '' + MM + '' + d;
    } else if (n == "hour") {
      var timer = h + ':' + m;
    } else if (n == "day") {
      var timer = MM + '.' + d;
    } else if (n == "dateTime") {
      var timer = y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
    } else if (n == "minuteTime") {
      var timer = y + '-' + MM + '-' + d + ' ' + h + ':' + m;
    }
    return timer
  }
}

/**
 * 获取一个距离当前/某个时间的一个时间  或者时间戳转换时间
 * @param {*} value 接受一个需要转换的时间
 * @param {*} type 类型包括 zeroPoint 零点  endTime 24点  以及getDatePicker接受的所有时间转换类型
 * @param {*} day 距离当前时间几天的一个时间，接受负值，意思是未来某一天
 */
export function getDateValue(value, type = 'date', day = 0) {
  let time = ""
  let date = ""
  if (value == null || value == '' || value == undefined) {
    date = new Date(new Date().getTime() - (24 * 60 * 60 * 1000 * day))
  } else {
    date = new Date(new Date(value).getTime() - (24 * 60 * 60 * 1000 * day))
  }
  let Y = date.getFullYear();// 年
  let M = date.getMonth() + 1;// 月
  M = M < 10 ? ('0' + M) : M;
  let D = date.getDate();// 日
  D = D < 10 ? ('0' + D) : D;
  let h = date.getHours();// 时
  h = h < 10 ? ('0' + h) : h;
  let m = date.getMinutes();// 分
  m = m < 10 ? ('0' + m) : m;
  let s = date.getSeconds();// 秒
  s = s < 10 ? ('0' + s) : s;
  if (type == 'zeroPoint') {
    time = Y + '-' + M + '-' + D + ' 00:00:00'
  } else if (type == 'endTime') {
    time = Y + '-' + M + '-' + D + ' 23:59:59'
  } else {
    time = getDatePicker(value, type)
  }
  return time
}


// 获取用户信息
export function getUserInfo(v) {
  let userInfo = sessionStorage.getItem("YL_pc_userInfo") ? JSON.parse(sessionStorage.getItem("YL_pc_userInfo")) : ''
  return userInfo[v]
}

// 出生日期换算周岁年龄
export function birthDateToYear(birthDate, returnNum) {
  birthDate = birthDate ?? ''
  let now = new Date().getYear()
  let birthYear = new Date(birthDate).getYear()
  return returnNum ? (now - birthYear) : (now - birthYear) + '岁'
}


// 生成uuid
export function uuid() {
  var s = [];
  var hexDigits = "0123456789abcdef";
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4";
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
  s[8] = s[13] = s[18] = s[23] = "-";
  return s.join("");
}

// 数字格式，每三位加逗号
export function numberFormat(value) {
  if (!value) return 0
  const reg = /(\d)(?=(?:\d{3})+$)/g;
  return value.toString().replace(reg, '$1,');
}

export function getTimeStateStr() {
  let timeNow = new Date();
  let hours = timeNow.getHours();
  if (hours >= 6 && hours <= 10) return `早上好`;
  if (hours >= 10 && hours <= 14) return `中午好`;
  if (hours >= 14 && hours <= 18) return `下午好`;
  if (hours >= 18 && hours <= 24) return `晚上好`;
  if (hours >= 0 && hours <= 6) return `凌晨好`;
}


/** *
 *
 *  将列表型的数据转化成树形数据 => 递归算法 => 自身调用自身 => 一定条件不能一样， 否则就会死循环
 *  遍历树形 有一个重点 要先找一个头儿
 * ***/
export function tranListToTreeData(list, rootValue) {
  //定义个空数组接收参数
  var arr = []
  list.forEach(item => {
    if (item.pid === rootValue) {
      // 找到之后 就要去找 item 下面有没有子节点
      //条件不能是(list, rootValue),否则死递归
      const children = tranListToTreeData(list, item.id)
      if (children.length) {
        // 如果children的长度大于0 说明找到了子节点
        item.children = children
      }
      arr.push(item) // 把找到的数据内容加入到数组中
    }
  })
  //返回这个数组
  return arr
}


// 身份证号、手机号、姓名 脱敏方法
export function cardNoFilter(value) {
  if (value == null || value == '' || value == undefined) {
    return '';
  } else {
    let x = ''
    for (let i = 0; i < (value.length - 3); i++) {
      x = x + '*'
    }
    return value.substr(0, 1) + x + value.substr(16, 18)
  }
}

export function phoneFilter(value) {
  if (value == null || value == '' || value == undefined) {
    return '';
  } else {
    return value.substr(0, 3) + "****" + value.substr(7, 12)
  }
}

export function nameFilter(value) {
  if (value == null || value == '' || value == undefined) {
    return '';
  } else {
    if (value.length == 2) {
      return "*" + value.substr(value.length - 1)
    } else if (value.length > 2) {
      let x = ''
      for (let i = 0; i < (value.length - 2); i++) {
        x = x + '*'
      }
      return value.substr(0, 1) + x + value.substr(value.length - 1)
    } else {
      return value
    }
  }
}

export function toChinesNum(num) {
  if (num == '' || num == null || num == undefined) return ''

  let changeNum = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  let unit = ["", "拾", "佰", "仟", "万"];
  num = parseInt(num);
  let getWan = (temp) => {
    let strArr = temp.toString().split("").reverse();
    let newNum = "";
    for (var i = 0; i < strArr.length; i++) {
      newNum = (i == 0 && strArr[i] == 0 ? "" : (i > 0 && strArr[i] == 0 && strArr[i - 1] == 0 ? "" : changeNum[strArr[i]] + (strArr[i] == 0 ? unit[0] : unit[i]))) + newNum;
    }
    return newNum;
  }
  let overWan = Math.floor(num / 10000);
  let noWan = num % 10000;
  if (noWan.toString().length < 4) noWan = "0" + noWan;
  return overWan ? getWan(overWan) + "万" + getWan(noWan) : getWan(num);
}


export function isWeixin() {
  var ua = navigator.userAgent.toLowerCase();
  if (ua.match(/MicroMessenger/i) == "micromessenger") {
    return true;
  } else {
    return false;
  }
}

// 下载 接收四个参数 response：返回的文件流 fileName：文件名 fileType/format：文件格式，传其中一个就行
export function fileDownload(response, fileName, format, fileType) {
  let typeEnums = {
    'xls': 'application/vnd.ms-excel application/x-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'json': 'application/json',
    'csv': 'text/csv',
    'zip': 'application/x-msdownload',
  },
    targetFileType = (fileType || typeEnums[format]) || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
    blob = new Blob([response], {
      'type': `${targetFileType}`
    }),
    downloadElement = document.createElement('a'),
    href = window.URL.createObjectURL(blob); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = fileName; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
  window.URL.revokeObjectURL(href); //释放掉blob对象
}

// 获取配置项信息
export function getSysConfigvalue(code) {
  let tmpArr = JSON.parse(localStorage.getItem("YL_pc_sysConfigure")) || []
  tmpArr = tmpArr.filter((item => {
    return item.code == code
  }))
  if (tmpArr.length > 0) {
    return tmpArr[0].value
  }
  return ''
}

export function importImageDynamic(url: string) {
  return new URL(`../assets/image/${url}`, import.meta.url).href;
}
